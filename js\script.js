// ===== FONT AWESOME FALLBACK CHECK =====
function checkFontAwesome() {
    // Wait for fonts to load
    setTimeout(() => {
        const testElement = document.createElement('i');
        testElement.className = 'fas fa-home';
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        testElement.style.fontSize = '16px';
        document.body.appendChild(testElement);

        const computedStyle = window.getComputedStyle(testElement, ':before');
        const fontFamily = computedStyle.getPropertyValue('font-family');
        const content = computedStyle.getPropertyValue('content');

        document.body.removeChild(testElement);

        // Check if Font Awesome is loaded properly
        const isFontAwesomeLoaded = fontFamily.toLowerCase().includes('font awesome') ||
                                   content !== 'none' && content !== '""';

        if (!isFontAwesomeLoaded) {
            document.body.classList.add('fa-fallback');
            console.warn('Font Awesome not loaded properly, using fallback icons');

            // Force reload Font Awesome
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
            document.head.appendChild(link);
        } else {
            console.log('Font Awesome loaded successfully');
        }
    }, 500);
}

// ===== GLOBAL VARIABLES =====
let notes = [];
let currentNoteId = null;
let joditEditor = null;
let notifications = [];
let notificationSound = null;
let notificationVolume = 1.0; // Full volume by default
let autoSaveTimer = null;
let autoSaveEnabled = true;
let autoSaveInterval = 0; // Real-time save (0 seconds)
let lastSavedContent = '';
let hasUnsavedChanges = false;

let currentCalendarDate = new Date();
let reminderTimers = new Map();
let dailyReminderCheckInterval = null; // Daily reminder check timer
let globalAudioContext = null; // Global AudioContext for sound
let audioContextInitialized = false;
let currentStoragePath = 'F:\\Desktop\\WEB WORLD\\01_website\\fahim_web_site_directory_2.0\\notes'; // Default storage path

let currentTheme = 'dark';
let currentColorTheme = 'blue';
let currentFontSize = 18;
let currentFontFamily = 'Courier New';
let currentFontWeight = 400;
let attachedFiles = new Map(); // Map to store attached files
let currentFilePreview = null;
let sortableInstance = null; // For drag and drop functionality
let isDragMode = false; // Track if drag mode is enabled
let originalNotesOrder = []; // Store original order for reset

// Vertical Tabs Variables
let editorTabs = [];
let currentActiveTabId = null;
let tabCounter = 0;

// Password Protection Variables
let currentPasswordAttempts = 0;
let maxPasswordAttempts = 3;
let lockedNoteId = null;

// Context Menu Variables
let contextMenu = null;
let selectedText = '';
let selectionRange = null;
let contextMenuTimeout = null;
let isContextMenuVisible = false;
let lastSelectionText = '';

// ===== LOADER FUNCTIONALITY =====
let loaderProgress = 0;
let loaderInterval = null;

function showLoader() {
    const loader = document.getElementById('fullscreenLoader');
    const progressBar = document.getElementById('progressBar');

    if (loader) {
        loader.classList.remove('hidden');
        loaderProgress = 0;

        // Animate progress bar
        loaderInterval = setInterval(() => {
            loaderProgress += Math.random() * 15 + 5; // Random increment between 5-20

            if (loaderProgress >= 100) {
                loaderProgress = 100;
                clearInterval(loaderInterval);

                // Hide loader after a short delay
                setTimeout(() => {
                    hideLoader();
                }, 500);
            }

            if (progressBar) {
                progressBar.style.width = loaderProgress + '%';
            }
        }, 100);
    }
}

function hideLoader() {
    const loader = document.getElementById('fullscreenLoader');

    if (loader) {
        loader.classList.add('hidden');

        // Remove loader from DOM after animation completes
        setTimeout(() => {
            if (loader.parentNode) {
                loader.parentNode.removeChild(loader);
            }
        }, 500);
    }

    if (loaderInterval) {
        clearInterval(loaderInterval);
        loaderInterval = null;
    }
}

function simulateLoading() {
    // Simulate different loading stages
    const stages = [
        { progress: 20, message: 'ডেটা লোড হচ্ছে...' },
        { progress: 40, message: 'এডিটর প্রস্তুত করা হচ্ছে...' },
        { progress: 60, message: 'থিম লোড হচ্ছে...' },
        { progress: 80, message: 'নোটিফিকেশন সেটআপ...' },
        { progress: 100, message: 'সম্পূর্ণ!' }
    ];

    let currentStage = 0;
    const progressBar = document.getElementById('progressBar');
    const loaderText = document.querySelector('.loader-text p');

    function updateStage() {
        if (currentStage < stages.length) {
            const stage = stages[currentStage];

            if (progressBar) {
                progressBar.style.width = stage.progress + '%';
            }

            if (loaderText) {
                loaderText.textContent = stage.message;
            }

            currentStage++;

            // Random delay between stages
            const delay = Math.random() * 800 + 400; // 400-1200ms
            setTimeout(updateStage, delay);
        } else {
            // All stages complete, hide loader
            setTimeout(() => {
                hideLoader();
            }, 300);
        }
    }

    // Start the loading simulation
    setTimeout(updateStage, 500);
}

// ===== DEMO DATA =====
const demoNotes = [
    {
        id: 'demo1',
        title: '📚 পড়াশোনার পরিকল্পনা',
        content: '<h2>📚 আমার পড়াশোনার পরিকল্পনা</h2><p>আজকের করণীয়:</p><ul><li>গণিত - অধ্যায় ৫ সম্পূর্ণ করা</li><li>ইংরেজি - নতুন শব্দ মুখস্থ করা</li><li>বিজ্ঞান - পরীক্ষার প্রস্তুতি নেওয়া</li></ul><p><strong>পরীক্ষার তারিখ:</strong> ২৫ জুলাই, ২০২৫</p><p>📝 <em>নোট: সকাল ৬টায় উঠে পড়াশোনা শুরু করতে হবে</em></p>',
        priority: 'high',

        reminder: new Date().toISOString(), // Set reminder for today
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo2',
        title: '🛒 কেনাকাটার তালিকা',
        content: '<h2>🛒 কেনাকাটার তালিকা</h2><p>আজ বাজার থেকে কিনতে হবে:</p><ul><li>চাল - ৫ কেজি</li><li>ডাল - ২ কেজি</li><li>পেঁয়াজ - ১ কেজি</li><li>আলু - ২ কেজি</li><li>মাছ - ১ কেজি রুই মাছ</li><li>সবজি - টমেটো, গাজর, শিম</li></ul><p>💰 <strong>আনুমানিক খরচ:</strong> ১৫০০ টাকা</p><p>⏰ <strong>সময়:</strong> বিকাল ৪টার মধ্যে শেষ করতে হবে</p>',
        priority: 'medium',

        reminder: null,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo3',
        title: '💼 অফিসের কাজ',
        content: '<h2>💼 অফিসের কাজ</h2><p><strong>আজকের মিটিং:</strong></p><ul><li>সকাল ১০টা - প্রজেক্ট রিভিউ</li><li>দুপুর ২টা - ক্লায়েন্ট মিটিং</li><li>বিকাল ৪টা - টিম স্ট্যান্ডআপ</li></ul><p><strong>জরুরি কাজ:</strong></p><ol><li>রিপোর্ট তৈরি করা</li><li>ইমেইল রিপ্লাই দেওয়া</li><li>প্রেজেন্টেশন প্রস্তুত করা</li></ol><p>📧 <em>গুরুত্বপূর্ণ: বসের সাথে বেতন নিয়ে কথা বলতে হবে</em></p>',
        priority: 'urgent',

        reminder: new Date().toISOString(), // Set reminder for today
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo4',
        title: '🎂 জন্মদিনের পার্টি পরিকল্পনা',
        content: '<h2>🎂 জন্মদিনের পার্টি পরিকল্পনা</h2><p>আমার ছোট বোনের জন্মদিন আগামী সপ্তাহে! 🎉</p><p><strong>প্রস্তুতি:</strong></p><ul><li>🎂 কেক অর্ডার করা</li><li>🎈 বেলুন ও সাজসজ্জার জিনিস কেনা</li><li>🎁 উপহার কেনা</li><li>👥 বন্ধুদের দাওয়াত দেওয়া</li><li>🍕 খাবারের ব্যবস্থা করা</li></ul><p><strong>বাজেট:</strong> ৫০০০ টাকা</p><p><strong>অতিথি সংখ্যা:</strong> ১৫ জন</p><p>🎵 <em>মিউজিক সিস্টেমের ব্যবস্থা করতে হবে</em></p>',
        priority: 'low',

        reminder: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo5',
        title: '🏥 স্বাস্থ্য পরীক্ষা',
        content: '<h2>🏥 স্বাস্থ্য পরীক্ষা</h2><p>আগামী সপ্তাহে ডাক্তারের কাছে যেতে হবে নিয়মিত চেকআপের জন্য।</p><p><strong>পরীক্ষা তালিকা:</strong></p><ul><li>রক্ত পরীক্ষা</li><li>চোখের পরীক্ষা</li><li>দাঁতের চেকআপ</li><li>হার্টের পরীক্ষা</li></ul><p><strong>ডাক্তারের অ্যাপয়েন্টমেন্ট:</strong></p><p>📅 তারিখ: ২০ জুলাই, ২০২৫</p><p>⏰ সময়: সকাল ৯টা</p><p>🏥 স্থান: সিটি হাসপাতাল</p><p>💊 <em>নিয়মিত ওষুধ নিতে ভুলবেন না</em></p>',
        priority: 'high',

        reminder: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo6',
        title: '🍳 রান্নার রেসিপি - বিরিয়ানি',
        content: '<h2>🍳 রান্নার রেসিপি - বিরিয়ানি</h2><p><strong>উপকরণ:</strong></p><ul><li>বাসমতি চাল - ৫০০ গ্রাম</li><li>গরুর মাংস - ১ কেজি</li><li>পেঁয়াজ - ৪টি (কাটা)</li><li>আদা-রসুন বাটা - ২ টেবিল চামচ</li><li>দই - ১ কাপ</li><li>বিরিয়ানি মসলা - ২ টেবিল চামচ</li><li>জাফরান - ১ চিমটি</li><li>দুধ - ১/২ কাপ</li></ul><p><strong>প্রস্তুতি প্রণালী:</strong></p><ol><li>মাংস মেরিনেট করুন ৩০ মিনিট</li><li>চাল ভিজিয়ে রাখুন</li><li>পেঁয়াজ ভেজে নিন</li><li>মাংস রান্না করুন</li><li>চাল সেদ্ধ করুন</li><li>স্তরে স্তরে সাজিয়ে দম দিন</li></ol><p>🕐 <strong>সময়:</strong> ২ ঘন্টা | 👥 <strong>পরিবেশন:</strong> ৬ জন</p><img src="https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop" alt="বিরিয়ানি" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'low',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo7',
        title: '💻 প্রোগ্রামিং প্রজেক্ট - ওয়েব অ্যাপ',
        content: '<h2>💻 প্রোগ্রামিং প্রজেক্ট - ওয়েব অ্যাপ</h2><p><strong>প্রজেক্ট নাম:</strong> ই-কমার্স ওয়েবসাইট</p><p><strong>প্রযুক্তি স্ট্যাক:</strong></p><ul><li>Frontend: React.js, Tailwind CSS</li><li>Backend: Node.js, Express.js</li><li>Database: MongoDB</li><li>Authentication: JWT</li><li>Payment: Stripe API</li></ul><p><strong>ফিচার তালিকা:</strong></p><ol><li>✅ ইউজার রেজিস্ট্রেশন ও লগইন</li><li>✅ প্রোডাক্ট ক্যাটালগ</li><li>🔄 শপিং কার্ট (চলমান)</li><li>⏳ পেমেন্ট গেটওয়ে (পরবর্তী)</li><li>⏳ অর্ডার ট্র্যাকিং (পরবর্তী)</li></ol><p><strong>গিটহাব রিপো:</strong> <a href="https://github.com/example/ecommerce-app">https://github.com/example/ecommerce-app</a></p><p>📅 <strong>ডেডলাইন:</strong> ৩১ জুলাই, ২০২৫</p><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">আপনার ব্রাউজার ভিডিও সাপোর্ট করে না।</video>',
        priority: 'urgent',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo8',
        title: '🎵 সঙ্গীত অনুশীলন',
        content: '<h2>🎵 সঙ্গীত অনুশীলন</h2><p>আজকের গিটার অনুশীলনের তালিকা:</p><p><strong>কর্ড অনুশীলন:</strong></p><ul><li>G Major - ১০ মিনিট</li><li>C Major - ১০ মিনিট</li><li>D Major - ১০ মিনিট</li><li>Em Minor - ১০ মিনিট</li></ul><p><strong>গান অনুশীলন:</strong></p><ol><li>"আমার সোনার বাংলা" - ২০ মিনিট</li><li>"একুশের গান" - ১৫ মিনিট</li><li>"ধন ধান্য পুষ্প ভরা" - ১৫ মিনিট</li></ol><p><strong>নতুন টেকনিক:</strong></p><p>আজ ফিঙ্গার পিকিং টেকনিক শিখব। ইউটিউবে টিউটোরিয়াল দেখে অনুশীলন করব।</p><p>🎼 <em>মনে রাখবেন: নিয়মিত অনুশীলনই সাফল্যের চাবিকাঠি</em></p>',
        priority: 'medium',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo9',
        title: '🌱 বাগান পরিচর্যা',
        content: '<h2>🌱 বাগান পরিচর্যা</h2><p>আমার ছাদ বাগানের আজকের কাজের তালিকা:</p><p><strong>সকালের কাজ (৬:০০ - ৮:০০):</strong></p><ul><li>🌿 সব গাছে পানি দেওয়া</li><li>🌱 নতুন চারা রোপণ (টমেটো ও মরিচ)</li><li>🍃 শুকনো পাতা পরিষ্কার করা</li><li>🐛 পোকামাকড় পরীক্ষা করা</li></ul><p><strong>বিকালের কাজ (৪:০০ - ৬:০০):</strong></p><ul><li>🌺 ফুলের গাছে সার দেওয়া</li><li>✂️ অতিরিক্ত ডাল ছাঁটাই</li><li>📝 বৃদ্ধির রেকর্ড আপডেট</li></ul><p><strong>এই মাসের ফসল:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>সবজি</th><th>পরিমাণ</th><th>অবস্থা</th></tr><tr><td>টমেটো</td><td>২ কেজি</td><td>পাকা</td></tr><tr><td>মরিচ</td><td>৫০০ গ্রাম</td><td>কাঁচা</td></tr><tr><td>পুদিনা</td><td>১০০ গ্রাম</td><td>তাজা</td></tr></table><img src="https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop" alt="বাগান" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'low',
        createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo10',
        title: '📚 বই পড়ার তালিকা - ২০২৫',
        content: '<h2>📚 বই পড়ার তালিকা - ২০২৫</h2><p><strong>এই মাসে পড়া বই:</strong></p><ol><li>✅ "দেবদাস" - শরৎচন্দ্র চট্টোপাধ্যায়</li><li>✅ "পথের পাঁচালী" - বিভূতিভূষণ বন্দ্যোপাধ্যায়</li><li>🔄 "গোরা" - রবীন্দ্রনাথ ঠাকুর (চলমান - ৬০%)</li></ol><p><strong>পরবর্তী মাসের পরিকল্পনা:</strong></p><ul><li>📖 "শেষের কবিতা" - রবীন্দ্রনাথ ঠাকুর</li><li>📖 "আরণ্যক" - বিভূতিভূষণ বন্দ্যোপাধ্যায়</li><li>📖 "চোখের বালি" - রবীন্দ্রনাথ ঠাকুর</li></ul><p><strong>পড়ার সময়সূচী:</strong></p><p>🌅 সকাল ৬:০০ - ৭:০০ (১ ঘন্টা)<br>🌙 রাত ৯:০০ - ১০:০০ (১ ঘন্টা)</p><blockquote style="border-left: 4px solid #007bff; padding-left: 15px; margin: 15px 0; font-style: italic;">"একটি বই হাজার বন্ধুর সমান" - প্রবাদ</blockquote><img src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop" alt="বই পড়া" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'medium',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo11',
        content: '<h2>🏋️‍♂️ ব্যায়াম ও ফিটনেস প্ল্যান</h2><p><strong>সাপ্তাহিক রুটিন:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>দিন</th><th>ব্যায়াম</th><th>সময়</th><th>ক্যালোরি</th></tr><tr><td>সোমবার</td><td>কার্ডিও + পুশআপ</td><td>৪৫ মিনিট</td><td>৩৫০</td></tr><tr><td>মঙ্গলবার</td><td>যোগব্যায়াম</td><td>৩০ মিনিট</td><td>২০০</td></tr><tr><td>বুধবার</td><td>ওয়েট ট্রেনিং</td><td>৬০ মিনিট</td><td>৪০০</td></tr><tr><td>বৃহস্পতিবার</td><td>সাঁতার</td><td>৪৫ মিনিট</td><td>৫০০</td></tr><tr><td>শুক্রবার</td><td>সাইক্লিং</td><td>৩০ মিনিট</td><td>৩০০</td></tr></table><p><strong>লক্ষ্য:</strong></p><ul><li>🎯 ওজন কমানো: ৫ কেজি (৩ মাসে)</li><li>💪 পেশী বৃদ্ধি: বুক ও হাতের পেশী</li><li>🏃‍♂️ সহনশীলতা বৃদ্ধি: ৫ কিমি দৌড়</li></ul><p><strong>খাদ্য তালিকা:</strong></p><p>🥗 সকাল: ওটমিল + ফল<br>🍽️ দুপুর: ভাত + মাছ + সবজি<br>🥙 রাত: রুটি + ডাল + সালাদ</p><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">ব্যায়ামের ভিডিও</video>',
        priority: 'high',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo12',
        content: '<h2>🎨 শিল্প প্রজেক্ট - পেইন্টিং</h2><p><strong>প্রজেক্ট:</strong> প্রকৃতির ল্যান্ডস্কেপ পেইন্টিং</p><p><strong>উপকরণ তালিকা:</strong></p><ul><li>🎨 অ্যাক্রিলিক রং (১২ রঙের সেট)</li><li>🖌️ ব্রাশ সেট (৫টি বিভিন্ন সাইজ)</li><li>🖼️ ক্যানভাস (১৬"×২০")</li><li>🎭 প্যালেট ও প্যালেট নাইফ</li><li>💧 পানির পাত্র</li><li>🧽 স্পঞ্জ ও টিস্যু</li></ul><p><strong>রঙের মিশ্রণ নোট:</strong></p><p>🔵 নীল + 🟡 হলুদ = 🟢 সবুজ<br>🔴 লাল + 🟡 হলুদ = 🟠 কমলা<br>🔴 লাল + 🔵 নীল = 🟣 বেগুনি</p><p><strong>টেকনিক:</strong></p><ol><li>স্কেচিং - পেন্সিল দিয়ে প্রাথমিক আকার</li><li>ব্যাকগ্রাউন্ড - আকাশ ও পাহাড়</li><li>মিডগ্রাউন্ড - গাছপালা ও নদী</li><li>ফোরগ্রাউন্ড - ঘাস ও ফুল</li><li>ডিটেইলিং - চূড়ান্ত স্পর্শ</li></ol><img src="https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop" alt="পেইন্টিং" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>🕐 <strong>আনুমানিক সময়:</strong> ৪-৫ ঘন্টা</p>',
        priority: 'low',
        createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo13',
        content: '<h2>🚗 গাড়ি রক্ষণাবেক্ষণ</h2><p><strong>গাড়ির তথ্য:</strong> টয়োটা করোলা ২০২০</p><p><strong>এই মাসের কাজ:</strong></p><ul><li>✅ ইঞ্জিন অয়েল পরিবর্তন (সম্পন্ন - ৫ জুলাই)</li><li>🔄 টায়ার চেক ও রোটেশন (আজ করতে হবে)</li><li>⏳ এয়ার ফিল্টার পরিবর্তন (১৫ জুলাই)</li><li>⏳ ব্রেক প্যাড চেক (২০ জুলাই)</li></ul><p><strong>খরচের হিসাব:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>সেবা</th><th>তারিখ</th><th>খরচ</th><th>পরবর্তী সেবা</th></tr><tr><td>ইঞ্জিন অয়েল</td><td>৫ জুলাই</td><td>২৫০০ টাকা</td><td>৫ অক্টোবর</td></tr><tr><td>টায়ার চেক</td><td>১০ জুলাই</td><td>৫০০ টাকা</td><td>১০ অক্টোবর</td></tr></table><p><strong>জরুরি যোগাযোগ:</strong></p><p>🔧 মেকানিক: করিম ভাই - ০১৭১২৩৪৫৬৭৮<br>🏪 গ্যারেজ: আল-আমিন অটো সার্ভিস<br>📍 ঠিকানা: ধানমন্ডি, ঢাকা</p><img src="https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=300&fit=crop" alt="গাড়ি" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'medium',
        createdAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo14',
        content: '<h2>🎓 অনলাইন কোর্স - ডেটা সায়েন্স</h2><p><strong>কোর্স:</strong> "Complete Data Science Bootcamp"</p><p><strong>প্ল্যাটফর্ম:</strong> Coursera</p><p><strong>অগ্রগতি:</strong></p><div style="background: #f0f0f0; border-radius: 10px; padding: 5px; margin: 10px 0;"><div style="background: #007bff; height: 20px; width: 65%; border-radius: 10px; text-align: center; color: white; line-height: 20px;">৬৫%</div></div><p><strong>সম্পন্ন মডিউল:</strong></p><ol><li>✅ Introduction to Data Science</li><li>✅ Python Programming Basics</li><li>✅ Data Manipulation with Pandas</li><li>✅ Data Visualization with Matplotlib</li><li>🔄 Machine Learning Fundamentals (চলমান)</li></ol><p><strong>আসন্ন মডিউল:</strong></p><ul><li>📚 Deep Learning with TensorFlow</li><li>📚 Natural Language Processing</li><li>📚 Capstone Project</li></ul><p><strong>অ্যাসাইনমেন্ট:</strong></p><p>📝 এই সপ্তাহে: "Customer Segmentation Analysis"<br>📅 জমা দেওয়ার তারিখ: ১৮ জুলাই, ২০২৫</p><p>🏆 <strong>লক্ষ্য:</strong> আগস্টের মধ্যে কোর্স সম্পন্ন করে সার্টিফিকেট অর্জন</p>',
        priority: 'high',
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo15',
        content: '<h2>🏠 ঘর সাজানোর পরিকল্পনা</h2><p><strong>রুম:</strong> বেডরুম রিনোভেশন</p><p><strong>বাজেট:</strong> ৫০,০০০ টাকা</p><p><strong>কেনাকাটার তালিকা:</strong></p><ul><li>🛏️ নতুন বিছানা (কিং সাইজ) - ২০,০০০ টাকা</li><li>🪑 ড্রেসিং টেবিল - ৮,০০০ টাকা</li><li>💡 LED লাইট (ওয়ার্ম হোয়াইট) - ৩,০০০ টাকা</li><li>🖼️ ওয়াল আর্ট ও ফ্রেম - ৫,০০০ টাকা</li><li>🪟 পর্দা (ব্লু কালার) - ৪,০০০ টাকা</li><li>🌿 ইনডোর প্ল্যান্ট - ২,০০০ টাকা</li></ul><p><strong>রঙের স্কিম:</strong></p><p>🎨 প্রাথমিক: সাদা ও হালকা নীল<br>🎨 সেকেন্ডারি: সোনালি ও বেইজ<br>🎨 অ্যাকসেন্ট: গাঢ় নীল</p><p><strong>কাজের সময়সূচী:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>কাজ</th><th>তারিখ</th><th>সময়</th></tr><tr><td>দেয়াল পেইন্টিং</td><td>২০-২১ জুলাই</td><td>২ দিন</td></tr><tr><td>ফার্নিচার সেটআপ</td><td>২২ জুলাই</td><td>১ দিন</td></tr><tr><td>ডেকোরেশন</td><td>২৩ জুলাই</td><td>১ দিন</td></tr></table><img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop" alt="বেডরুম" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'medium',
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo16',
        content: '<h2>💰 মাসিক বাজেট পরিকল্পনা</h2><p><strong>মাস:</strong> জুলাই ২০২৫</p><p><strong>মোট আয়:</strong> ৮০,০০০ টাকা</p><p><strong>খরচের বিভাগ:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>বিভাগ</th><th>বাজেট</th><th>খরচ</th><th>অবশিষ্ট</th></tr><tr><td>🏠 ঘর ভাড়া</td><td>২০,০০০</td><td>২০,০০০</td><td>০</td></tr><tr><td>🍽️ খাবার</td><td>১৫,০০০</td><td>১২,৫০০</td><td>২,৫০০</td></tr><tr><td>🚗 যাতায়াত</td><td>৮,০০০</td><td>৬,২০০</td><td>১,৮০০</td></tr><tr><td>💡 ইউটিলিটি</td><td>৫,০০০</td><td>৪,৮০০</td><td>২০০</td></tr><tr><td>🎯 বিনোদন</td><td>৭,০০০</td><td>৩,৫০০</td><td>৩,৫০০</td></tr><tr><td>💊 স্বাস্থ্য</td><td>৫,০০০</td><td>২,০০০</td><td>৩,০০০</td></tr><tr><td>💰 সঞ্চয়</td><td>২০,০০০</td><td>২০,০০০</td><td>০</td></tr></table><p><strong>সঞ্চয়ের লক্ষ্য:</strong></p><ul><li>🏦 ব্যাংক সঞ্চয়: ১৫,০০০ টাকা</li><li>📈 বিনিয়োগ: ৫,০০০ টাকা</li></ul><p><strong>আগামী মাসের পরিকল্পনা:</strong></p><p>📊 খরচ কমানোর ক্ষেত্র: বিনোদন ও খাবার<br>📈 আয় বৃদ্ধির উপায়: ফ্রিল্যান্সিং প্রজেক্ট</p><div style="background: #28a745; color: white; padding: 10px; border-radius: 5px; margin: 10px 0;">💡 টিপ: প্রতিদিন খরচের হিসাব রাখুন</div>',
        priority: 'high',
        createdAt: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo17',
        content: '<h2>🌍 ভ্রমণ পরিকল্পনা - কক্সবাজার</h2><p><strong>ভ্রমণের তারিখ:</strong> ২৫-২৮ জুলাই, ২০২৫ (৪ দিন)</p><p><strong>যাত্রী সংখ্যা:</strong> ৪ জন (পরিবার)</p><p><strong>যাতায়াত:</strong></p><ul><li>🚌 ঢাকা থেকে কক্সবাজার: AC বাস</li><li>🕐 যাত্রা: রাত ১০:৩০ (২৪ জুলাই)</li><li>🕘 পৌঁছানো: সকাল ৯:০০ (২৫ জুলাই)</li><li>💰 খরচ: ৮,০০০ টাকা (৪ জনের)</li></ul><p><strong>থাকার ব্যবস্থা:</strong></p><p>🏨 হোটেল: সী প্যালেস রিসোর্ট<br>🛏️ রুম: ২টি ডাবল রুম (সমুদ্র দৃশ্য)<br>💰 খরচ: ১৮,০০০ টাকা (৩ রাত)</p><p><strong>দর্শনীয় স্থান:</strong></p><ol><li>🏖️ কক্সবাজার সমুদ্র সৈকত</li><li>🌅 ইনানী বিচ</li><li>🏛️ হিমছড়ি</li><li>🦌 সাফারি পার্ক</li><li>🏝️ সেন্ট মার্টিন দ্বীপ (দিনে ভ্রমণ)</li></ol><p><strong>মোট বাজেট:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>খাত</th><th>খরচ</th></tr><tr><td>যাতায়াত</td><td>১০,০০০</td></tr><tr><td>হোটেল</td><td>১৮,০০০</td></tr><tr><td>খাবার</td><td>১২,০০০</td></tr><tr><td>ভ্রমণ ও বিনোদন</td><td>৮,০০০</td></tr><tr><td>অন্যান্য</td><td>২,০০০</td></tr><tr><td><strong>মোট</strong></td><td><strong>৫০,০০০</strong></td></tr></table><img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop" alt="কক্সবাজার" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;">',
        priority: 'medium',
        createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo18',
        content: '<h2>📱 মোবাইল অ্যাপ ডেভেলপমেন্ট</h2><p><strong>অ্যাপ নাম:</strong> "স্মার্ট শপিং"</p><p><strong>প্ল্যাটফর্ম:</strong> Android & iOS</p><p><strong>প্রযুক্তি:</strong> React Native, Firebase</p><p><strong>ফিচার তালিকা:</strong></p><ul><li>✅ ইউজার রেজিস্ট্রেশন ও লগইন</li><li>✅ প্রোডাক্ট ব্রাউজিং</li><li>🔄 শপিং কার্ট (৮০% সম্পন্ন)</li><li>⏳ পেমেন্ট ইন্টিগ্রেশন</li><li>⏳ অর্ডার ট্র্যাকিং</li><li>⏳ পুশ নোটিফিকেশন</li></ul><p><strong>ডেভেলপমেন্ট টাইমলাইন:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>ফেজ</th><th>কাজ</th><th>সময়সীমা</th><th>স্ট্যাটাস</th></tr><tr><td>১</td><td>UI/UX ডিজাইন</td><td>১-৭ জুলাই</td><td>✅ সম্পন্ন</td></tr><tr><td>২</td><td>ফ্রন্টএন্ড ডেভেলপমেন্ট</td><td>৮-২০ জুলাই</td><td>🔄 চলমান</td></tr><tr><td>৩</td><td>ব্যাকএন্ড ইন্টিগ্রেশন</td><td>২১-৩১ জুলাই</td><td>⏳ পরবর্তী</td></tr><tr><td>৪</td><td>টেস্টিং ও ডিপ্লয়মেন্ট</td><td>১-১০ আগস্ট</td><td>⏳ পরবর্তী</td></tr></table><p><strong>টিম মেম্বার:</strong></p><p>👨‍💻 ডেভেলপার: আমি<br>🎨 ডিজাইনার: সারা আপা<br>🧪 টেস্টার: রহিম ভাই</p><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">অ্যাপ ডেমো ভিডিও</video><p>🎯 <strong>লক্ষ্য:</strong> আগস্টের মধ্যে প্লে স্টোরে পাবলিশ করা</p>',
        priority: 'urgent',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo19',
        content: '<h2>🍰 বেকিং রেসিপি - চকলেট কেক</h2><p><strong>উপকরণ (কেক):</strong></p><ul><li>ময়দা - ২ কাপ</li><li>চিনি - ১.৫ কাপ</li><li>কোকো পাউডার - ৩/ৄ কাপ</li><li>বেকিং পাউডার - ২ চা চামচ</li><li>ডিম - ২টি</li><li>দুধ - ১ কাপ</li><li>তেল - ১/২ কাপ</li><li>ভ্যানিলা এসেন্স - ১ চা চামচ</li></ul><p><strong>উপকরণ (ফ্রস্টিং):</strong></p><ul><li>মাখন - ১০০ গ্রাম</li><li>পাউডার চিনি - ২ কাপ</li><li>কোকো পাউডার - ৩ টেবিল চামচ</li><li>দুধ - ২-৩ টেবিল চামচ</li></ul><p><strong>প্রস্তুতি প্রণালী:</strong></p><ol><li>ওভেন ১৮০°C তাপমাত্রায় প্রিহিট করুন</li><li>শুকনো উপকরণ একসাথে মিশান</li><li>ভেজা উপকরণ আলাদা বাটিতে মিশান</li><li>দুটি মিশ্রণ একসাথে করে ব্যাটার তৈরি করুন</li><li>গ্রিজ করা প্যানে ঢেলে ৩০-৩৫ মিনিট বেক করুন</li><li>ঠান্ডা হলে ফ্রস্টিং দিয়ে সাজান</li></ol><p><strong>টিপস:</strong></p><div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0;">💡 কেক টেস্ট করতে টুথপিক ব্যবহার করুন<br>💡 ফ্রস্টিং দেওয়ার আগে কেক সম্পূর্ণ ঠান্ডা হতে দিন</div><img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop" alt="চকলেট কেক" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>🕐 <strong>মোট সময়:</strong> ১.৫ ঘন্টা | 🍰 <strong>পরিবেশন:</strong> ৮-১০ জন</p>',
        priority: 'low',
        createdAt: new Date(Date.now() - 13 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo20',
        content: '<h2>🎬 সিনেমা রিভিউ - "পদ্মা নদীর মাঝি"</h2><p><strong>পরিচালক:</strong> গৌতম ঘোষ</p><p><strong>মুক্তির বছর:</strong> ১৯৯৩</p><p><strong>ধরন:</strong> ড্রামা</p><p><strong>রেটিং:</strong> ⭐⭐⭐⭐⭐ (৫/৫)</p><p><strong>কাহিনী সংক্ষেপ:</strong></p><p>মানিক বন্দ্যোপাধ্যায়ের বিখ্যাত উপন্যাস অবলম্বনে নির্মিত এই চলচ্চিত্রে দেখানো হয়েছে পদ্মা নদীর তীরবর্তী জেলে সম্প্রদায়ের জীবনযাত্রা। কুবের ও তার স্ত্রী কপিলার সংগ্রামী জীবনের মর্মস্পর্শী গল্প।</p><p><strong>উল্লেখযোগ্য দিক:</strong></p><ul><li>🎭 অসাধারণ অভিনয় - উৎপল দত্ত, রৌমারী সেন</li><li>📸 চমৎকার সিনেমাটোগ্রাফি</li><li>🎵 হৃদয়স্পর্শী সঙ্গীত</li><li>📝 শক্তিশালী চিত্রনাট্য</li></ul><p><strong>প্রিয় দৃশ্য:</strong></p><p>নদীতে মাছ ধরার দৃশ্যগুলো অত্যন্ত বাস্তবধর্মী এবং মনোমুগ্ধকর।</p><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">সিনেমার ট্রেইলার</video><blockquote style="border-left: 4px solid #28a745; padding-left: 15px; margin: 15px 0; font-style: italic;">"এই সিনেমা বাংলা সিনেমার ইতিহাসে একটি মাইলফলক।"</blockquote>',
        priority: 'low',
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo21',
        content: '<h2>🏥 ডাক্তারের অ্যাপয়েন্টমেন্ট ট্র্যাকার</h2><p><strong>রোগীর তথ্য:</strong></p><ul><li>👤 নাম: মোহাম্মদ করিম</li><li>📅 বয়স: ৪৫ বছর</li><li>🩸 রক্তের গ্রুপ: B+</li><li>📞 ফোন: ০১৭১২৩৪৫৬৭৮</li></ul><p><strong>আসন্ন অ্যাপয়েন্টমেন্ট:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>তারিখ</th><th>সময়</th><th>ডাক্তার</th><th>বিভাগ</th><th>স্ট্যাটাস</th></tr><tr><td>১৮ জুলাই</td><td>১০:০০ AM</td><td>ডা. রহমান</td><td>কার্ডিওলজি</td><td>✅ নিশ্চিত</td></tr><tr><td>২২ জুলাই</td><td>২:৩০ PM</td><td>ডা. খান</td><td>অর্থোপেডিক্স</td><td>⏳ অপেক্ষমাণ</td></tr><tr><td>২৫ জুলাই</td><td>৯:০০ AM</td><td>ডা. আহমেদ</td><td>নিউরোলজি</td><td>📞 কল করতে হবে</td></tr></table><p><strong>পূর্ববর্তী রিপোর্ট:</strong></p><ul><li>📋 রক্ত পরীক্ষা (১০ জুলাই) - স্বাভাবিক</li><li>🫀 ECG (১২ জুলাই) - সামান্য অনিয়ম</li><li>🦴 X-Ray (১৫ জুলাই) - হাঁটুতে সামান্য ক্ষয়</li></ul><p><strong>নিয়মিত ওষুধ:</strong></p><ol><li>💊 Amlodipine 5mg - দিনে ১ বার (সকালে)</li><li>💊 Metformin 500mg - দিনে ২ বার (খাবারের পর)</li><li>💊 Calcium + Vitamin D - দিনে ১ বার (রাতে)</li></ol><div style="background: #dc3545; color: white; padding: 10px; border-radius: 5px; margin: 10px 0;">⚠️ জরুরি: উচ্চ রক্তচাপের ওষুধ নিয়মিত খেতে হবে</div>',
        priority: 'urgent',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo22',
        content: '<h2>📊 ব্যবসায়িক পরিকল্পনা - অনলাইন শপ</h2><p><strong>ব্যবসার নাম:</strong> "দেশি বাজার"</p><p><strong>ব্যবসার ধরন:</strong> অনলাইন গ্রোসারি শপ</p><p><strong>টার্গেট মার্কেট:</strong></p><ul><li>🏙️ শহুরে পরিবার</li><li>👩‍💼 কর্মজীবী মহিলা</li><li>👨‍💻 ব্যস্ত পেশাদার</li><li>👴 বয়স্ক ব্যক্তি</li></ul><p><strong>প্রোডাক্ট ক্যাটাগরি:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>ক্যাটাগরি</th><th>প্রোডাক্ট সংখ্যা</th><th>গড় মার্জিন</th></tr><tr><td>🌾 চাল-ডাল</td><td>৫০</td><td>১৫%</td></tr><tr><td>🥬 তাজা সবজি</td><td>৮০</td><td>২৫%</td></tr><tr><td>🐟 মাছ-মাংস</td><td>৩০</td><td>২০%</td></tr><tr><td>🥛 দুগ্ধজাত</td><td>২৫</td><td>১৮%</td></tr><tr><td>🍯 মসলা-তেল</td><td>৪০</td><td>২২%</td></tr></table><p><strong>মাসিক আর্থিক লক্ষ্য:</strong></p><ul><li>💰 বিক্রয়: ৫,০০,০০০ টাকা</li><li>📈 মুনাফা: ১,০০,০০০ টাকা (২০%)</li><li>🛒 অর্ডার সংখ্যা: ১,০০০টি</li><li>👥 নতুন কাস্টমার: ২০০ জন</li></ul><p><strong>মার্কেটিং কৌশল:</strong></p><ol><li>📱 সোশ্যাল মিডিয়া প্রচার</li><li>🎁 প্রথম অর্ডারে ছাড়</li><li>🚚 ফ্রি ডেলিভারি (৫০০+ টাকা)</li><li>⭐ কাস্টমার রিভিউ সিস্টেম</li></ol><img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop" alt="অনলাইন শপিং" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>🎯 <strong>৬ মাসের লক্ষ্য:</strong> ঢাকার টপ ৫ অনলাইন গ্রোসারি শপের মধ্যে স্থান করা</p>',
        priority: 'high',
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo23',
        content: '<h2>🎸 গিটার শেখার যাত্রা</h2><p><strong>কোর্স:</strong> "Complete Guitar Mastery"</p><p><strong>প্রশিক্ষক:</strong> জন স্মিথ (অনলাইন)</p><p><strong>কোর্সের সময়কাল:</strong> ৬ মাস</p><p><strong>সাপ্তাহিক অনুশীলন:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>দিন</th><th>সময়</th><th>বিষয়</th><th>অগ্রগতি</th></tr><tr><td>সোমবার</td><td>৭-৮ PM</td><td>কর্ড অনুশীলন</td><td>✅ সম্পন্ন</td></tr><tr><td>বুধবার</td><td>৭-৮ PM</td><td>স্ট্রামিং প্যাটার্ন</td><td>🔄 চলমান</td></tr><tr><td>শুক্রবার</td><td>৭-৮ PM</td><td>গান অনুশীলন</td><td>⏳ পরবর্তী</td></tr><tr><td>রবিবার</td><td>৬-৭ PM</td><td>ফিঙ্গার এক্সারসাইজ</td><td>⏳ পরবর্তী</td></tr></table><p><strong>শেখা কর্ড:</strong></p><ul><li>✅ G Major</li><li>✅ C Major</li><li>✅ D Major</li><li>✅ Em Minor</li><li>🔄 Am Minor (অনুশীলন চলছে)</li><li>⏳ F Major (পরবর্তী)</li></ul><p><strong>প্রিয় গান:</strong></p><ol><li>🎵 "Wonderwall" - Oasis</li><li>🎵 "Blackbird" - The Beatles</li><li>🎵 "Hotel California" - Eagles</li></ol><p><strong>লক্ষ্য:</strong></p><p>🎯 ৩ মাসে ১০টি গান বাজাতে পারা<br>🎯 ৬ মাসে একটি পূর্ণাঙ্গ পারফরমেন্স দেওয়া</p>',
        priority: 'medium',
        createdAt: new Date(Date.now() - 16 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo24',
        content: '<h2>🌟 ব্যক্তিত্ব উন্নয়ন পরিকল্পনা</h2><p><strong>লক্ষ্য:</strong> আত্মবিশ্বাস ও যোগাযোগ দক্ষতা বৃদ্ধি</p><p><strong>দৈনিক অভ্যাস:</strong></p><ul><li>🧘‍♂️ সকালে ১০ মিনিট মেডিটেশন</li><li>📚 দিনে ৩০ মিনিট বই পড়া</li><li>🗣️ আয়নার সামনে ৫ মিনিট বক্তৃতা অনুশীলন</li><li>📝 রাতে দিনের ৩টি ভালো কাজ লেখা</li><li>💪 ইতিবাচক চিন্তা করা</li></ul><p><strong>সাপ্তাহিক চ্যালেঞ্জ:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>সপ্তাহ</th><th>চ্যালেঞ্জ</th><th>স্ট্যাটাস</th></tr><tr><td>১</td><td>অপরিচিত ৫ জনের সাথে কথা বলা</td><td>✅ সম্পন্ন</td></tr><tr><td>২</td><td>একটি পাবলিক স্পিকিং ইভেন্টে অংশগ্রহণ</td><td>🔄 চলমান</td></tr><tr><td>৩</td><td>নতুন একটি দক্ষতা শেখা</td><td>⏳ পরবর্তী</td></tr><tr><td>৪</td><td>একটি নেটওয়ার্কিং ইভেন্টে যোগদান</td><td>⏳ পরবর্তী</td></tr></table><p><strong>পড়া বই:</strong></p><ol><li>📖 "How to Win Friends and Influence People" - Dale Carnegie</li><li>📖 "The 7 Habits of Highly Effective People" - Stephen Covey</li><li>📖 "Mindset" - Carol Dweck</li></ol><p><strong>অনুপ্রেরণামূলক উক্তি:</strong></p><blockquote style="border-left: 4px solid #007bff; padding-left: 15px; margin: 15px 0; font-style: italic;">"সফলতা একদিনে আসে না, এটি প্রতিদিনের ছোট ছোট উন্নতির ফল।"</blockquote><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">মোটিভেশনাল ভিডিও</video><p>🎯 <strong>৩ মাসের লক্ষ্য:</strong> আত্মবিশ্বাসের সাথে ১০০ জনের সামনে বক্তৃতা দেওয়া</p>',
        priority: 'high',
        createdAt: new Date(Date.now() - 17 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 13 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo25',
        content: '<h2>🏃‍♂️ ম্যারাথন প্রস্তুতি</h2><p><strong>ইভেন্ট:</strong> ঢাকা ম্যারাথন ২০২৫</p><p><strong>তারিখ:</strong> ১৫ ডিসেম্বর, ২০২৫</p><p><strong>দূরত্ব:</strong> ২১ কিমি (হাফ ম্যারাথন)</p><p><strong>বর্তমান ফিটনেস লেভেল:</strong></p><ul><li>🏃‍♂️ সর্বোচ্চ দৌড়: ৮ কিমি</li><li>⏱️ গড় গতি: ৬ মিনিট/কিমি</li><li>💓 বিশ্রামকালীন হার্ট রেট: ৬৮ bpm</li><li>🏋️‍♂️ সাপ্তাহিক ট্রেনিং: ৪ দিন</li></ul><p><strong>১২ সপ্তাহের ট্রেনিং প্ল্যান:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>সপ্তাহ</th><th>সোমবার</th><th>বুধবার</th><th>শুক্রবার</th><th>রবিবার</th></tr><tr><td>১-২</td><td>৩ কিমি</td><td>৪ কিমি</td><td>৩ কিমি</td><td>৬ কিমি</td></tr><tr><td>৩-৪</td><td>৪ কিমি</td><td>৫ কিমি</td><td>৪ কিমি</td><td>৮ কিমি</td></tr><tr><td>৫-৬</td><td>৫ কিমি</td><td>৬ কিমি</td><td>৫ কিমি</td><td>১০ কিমি</td></tr><tr><td>৭-৮</td><td>৬ কিমি</td><td>৭ কিমি</td><td>৬ কিমি</td><td>১৩ কিমি</td></tr></table><p><strong>পুষ্টি পরিকল্পনা:</strong></p><ul><li>🥗 প্রতিদিন ৫ পরিবেশন ফল ও সবজি</li><li>🍗 প্রোটিন: মাছ, মুরগি, ডাল</li><li>🍞 কার্বোহাইড্রেট: ভাত, রুটি, ওটস</li><li>💧 দিনে ৩-৪ লিটার পানি</li><li>🚫 জাঙ্ক ফুড এড়িয়ে চলা</li></ul><p><strong>প্রয়োজনীয় সরঞ্জাম:</strong></p><ol><li>👟 ভালো রানিং শুজ - ৮,০০০ টাকা</li><li>👕 ড্রাই-ফিট জার্সি - ২,০০০ টাকা</li><li>⌚ ফিটনেস ট্র্যাকার - ১০,০০০ টাকা</li><li>🧴 ওয়াটার বটল - ৫০০ টাকা</li></ol><img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop" alt="ম্যারাথন" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>🏆 <strong>লক্ষ্য সময়:</strong> ২ ঘন্টা ১৫ মিনিটের মধ্যে শেষ করা</p>',
        priority: 'medium',
        createdAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo26',
        content: '<h2>🎭 থিয়েটার প্রোডাকশন - "রক্তকরবী"</h2><p><strong>নাটক:</strong> রক্তকরবী (রবীন্দ্রনাথ ঠাকুর)</p><p><strong>পরিচালক:</strong> আতাউর রহমান</p><p><strong>প্রোডাকশন হাউস:</strong> ঢাকা থিয়েটার</p><p><strong>আমার ভূমিকা:</strong> নন্দিনী (প্রধান চরিত্র)</p><p><strong>রিহার্সাল সময়সূচী:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>দিন</th><th>সময়</th><th>দৃশ্য</th><th>স্থান</th></tr><tr><td>সোমবার</td><td>৬-৯ PM</td><td>Act 1, Scene 1-2</td><td>স্টুডিও A</td></tr><tr><td>বুধবার</td><td>৬-৯ PM</td><td>Act 2, Scene 1-3</td><td>স্টুডিও B</td></tr><tr><td>শুক্রবার</td><td>৬-৯ PM</td><td>Act 3, পূর্ণ দৃশ্য</td><td>মূল মঞ্চ</td></tr><tr><td>রবিবার</td><td>৪-৮ PM</td><td>সম্পূর্ণ নাটক রান-থ্রু</td><td>মূল মঞ্চ</td></tr></table><p><strong>মুখস্থ করার অগ্রগতি:</strong></p><ul><li>✅ Act 1 - সম্পূর্ণ মুখস্থ</li><li>🔄 Act 2 - ৭০% মুখস্থ</li><li>⏳ Act 3 - ৩০% মুখস্থ</li></ul><p><strong>পোশাক ও মেকআপ:</strong></p><ul><li>👗 পোশাক ফিটিং: ২০ জুলাই</li><li>💄 মেকআপ টেস্ট: ২২ জুলাই</li><li>📸 ফটোশুট: ২৫ জুলাই</li></ul><p><strong>পারফরমেন্স তারিখ:</strong></p><p>🎭 প্রিমিয়ার: ৩০ জুলাই, ২০২৫<br>🎭 নিয়মিত শো: ১-১৫ আগস্ট (সপ্তাহে ৩ দিন)</p><blockquote style="border-left: 4px solid #dc3545; padding-left: 15px; margin: 15px 0; font-style: italic;">"আমি নন্দিনী, আমি কারো অধীনে নই।" - নন্দিনী</blockquote>',
        priority: 'high',
        createdAt: new Date(Date.now() - 19 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo27',
        content: '<h2>🌱 পরিবেশ সংরক্ষণ প্রকল্প</h2><p><strong>প্রকল্প নাম:</strong> "সবুজ ঢাকা" উদ্যোগ</p><p><strong>উদ্দেশ্য:</strong> শহরে বৃক্ষরোপণ ও পরিবেশ সচেতনতা বৃদ্ধি</p><p><strong>টিম সদস্য:</strong> ১৫ জন স্বেচ্ছাসেবক</p><p><strong>এই মাসের কার্যক্রম:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>তারিখ</th><th>কার্যক্রম</th><th>স্থান</th><th>অংশগ্রহণকারী</th></tr><tr><td>২০ জুলাই</td><td>বৃক্ষরোপণ</td><td>রমনা পার্ক</td><td>৫০ জন</td></tr><tr><td>২৫ জুলাই</td><td>পরিচ্ছন্নতা অভিযান</td><td>ধানমন্ডি লেক</td><td>৩০ জন</td></tr><tr><td>৩০ জুলাই</td><td>সচেতনতা সেমিনার</td><td>ঢাকা বিশ্ববিদ্যালয়</td><td>২০০ জন</td></tr></table><p><strong>রোপণ করা গাছের তালিকা:</strong></p><ul><li>🌳 নিম গাছ - ২৫টি</li><li>🌳 কৃষ্ণচূড়া - ১৫টি</li><li>🌳 শিমুল - ১০টি</li><li>🌳 বট গাছ - ৫টি</li><li>🌿 ফুলের গাছ - ৫০টি</li></ul><p><strong>পরিবেশগত প্রভাব:</strong></p><ul><li>🌬️ বার্ষিক CO2 শোষণ: ২ টন</li><li>💧 বৃষ্টির পানি সংরক্ষণ: ১০,০০০ লিটার</li><li>🐦 পাখির আবাসস্থল: ১০০+ পাখি</li><li>🌡️ তাপমাত্রা হ্রাস: ২-৩ ডিগ্রি</li></ul><p><strong>আর্থিক তথ্য:</strong></p><p>💰 মোট বাজেট: ৫০,০০০ টাকা<br>💰 খরচ হয়েছে: ৩৫,০০০ টাকা<br>💰 অবশিষ্ট: ১৫,০০০ টাকা</p><video controls style="width: 100%; max-width: 500px; border-radius: 8px; margin: 10px 0;"><source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">বৃক্ষরোপণ কার্যক্রমের ভিডিও</video><div style="background: #28a745; color: white; padding: 10px; border-radius: 5px; margin: 10px 0;">🌍 আমাদের লক্ষ্য: এক বছরে ১০০০ গাছ রোপণ</div>',
        priority: 'medium',
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 16 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo28',
        content: '<h2>📷 ফটোগ্রাফি প্রজেক্ট - "পুরান ঢাকা"</h2><p><strong>প্রজেক্ট থিম:</strong> পুরান ঢাকার ঐতিহ্য ও সংস্কৃতি</p><p><strong>ক্যামেরা:</strong> Canon EOS 5D Mark IV</p><p><strong>লেন্স:</strong> 24-70mm f/2.8, 50mm f/1.4</p><p><strong>শুটিং লোকেশন:</strong></p><ul><li>📍 লালবাগ কেল্লা - ঐতিহাসিক স্থাপত্য</li><li>📍 আহসান মঞ্জিল - মুঘল স্থাপত্য</li><li>📍 শাঁখারী বাজার - ঐতিহ্যবাহী ব্যবসা</li><li>📍 ধোলাইখাল - নৌকা ও নদী জীবন</li><li>📍 চকবাজার - রাস্তার খাবার</li></ul><p><strong>ফটো ক্যাটাগরি:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>ক্যাটাগরি</th><th>তোলা ছবি</th><th>সিলেক্ট</th><th>এডিট সম্পন্ন</th></tr><tr><td>স্থাপত্য</td><td>১৫০</td><td>২৫</td><td>১৫</td></tr><tr><td>মানুষ ও জীবনযাত্রা</td><td>২০০</td><td>৩০</td><td>২০</td></tr><tr><td>খাবার</td><td>৮০</td><td>১৫</td><td>১০</td></tr><tr><td>রাস্তার দৃশ্য</td><td>১২০</td><td>২০</td><td>১২</td></tr></table><p><strong>এডিটিং সফটওয়্যার:</strong></p><ul><li>🖥️ Adobe Lightroom - কালার কারেকশন</li><li>🖥️ Adobe Photoshop - ডিটেইল এডিটিং</li><li>📱 VSCO - মোবাইল এডিটিং</li></ul><p><strong>প্রদর্শনী পরিকল্পনা:</strong></p><p>🖼️ স্থান: বাংলা একাডেমি<br>📅 তারিখ: ১৫-৩০ আগস্ট, ২০২৫<br>🎯 দর্শক: ৫০০+ মানুষ</p><img src="https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop" alt="পুরান ঢাকা" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>📸 <strong>বিশেষ মুহূর্ত:</strong> সূর্যাস্তের সময় লালবাগ কেল্লার ছবি</p>',
        priority: 'low',
        createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 17 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo29',
        content: '<h2>🎓 গবেষণা প্রবন্ধ - "বাংলাদেশে ডিজিটাল শিক্ষা"</h2><p><strong>বিষয়:</strong> কোভিড-১৯ পরবর্তী বাংলাদেশে ডিজিটাল শিক্ষার প্রভাব ও সম্ভাবনা</p><p><strong>গবেষণার ধরন:</strong> মিশ্র পদ্ধতি (Quantitative + Qualitative)</p><p><strong>নমুনা সংখ্যা:</strong> ৫০০ শিক্ষার্থী, ১০০ শিক্ষক</p><p><strong>গবেষণার অগ্রগতি:</strong></p><div style="background: #f0f0f0; border-radius: 10px; padding: 5px; margin: 10px 0;"><div style="background: #007bff; height: 20px; width: 75%; border-radius: 10px; text-align: center; color: white; line-height: 20px;">৭৫%</div></div><p><strong>সম্পন্ন কাজ:</strong></p><ul><li>✅ সাহিত্য পর্যালোচনা (Literature Review)</li><li>✅ গবেষণা পদ্ধতি নির্ধারণ</li><li>✅ প্রশ্নমালা প্রস্তুতি</li><li>✅ ডেটা সংগ্রহ (৮০% সম্পন্ন)</li><li>🔄 ডেটা বিশ্লেষণ (চলমান)</li></ul><p><strong>অবশিষ্ট কাজ:</strong></p><ul><li>⏳ ডেটা বিশ্লেষণ সম্পন্ন করা</li><li>⏳ ফলাফল লেখা</li><li>⏳ সুপারভাইজারের সাথে আলোচনা</li><li>⏳ চূড়ান্ত খসড়া প্রস্তুতি</li></ul><p><strong>মূল ফলাফল (প্রাথমিক):</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>বিষয়</th><th>ইতিবাচক</th><th>নেতিবাচক</th><th>নিরপেক্ষ</th></tr><tr><td>অনলাইন ক্লাসের কার্যকারিতা</td><td>৪৫%</td><td>৩৫%</td><td>২০%</td></tr><tr><td>প্রযুক্তিগত সুবিধা</td><td>৩০%</td><td>৫০%</td><td>২০%</td></tr><tr><td>শিক্ষার মান</td><td>৪০%</td><td>৪০%</td><td>২০%</td></tr></table><p><strong>জমা দেওয়ার তারিখ:</strong> ৩১ আগস্ট, ২০২৫</p><p>📊 <strong>ব্যবহৃত সফটওয়্যার:</strong> SPSS, NVivo, Microsoft Excel</p>',
        priority: 'urgent',
        createdAt: new Date(Date.now() - 22 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo30',
        content: '<h2>🍕 রেস্টুরেন্ট ব্যবসা পরিকল্পনা</h2><p><strong>রেস্টুরেন্টের নাম:</strong> "স্বাদের রাজ্য"</p><p><strong>ধরন:</strong> বাংলাদেশি ঐতিহ্যবাহী খাবার</p><p><strong>অবস্থান:</strong> ধানমন্ডি, ঢাকা</p><p><strong>আয়তন:</strong> ২০০০ বর্গফুট, ৫০ আসন</p><p><strong>মেনু ক্যাটাগরি:</strong></p><table border="1" style="width: 100%; border-collapse: collapse; margin: 10px 0;"><tr><th>ক্যাটাগরি</th><th>আইটেম সংখ্যা</th><th>মূল্য পরিসীমা</th></tr><tr><td>🍛 ভাত ও তরকারি</td><td>১৫</td><td>১৫০-৪০০ টাকা</td></tr><tr><td>🍖 মাংসের পদ</td><td>১২</td><td>২৫০-৮০০ টাকা</td></tr><tr><td>🐟 মাছের পদ</td><td>১০</td><td>২০০-৬০০ টাকা</td></tr><tr><td>🥘 বিরিয়ানি</td><td>৫</td><td>৩০০-৭০০ টাকা</td></tr><tr><td>🍰 মিষ্টি</td><td>৮</td><td>৫০-২০০ টাকা</td></tr></table><p><strong>প্রাথমিক বিনিয়োগ:</strong></p><ul><li>🏠 দোকান ভাড়া (৬ মাস): ৩,০০,০০০ টাকা</li><li>🍽️ রান্নাঘর সরঞ্জাম: ৫,০০,০০০ টাকা</li><li>🪑 ফার্নিচার: ২,০০,০০০ টাকা</li><li>🎨 ইন্টেরিয়র ডিজাইন: ১,৫০,০০০ টাকা</li><li>📄 লাইসেন্স ও অন্যান্য: ৫০,০০০ টাকা</li><li><strong>মোট: ১২,০০,০০০ টাকা</strong></li></ul><p><strong>মাসিক আয়-ব্যয়:</strong></p><ul><li>💰 প্রত্যাশিত আয়: ৮,০০,০০০ টাকা</li><li>💸 খরচ: ৬,০০,০০০ টাকা</li><li>📈 মুনাফা: ২,০০,০০০ টাকা (২৫%)</li></ul><p><strong>বিশেষত্ব:</strong></p><ol><li>🔥 লাইভ কুকিং স্টেশন</li><li>🎵 সাংস্কৃতিক অনুষ্ঠান (শুক্রবার)</li><li>🚚 হোম ডেলিভারি সেবা</li><li>👨‍🍳 বিখ্যাত শেফের রেসিপি</li></ol><img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop" alt="রেস্টুরেন্ট" style="width: 100%; max-width: 400px; border-radius: 8px; margin: 10px 0;"><p>🎯 <strong>লক্ষ্য:</strong> ২ বছরে ঢাকার টপ ১০ বাংলাদেশি রেস্টুরেন্টের মধ্যে স্থান</p>',
        priority: 'high',
        createdAt: new Date(Date.now() - 23 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 19 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
        id: 'demo6',
        content: '<h2>📱 নতুন অ্যাপ আইডিয়া</h2><p>একটি দুর্দান্ত অ্যাপ আইডিয়া মাথায় এসেছে! 💡</p><p><strong>অ্যাপের নাম:</strong> "স্মার্ট নোট"</p><p><strong>ফিচারসমূহ:</strong></p><ul><li>ভয়েস টু টেক্সট</li><li>ইমেজ স্ক্যানিং</li><li>ক্লাউড সিঙ্ক</li><li>কোলাবরেশন</li><li>স্মার্ট সার্চ</li></ul><p><strong>টার্গেট অডিয়েন্স:</strong> ছাত্র ও পেশাদাররা</p><p><strong>প্ল্যাটফর্ম:</strong> Android & iOS</p><p>🚀 <em>এই প্রজেক্ট নিয়ে আরও গবেষণা করতে হবে</em></p>',
        priority: 'medium',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    }
];

// ===== EMOJI DATA =====
const emojiData = {
    smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'],
    people: ['👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👩', '🧓', '👴', '👵', '🙍', '🙎', '🙅', '🙆', '💁', '🙋', '🧏', '🙇', '🤦', '🤷', '👮', '🕵', '💂', '👷', '🤴', '👸', '👳', '👲', '🧕', '🤵', '👰', '🤰', '🤱', '👼', '🎅', '🤶', '🦸', '🦹', '🧙', '🧚', '🧛', '🧜', '🧝', '🧞', '🧟', '💆', '💇', '🚶', '🏃', '💃', '🕺', '🕴', '👯', '🧖', '🧗', '🤺', '🏇', '⛷', '🏂', '🏌', '🏄', '🚣', '🏊', '⛹', '🏋', '🚴', '🚵', '🤸', '🤼', '🤽', '🤾', '🤹', '🧘', '🛀', '🛌'],
    nature: ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷', '🕸', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🕊', '🐇', '🦝', '🦨', '🦡', '🦦', '🦥', '🐁', '🐀', '🐿', '🦔'],
    food: ['🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '🥯', '🍞', '🥖', '🥨', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🫓', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🥫', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯'],
    activities: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸', '🥌', '🎿', '⛷', '🏂', '🪂', '🏋', '🤸', '🤺', '🤾', '🏌', '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖', '🏵', '🎗', '🎫', '🎟', '🎪', '🤹', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🥁', '🪘', '🎹', '🎷', '🎺', '🎸', '🪕', '🎻', '🎲', '♟', '🎯', '🎳', '🎮', '🎰', '🧩'],
    travel: ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍', '🛵', '🚲', '🛴', '🛹', '🛼', '🚁', '🛸', '✈', '🛩', '🛫', '🛬', '🪂', '💺', '🚀', '🛰', '🚉', '🚞', '🚝', '🚄', '🚅', '🚈', '🚂', '🚆', '🚇', '🚊', '🚟', '🚠', '🚡', '⛴', '🛥', '🚤', '⛵', '🛶', '🚲', '🏍', '🛵', '🚁', '🚟', '🚠', '🚡', '🛸', '🚀', '🛰', '🌍', '🌎', '🌏', '🌐', '🗺', '🗾', '🧭', '🏔', '⛰', '🌋', '🗻', '🏕', '🏖', '🏜', '🏝', '🏞', '🏟', '🏛', '🏗', '🧱', '🪨', '🪵', '🛖', '🏘', '🏚', '🏠', '🏡', '🏢', '🏣', '🏤', '🏥', '🏦', '🏨', '🏩', '🏪', '🏫', '🏬', '🏭', '🏯', '🏰', '🗼', '🗽', '⛪', '🕌', '🛕', '🕍', '⛩', '🕋'],
    objects: ['⌚', '📱', '📲', '💻', '⌨', '🖥', '🖨', '🖱', '🖲', '🕹', '🗜', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽', '🎞', '📞', '☎', '📟', '📠', '📺', '📻', '🎙', '🎚', '🎛', '🧭', '⏱', '⏲', '⏰', '🕰', '⌛', '⏳', '📡', '🔋', '🔌', '💡', '🔦', '🕯', '🪔', '🧯', '🛢', '💸', '💵', '💴', '💶', '💷', '🪙', '💰', '💳', '💎', '⚖', '🪜', '🧰', '🔧', '🔨', '⚒', '🛠', '⛏', '🪓', '🪚', '🔩', '⚙', '🪤', '🧱', '⛓', '🧲', '🔫', '💣', '🧨', '🪓', '🔪', '🗡', '⚔', '🛡', '🚬', '⚰', '🪦', '⚱', '🏺', '🔮', '📿', '🧿', '💈', '⚗', '🔭', '🔬', '🕳', '🩹', '🩺', '💊', '💉', '🩸', '🧬', '🦠', '🧫', '🧪', '🌡', '🧹', '🪠', '🧽', '🧴', '🛎', '🔑', '🗝', '🚪', '🪑', '🛋', '🛏', '🛌', '🧸', '🪆', '🖼', '🪞', '🪟', '🛍', '🛒', '🎁', '🎈', '🎏', '🎀', '🪄', '🪅', '🎊', '🎉', '🪩', '🎎', '🏮', '🎐', '🧧', '✉', '📩', '📨', '📧', '💌', '📥', '📤', '📦', '🏷', '🪧', '📪', '📫', '📬', '📭', '📮', '📯', '📜', '📃', '📄', '📑', '🧾', '📊', '📈', '📉', '🗒', '🗓', '📆', '📅', '🗑', '📇', '🗃', '🗳', '🗄', '📋', '📁', '📂', '🗂', '🗞', '📰', '📓', '📔', '📒', '📕', '📗', '📘', '📙', '📚', '📖', '🔖', '🧷', '🔗', '📎', '🖇', '📐', '📏', '🧮', '📌', '📍', '✂', '🖊', '🖋', '✒', '🖌', '🖍', '📝', '✏', '🔍', '🔎', '🔏', '🔐', '🔒', '🔓'],
    symbols: ['❤', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮', '✝', '☪', '🕉', '☸', '✡', '🔯', '🕎', '☯', '☦', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛', '🉑', '☢', '☣', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷', '✴', '🆚', '💮', '🉐', '㊙', '㊗', '🈴', '🈵', '🈹', '🈲', '🅰', '🅱', '🆎', '🆑', '🅾', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼', '⁉', '🔅', '🔆', '〽', '⚠', '🚸', '🔱', '⚜', '🔰', '♻', '✅', '🈯', '💹', '❇', '✳', '❎', '🌐', '💠', 'Ⓜ', '🌀', '💤', '🏧', '🚾', '♿', '🅿', '🛗', '🈳', '🈂', '🛂', '🛃', '🛄', '🛅', '🚹', '🚺', '🚼', '⚧', '🚻', '🚮', '🎦', '📶', '🈁', '🔣', 'ℹ', '🔤', '🔡', '🔠', '🆖', '🆗', '🆙', '🆒', '🆕', '🆓', '0⃣', '1⃣', '2⃣', '3⃣', '4⃣', '5⃣', '6⃣', '7⃣', '8⃣', '9⃣', '🔟']
};

// ===== DOM ELEMENTS =====
const notesGrid = document.getElementById('notesGrid');
const emptyState = document.getElementById('emptyState');
const noteModal = document.getElementById('noteModal');
const searchModal = document.getElementById('searchModal');
const emojiModal = document.getElementById('emojiModal');
const addNoteBtn = document.getElementById('addNoteBtn');
const loadSampleBtn = document.getElementById('loadSampleBtn');
const clearSampleBtn = document.getElementById('clearSampleBtn');
const backupBtn = document.getElementById('backupBtn');
const restoreBtn = document.getElementById('restoreBtn');
const searchBtn = document.getElementById('searchBtn');
const saveNoteBtn = document.getElementById('saveNoteBtn');
const deleteNoteBtn = document.getElementById('deleteNoteBtn');
const printCurrentNoteBtn = document.getElementById('printCurrentNoteBtn');
const pdfCurrentNoteBtn = document.getElementById('pdfCurrentNoteBtn');
const insertDateBtn = document.getElementById('insertDateBtn');
const insertTimeBtn = document.getElementById('insertTimeBtn');
const insertDateTimeBtn = document.getElementById('insertDateTimeBtn');
const insertEmojiBtn = document.getElementById('insertEmojiBtn');
const fullscreenBtn = document.getElementById('fullscreenBtn');
const autoSaveBtn = document.getElementById('autoSaveBtn');
const searchInput = document.getElementById('searchInput');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const searchFullscreenBtn = document.getElementById('searchFullscreenBtn');
const searchResults = document.getElementById('searchResults');
const emojiGrid = document.getElementById('emojiGrid');
const modalTitle = document.getElementById('modalTitle');
const notePriority = document.getElementById('notePriority');
const notificationContainer = document.getElementById('notificationContainer');
const notificationBell = document.getElementById('notificationBell');
const notificationBadge = document.getElementById('notificationBadge');
const notificationPanel = document.getElementById('notificationPanel');
const notificationPanelBody = document.getElementById('notificationPanelBody');
const clearAllNotifications = document.getElementById('clearAllNotifications');
const restoreFileInput = document.getElementById('restoreFileInput');

// Password Protection Elements
const passwordModal = document.getElementById('passwordModal');
const enablePasswordCheckbox = document.getElementById('enablePassword');
const passwordInputGroup = document.getElementById('passwordInputGroup');
const notePasswordInput = document.getElementById('notePassword');
const confirmPasswordInput = document.getElementById('confirmPassword');
const togglePasswordBtn = document.getElementById('togglePasswordBtn');
const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPasswordBtn');
const passwordStrengthFill = document.getElementById('strengthFill');
const passwordStrengthText = document.getElementById('strengthText');
const unlockPasswordInput = document.getElementById('unlockPassword');
const toggleUnlockPasswordBtn = document.getElementById('toggleUnlockPasswordBtn');
const unlockNoteBtn = document.getElementById('unlockNoteBtn');
const passwordError = document.getElementById('passwordError');
const passwordAttempts = document.getElementById('passwordAttempts');
const attemptsLeft = document.getElementById('attemptsLeft');
const lockedNoteTitle = document.getElementById('lockedNoteTitle');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Check Font Awesome loading
    setTimeout(checkFontAwesome, 100);

    // Check and fix social icons
    setTimeout(checkSocialIcons, 2000);

    // Start the loader simulation
    simulateLoading();

    loadNotes();
    loadNotifications();
    loadStoragePath(); // Load storage path settings
    suppressPassiveEventWarnings(); // Suppress console warnings
    setupPassiveEventListeners(); // Setup passive listeners before Jodit
    initializeJoditEditor();
    setupEventListeners();
    setupContextMenu(); // Initialize context menu
    loadEmojis('smileys');

    // Show helpful notifications after a delay
    setTimeout(() => {
        showNotification('💡 টিপস: এডিটরে টেক্সট সিলেক্ট করে রাইট ক্লিক করুন - অটোমেটিক কনটেক্সট মেনু দেখা যাবে!', 'info', 5000);
    }, 3000);

    // Initialize notification manager after a short delay
    setTimeout(() => {
        initializeNotificationManager();
    }, 2000);

    setTimeout(() => {
        showNotification('⌨️ এডিটর উন্নতি: এখন আরও স্বাভাবিক টাইপিং অভিজ্ঞতা পাবেন!', 'success', 4000);
    }, 8000);
    loadSortOption(); // Load saved sort option
    updateNotesDisplay();
    updateNotificationBadge();

    // Initialize background customization
    initializeBackgroundCustomization();

    // Initialize notification sounds
    initializeNotificationSounds();

    // Clear any external errors and add error handler
    setTimeout(() => {
        console.clear();
        console.log('ASN Note App loaded successfully! 🎉');

        // Add comprehensive error handler for template literals
        window.addEventListener('error', function(e) {
            if (e.message && (
                e.message.includes('showDetailsData') ||
                e.message.includes('strMealThumb') ||
                e.filename && e.filename.includes('%7B')
            )) {
                e.preventDefault();
                console.warn('Blocked external template literal error:', e.message);
                return false;
            }
        });

        // Block only specific malicious XHR requests
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (typeof url === 'string' && (
                url.includes('showDetailsData.strMealThumb') ||
                url.includes('%7BshowDetailsData.strMealThumb%7D')
            )) {
                console.warn('Blocked malicious XHR request:', url);
                return;
            }
            return originalXHROpen.call(this, method, url, ...args);
        };

        // Optimize performance by reducing forced reflows
        optimizePerformance();
    }, 3000);



    // Initialize notification sound
    initializeNotificationSound();

    // Initialize reminders
    setupReminderHandlers();

    // Ensure notification sound is working after a delay
    setTimeout(() => {
        if (!notificationSound || !notificationSound.play) {
            console.log('Reinitializing notification sound...');
            initializeNotificationSound();
        }
    }, 1000);

    // Enable audio context on first user interaction
    let audioContextEnabled = false;
    document.addEventListener('click', function enableAudio() {
        if (!audioContextEnabled) {
            initializeNotificationSound();
            audioContextEnabled = true;
            console.log('Audio context enabled after user interaction');
        }
    }, { once: true });

    // Initialize themes and settings
    initializeThemes();
    loadSettings();

    // Load path display state
    loadPathDisplayState();

    // Initialize vertical tabs after jodit editor is ready
    setTimeout(() => {
        initializeVerticalTabs();
    }, 500);

    // Initialize password protection
    initializePasswordProtection();

    // Clear temporary decrypted data on page unload for security
    window.addEventListener('beforeunload', clearAllTemporaryDecryptedData);

    // Setup reminders for existing notes (without requesting permission)
    notes.forEach(note => {
        if (note.reminder) {
            setReminderForNoteWithoutPermission(note.id, note.reminder);
        }
    });

    // Show welcome notification if no notes exist
    if (notes.length === 0) {
        setTimeout(() => {
            addNotification('স্বাগতম', 'নোট অর্গানাইজারে আপনাকে স্বাগতম! "স্যাম্পল ডাটা" বাটনে ক্লিক করে ডেমো দেখুন।', 'info', 'welcome');
        }, 1000);

        // Show a demo notification after 2 seconds
        setTimeout(() => {
            addNotification('নতুন ফিচার', 'অটো সেভ ফিচার যোগ হয়েছে! এডিটরে ৩০ সেকেন্ড পর স্বয়ংক্রিয়ভাবে সেভ হবে।', 'success', 'feature');
        }, 2500);

        // Create sample notes for testing download functionality
        createSampleNotesForDownload();
    }
});

// ===== WINDOW LOAD EVENT =====
window.addEventListener('load', function() {
    // Ensure loader is hidden when everything is fully loaded
    setTimeout(() => {
        hideLoader();
    }, 1000); // Give a little extra time for smooth experience
});

// ===== CONSOLE WARNING SUPPRESSION =====
function suppressPassiveEventWarnings() {
    // Store original console.warn
    const originalWarn = console.warn;

    // Override console.warn to filter out specific warnings
    console.warn = function(...args) {
        const message = args.join(' ');

        // Check if this is a passive event listener warning
        if (message.includes('Added non-passive event listener') &&
            (message.includes('mousewheel') || message.includes('touchstart') || message.includes('touchmove'))) {
            // Suppress these specific warnings
            return;
        }

        // Check if this is a forced reflow warning
        if (message.includes('Forced reflow while executing JavaScript')) {
            // Suppress forced reflow warnings
            return;
        }

        // Check if this is a notification permission warning
        if (message.includes('Only request notification permission in response to a user gesture')) {
            // Suppress notification permission warnings
            return;
        }

        // Check if this is a violation warning we want to suppress
        if (message.includes('[Violation]') &&
            (message.includes('Only request notification permission') ||
             message.includes('Forced reflow while executing JavaScript'))) {
            // Suppress these violation warnings
            return;
        }

        // Allow all other warnings through
        originalWarn.apply(console, args);
    };
}

// ===== PASSIVE EVENT LISTENER OVERRIDE =====
function setupPassiveEventListeners() {
    // Override addEventListener to make scroll-blocking events passive by default
    const originalAddEventListener = EventTarget.prototype.addEventListener;

    EventTarget.prototype.addEventListener = function(type, listener, options) {
        // List of events that should be passive to prevent warnings
        const passiveEvents = ['mousewheel', 'wheel', 'touchstart', 'touchmove', 'touchend'];

        if (passiveEvents.includes(type)) {
            // If options is a boolean (for capture), convert to object
            if (typeof options === 'boolean') {
                options = { capture: options, passive: true };
            } else if (typeof options === 'object' && options !== null) {
                // If options is an object, add passive: true if not explicitly set
                if (options.passive === undefined) {
                    options = { ...options, passive: true };
                }
            } else {
                // If no options provided, set passive: true
                options = { passive: true };
            }
        }

        return originalAddEventListener.call(this, type, listener, options);
    };
}

// ===== JODIT EDITOR FONT STYLING =====
function forceUnifiedFontStyling(editor) {
    try {
        if (!editor) return;

        let wysiwyg = null;

        // Try different ways to get the editor element
        if (editor.editor) {
            wysiwyg = editor.editor;
        } else if (editor.container && editor.container.querySelector) {
            wysiwyg = editor.container.querySelector('.jodit-wysiwyg');
        } else if (editor.querySelector) {
            wysiwyg = editor.querySelector('.jodit-wysiwyg');
        } else {
            // Fallback: try to find the editor in DOM
            wysiwyg = document.querySelector('.jodit-wysiwyg');
        }

        if (!wysiwyg) return;

        // Apply unified font styling to the editor
        wysiwyg.style.fontFamily = 'Kalpurush, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
        wysiwyg.style.fontSize = '16px';
        wysiwyg.style.lineHeight = '1.6';

        // Apply to all existing content
        const allElements = wysiwyg.querySelectorAll('*');
        allElements.forEach(element => {
            element.style.fontFamily = 'Kalpurush, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif';
            element.style.fontSize = '16px';
            element.style.lineHeight = '1.6';
        });

        // Set default styles for new content
        if (editor.editorDocument && editor.editorDocument.body) {
            const style = editor.editorDocument.createElement('style');
            style.textContent = `
                body, * {
                    font-family: Kalpurush, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
                    font-size: 16px !important;
                    line-height: 1.6 !important;
                }
                h1 { font-size: 24px !important; line-height: 1.4 !important; }
                h2 { font-size: 22px !important; line-height: 1.4 !important; }
                h3 { font-size: 20px !important; line-height: 1.4 !important; }
                h4 { font-size: 18px !important; line-height: 1.5 !important; }
                h5 { font-size: 16px !important; line-height: 1.5 !important; }
                h6 { font-size: 14px !important; line-height: 1.5 !important; }
            `;
            editor.editorDocument.head.appendChild(style);
        }

        console.log('Unified font styling applied to Jodit editor');
    } catch (error) {
        console.warn('Error applying unified font styling:', error);
    }
}

// ===== JODIT EDITOR INITIALIZATION =====
function initializeJoditEditor() {
    const config = {
        height: 400,
        language: 'bn',
        toolbarSticky: false,
        showCharsCounter: false,
        showWordsCounter: false,
        showXPathInStatusbar: false,
        askBeforePasteHTML: false,
        askBeforePasteFromWord: false,
        defaultActionOnPaste: 'insert_clear_html',

        // Simple and reliable behavior - let Jodit handle most things
        enter: 'P', // Use P tags for new lines
        useSplitMode: false,

        // Minimal clean HTML settings
        cleanHTML: {
            removeEmptyElements: false,
            replaceNBSP: false
        },

        // Configure event handling to reduce passive listener warnings
        useNativeTooltip: true,
        disablePlugins: ['mobile'],
        buttons: [
            'bold', 'italic', 'underline', 'strikethrough', '|',
            'ul', 'ol', '|',
            'font', 'fontsize', '|',
            'brush', 'paragraph', '|',
            'image', 'link', '|',
            'align', '|',
            'undo', 'redo', '|',
            'customHr', 'table', '|',
            'symbols', 'emoji', '|',
            'fullsize'
        ],
        uploader: {
            insertImageAsBase64URI: true,
            url: '', // Disable external upload URL
            isSuccess: function() { return true; },
            getMessage: function() { return ''; }
        },

        // Disable external content loading
        iframe: false,
        iframeStyle: '',
        iframeCSSLinks: [],

        // Enable Tab key for indentation
        useTabForIndenting: true,
        tabIndex: 0,

        // Custom hotkeys for Tab functionality
        commandToHotkeys: {
            'insertTab': ['tab'],
            'removeTab': ['shift+tab']
        },
        style: {
            font: 'Kalpurush, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
            fontSize: '16px',
            lineHeight: '1.6'
        },
        events: {
            afterInit: function(editor) {
                // Use requestAnimationFrame to prevent forced reflow
                requestAnimationFrame(() => {
                    // Batch DOM operations to minimize reflows
                    const operations = [];

                    // Check fullscreen state
                    if (noteModal.classList.contains('fullscreen')) {
                        operations.push(() => resizeEditorToFullscreen());
                    }

                    // Apply current theme
                    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                    operations.push(() => updateJoditEditorTheme(currentTheme));

                    // Force unified font styling
                    operations.push(() => forceUnifiedFontStyling(editor));

                    // Execute all operations in next frame
                    requestAnimationFrame(() => {
                        operations.forEach(op => {
                            try {
                                op();
                            } catch (error) {
                                console.warn('Error executing editor operation:', error);
                            }
                        });
                    });
                });

                // Setup functionality without DOM manipulation
                setupTabKeyHandling(editor);
                registerTabCommands(editor);
                setupMinimalKeyboardFixes(editor);
            },
            change: function(newValue) {
                // Handle content changes for auto-save
                handleContentChange(newValue);

                // Apply unified font styling to new content
                setTimeout(() => {
                    try {
                        forceUnifiedFontStyling(this);
                    } catch (error) {
                        console.warn('Error applying font styling on content change:', error);
                    }
                }, 100);
            }
        },
        extraButtons: [
            {
                name: 'customHr',
                iconURL: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGxpbmUgeDE9IjMiIHkxPSIxMiIgeDI9IjIxIiB5Mj0iMTIiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+',
                tooltip: 'হরাইজন্টাল লাইন যুক্ত করুন',
                exec: function (editor) {
                    insertHorizontalLineAtCursor(editor);
                }
            },
            {
                name: 'symbols',
                iconURL: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+',
                tooltip: 'সিমবল যুক্ত করুন',
                exec: function (editor) {
                    insertSymbols(editor);
                }
            },
            {
                name: 'emoji',
                iconURL: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiLz4KPHBhdGggZD0ibTkgOSAzIDMgMyAtMyIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Im05IDE1IDMgLTMgMyAzIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+',
                tooltip: 'ইমোজি যুক্ত করুন',
                exec: function () {
                    openEmojiModal();
                }
            }
        ]
    };

    try {
        // Only block specific malicious template literal patterns
        const originalFetch = window.fetch;
        window.fetch = function(url, ...args) {
            // Only block very specific malicious patterns
            if (typeof url === 'string' && (
                url.includes('showDetailsData.strMealThumb') ||
                url.includes('%7BshowDetailsData.strMealThumb%7D')
            )) {
                console.warn('Blocked malicious template literal URL request:', url);
                return Promise.reject(new Error('Blocked malicious URL'));
            }
            return originalFetch.call(this, url, ...args);
        };



        joditEditor = Jodit.make('#noteEditor', config);

        // Restore original fetch after initialization
        setTimeout(() => {
            window.fetch = originalFetch;
        }, 5000);

    } catch (error) {
        console.error('Jodit Editor initialization error:', error);
        // Fallback to simple textarea if Jodit fails
        const noteEditor = document.getElementById('noteEditor');
        if (noteEditor) {
            noteEditor.style.minHeight = '400px';
            noteEditor.style.border = '1px solid #ccc';
            noteEditor.style.padding = '10px';
        }
    }

    // Set up iframe content monitoring for theme changes
    setupJoditIframeMonitoring();
}

// ===== MINIMAL KEYBOARD FIXES =====
function setupMinimalKeyboardFixes(editor) {
    // Only fix the most essential issues without interfering with Jodit's behavior

    // Ensure editor always has content to prevent issues
    editor.events.on('afterSetMode', function() {
        setTimeout(() => {
            const editorBody = editor.editor;
            if (editorBody && editorBody.innerHTML.trim() === '') {
                editorBody.innerHTML = '<p><br></p>';
            }
        }, 100);
    });
}

// Removed complex keyboard handling functions that were causing issues
// Now using minimal fixes only

// ===== TAB KEY HANDLING =====
function setupTabKeyHandling(editor) {
    // Handle Tab key in the editor
    editor.events.on('keydown', function(event) {
        if (event.key === 'Tab') {
            event.preventDefault();
            event.stopPropagation();

            const selection = editor.selection;

            if (!selection) return;

            // Check if Shift+Tab (outdent) or just Tab (indent)
            if (event.shiftKey) {
                // Shift+Tab: Remove indentation
                handleOutdent(editor, selection);
            } else {
                // Tab: Add indentation
                handleIndent(editor, selection);
            }

            return false;
        }
    });

    // Also handle Tab key in iframe if editor uses iframe mode
    setTimeout(() => {
        const iframe = editor.container.querySelector('iframe');
        if (iframe && iframe.contentDocument) {
            iframe.contentDocument.addEventListener('keydown', function(event) {
                if (event.key === 'Tab') {
                    event.preventDefault();
                    event.stopPropagation();

                    const selection = editor.selection;
                    if (!selection) return;

                    if (event.shiftKey) {
                        handleOutdent(editor, selection);
                    } else {
                        handleIndent(editor, selection);
                    }

                    return false;
                }
            });
        }
    }, 500);
}

// ===== REGISTER TAB COMMANDS =====
function registerTabCommands(editor) {
    // Register insertTab command
    editor.registerCommand('insertTab', {
        exec: function() {
            const selection = editor.selection;
            if (selection) {
                // Insert 4 spaces as tab
                selection.insertHTML('&nbsp;&nbsp;&nbsp;&nbsp;');
            }
            return false;
        }
    });

    // Register removeTab command
    editor.registerCommand('removeTab', {
        exec: function() {
            const selection = editor.selection;
            const range = selection.range;

            if (!range) return false;

            try {
                const startContainer = range.startContainer;

                if (startContainer.nodeType === Node.TEXT_NODE) {
                    const textContent = startContainer.textContent;
                    const cursorPosition = range.startOffset;

                    // Look for spaces before cursor (up to 4 spaces)
                    let spacesToRemove = 0;
                    for (let i = cursorPosition - 1; i >= 0 && spacesToRemove < 4; i--) {
                        if (textContent[i] === ' ' || textContent[i] === '\u00A0') { // Regular space or &nbsp;
                            spacesToRemove++;
                        } else {
                            break;
                        }
                    }

                    if (spacesToRemove > 0) {
                        // Remove the spaces
                        const newRange = editor.editorDocument.createRange();
                        newRange.setStart(startContainer, cursorPosition - spacesToRemove);
                        newRange.setEnd(startContainer, cursorPosition);
                        newRange.deleteContents();
                    }
                }
            } catch (error) {
                console.error('Error removing tab spaces:', error);
            }

            return false;
        }
    });
}

function handleIndent(editor, selection) {
    try {
        const range = selection.range;
        if (!range) {
            insertTabSpaces(editor, selection);
            return;
        }

        // Get the current element
        const currentElement = range.startContainer;
        const parentElement = currentElement.nodeType === Node.TEXT_NODE ?
            currentElement.parentElement : currentElement;

        // Check if we're in a list
        if (isInList(parentElement)) {
            // Handle list indentation
            handleListIndent(editor, selection);
        } else {
            // Handle regular text indentation
            insertTabSpaces(editor, selection);
        }
    } catch (error) {
        console.error('Error handling indent:', error);
        // Fallback: insert tab spaces
        insertTabSpaces(editor, selection);
    }
}

function handleOutdent(editor, selection) {
    try {
        const range = selection.range;
        if (!range) return;

        const currentElement = range.startContainer;
        const parentElement = currentElement.nodeType === Node.TEXT_NODE ?
            currentElement.parentElement : currentElement;

        // Check if we're in a list
        if (isInList(parentElement)) {
            // Handle list outdentation
            handleListOutdent(editor, selection);
        } else {
            // Handle regular text outdentation
            removeTabSpaces(editor, selection, range);
        }
    } catch (error) {
        console.error('Error handling outdent:', error);
    }
}

function insertTabSpaces(editor, selection) {
    // Insert 4 spaces as tab
    const tabSpaces = '&nbsp;&nbsp;&nbsp;&nbsp;';
    selection.insertHTML(tabSpaces);
}

function removeTabSpaces(editor, selection, range) {
    try {
        const startContainer = range.startContainer;

        if (startContainer.nodeType === Node.TEXT_NODE) {
            const textContent = startContainer.textContent;
            const cursorPosition = range.startOffset;

            // Look for spaces before cursor
            let spacesToRemove = 0;
            for (let i = cursorPosition - 1; i >= 0 && spacesToRemove < 4; i--) {
                if (textContent[i] === ' ' || textContent[i] === '\u00A0') { // Regular space or &nbsp;
                    spacesToRemove++;
                } else {
                    break;
                }
            }

            if (spacesToRemove > 0) {
                // Remove the spaces
                const newRange = editor.editorDocument.createRange();
                newRange.setStart(startContainer, cursorPosition - spacesToRemove);
                newRange.setEnd(startContainer, cursorPosition);
                newRange.deleteContents();
            }
        }
    } catch (error) {
        console.error('Error removing tab spaces:', error);
    }
}

function isInList(element) {
    // Check if current element is inside a list (ul, ol, li)
    while (element && element !== document.body) {
        if (element.tagName === 'LI' || element.tagName === 'UL' || element.tagName === 'OL') {
            return true;
        }
        element = element.parentElement;
    }
    return false;
}

function handleListIndent(editor, selection) {
    try {
        // Use Jodit's built-in indent command for lists
        editor.execCommand('indent');
    } catch (error) {
        console.error('Error indenting list:', error);
        // Fallback to inserting spaces
        insertTabSpaces(editor, selection);
    }
}

function handleListOutdent(editor, selection) {
    try {
        // Use Jodit's built-in outdent command for lists
        editor.execCommand('outdent');
    } catch (error) {
        console.error('Error outdenting list:', error);
    }
}

function setupJoditIframeMonitoring() {
    if (!joditEditor) return;

    // Monitor for iframe creation and content changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName === 'IFRAME') {
                        // Wait for iframe to load and apply theme
                        node.addEventListener('load', () => {
                            setTimeout(() => {
                                const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                                updateJoditEditorTheme(currentTheme);
                            }, 100);
                        });
                    }
                });
            }
        });
    });

    // Start observing the editor container
    if (joditEditor.container) {
        observer.observe(joditEditor.container, {
            childList: true,
            subtree: true
        });
    }

    // Also check for existing iframe
    setTimeout(() => {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        updateJoditEditorTheme(currentTheme);
    }, 500);
}

// ===== EVENT LISTENERS =====
function setupEventListeners() {
    // Header buttons
    addNoteBtn.addEventListener('click', createNewNote);
    loadSampleBtn.addEventListener('click', loadSampleData);
    clearSampleBtn.addEventListener('click', clearSampleData);
    backupBtn.addEventListener('click', createBackup);
    restoreBtn.addEventListener('click', showRestoreModal);

    // Storage Path Management
    const updateStoragePathBtn = document.getElementById('updateStoragePathBtn');
    if (updateStoragePathBtn) {
        updateStoragePathBtn.addEventListener('click', updateStoragePath);
    }

    const copyCurrentPathBtn = document.getElementById('copyCurrentPathBtn');
    if (copyCurrentPathBtn) {
        copyCurrentPathBtn.addEventListener('click', copyCurrentStoragePath);
    }

    const storagePathInput = document.getElementById('storagePathInput');
    if (storagePathInput) {
        storagePathInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                updateStoragePath();
            }
        });
    }

    // Test reminder button
    const testReminderBtn = document.getElementById('testReminderBtn');
    if (testReminderBtn) {
        testReminderBtn.addEventListener('click', testReminderSystem);
    }

    // Bulk download button
    const bulkDownloadBtn = document.getElementById('bulkDownloadBtn');
    if (bulkDownloadBtn) {
        bulkDownloadBtn.addEventListener('click', bulkDownloadNotes);
    }
    searchBtn.addEventListener('click', openSearchModal);

    // Advanced search functionality
    setupAdvancedSearchListeners();

    // File attachment functionality
    setupFileAttachmentListeners();
    setupFilePreviewListeners();



    // Sharing functionality
    setupSharingListeners();

    // Sort functionality
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
    }

    // Dashboard button
    const dashboardBtn = document.getElementById('dashboardBtn');
    if (dashboardBtn) {
        dashboardBtn.addEventListener('click', openDashboardModal);
    }



    // Calendar view button
    const calendarViewBtn = document.getElementById('calendarViewBtn');
    if (calendarViewBtn) {
        calendarViewBtn.addEventListener('click', openCalendarModal);
    }



    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettingsModal);
    }

    // Help button
    const helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
        helpBtn.addEventListener('click', openHelpModal);
    }

    // Drag mode toggle button
    const toggleDragBtn = document.getElementById('toggleDragBtn');
    if (toggleDragBtn) {
        toggleDragBtn.addEventListener('click', toggleDragMode);
    }

    // Reset order button
    const resetOrderBtn = document.getElementById('resetOrderBtn');
    if (resetOrderBtn) {
        resetOrderBtn.addEventListener('click', resetNotesOrder);
    }



    // Dashboard fullscreen button
    const dashboardFullscreenBtn = document.getElementById('dashboardFullscreenBtn');
    if (dashboardFullscreenBtn) {
        dashboardFullscreenBtn.addEventListener('click', toggleDashboardFullscreen);
    }

    // Settings fullscreen button
    const settingsFullscreenBtn = document.getElementById('settingsFullscreenBtn');
    if (settingsFullscreenBtn) {
        settingsFullscreenBtn.addEventListener('click', toggleSettingsFullscreen);
    }



    // Calendar fullscreen button
    const calendarFullscreenBtn = document.getElementById('calendarFullscreenBtn');
    if (calendarFullscreenBtn) {
        calendarFullscreenBtn.addEventListener('click', toggleCalendarFullscreen);
    }

    // Help fullscreen button
    const helpFullscreenBtn = document.getElementById('helpFullscreenBtn');
    if (helpFullscreenBtn) {
        helpFullscreenBtn.addEventListener('click', toggleHelpFullscreen);
    }



    // Modal buttons
    saveNoteBtn.addEventListener('click', () => saveNote(false));
    deleteNoteBtn.addEventListener('click', deleteNote);
    printCurrentNoteBtn.addEventListener('click', () => printNote(currentNoteId));
    pdfCurrentNoteBtn.addEventListener('click', () => downloadNotePDF(currentNoteId));

    // Download TXT button
    const downloadTxtBtn = document.getElementById('downloadTxtBtn');
    if (downloadTxtBtn) {
        downloadTxtBtn.addEventListener('click', downloadNoteAsTxt);
    }
    autoSaveBtn.addEventListener('click', toggleAutoSave);
    insertDateBtn.addEventListener('click', insertCurrentDate);
    insertTimeBtn.addEventListener('click', insertCurrentTime);
    insertDateTimeBtn.addEventListener('click', insertCurrentDateTime);
    insertEmojiBtn.addEventListener('click', openEmojiModal);
    fullscreenBtn.addEventListener('click', toggleFullscreen);

    // Notification bell
    notificationBell.addEventListener('click', toggleNotificationPanel);
    clearAllNotifications.addEventListener('click', clearAllNotificationItems);

    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    clearSearchBtn.addEventListener('click', clearSearch);
    searchFullscreenBtn.addEventListener('click', toggleSearchFullscreen);

    // Filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', handleFilter);
    });

    // Emoji categories
    document.querySelectorAll('.emoji-category').forEach(btn => {
        btn.addEventListener('click', handleEmojiCategory);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Window resize handler for fullscreen mode
    window.addEventListener('resize', handleWindowResize);

    // Advanced options toggle
    const toggleAdvancedBtn = document.getElementById('toggleAdvancedBtn');
    if (toggleAdvancedBtn) {
        toggleAdvancedBtn.addEventListener('click', toggleAdvancedOptions);
    }

    // Scroll buttons
    setupScrollButtons();

    // Scroll spy
    setupScrollSpy();

    // Animated footer
    setupAnimatedFooter();
}

// ===== COMPACT EDITOR FUNCTIONS =====
function toggleAdvancedOptions() {
    const advancedOptions = document.getElementById('advancedOptions');
    const toggleBtn = document.getElementById('toggleAdvancedBtn');

    console.log('toggleAdvancedOptions called');
    console.log('advancedOptions:', advancedOptions);
    console.log('toggleBtn:', toggleBtn);

    if (advancedOptions && toggleBtn) {
        const currentDisplay = advancedOptions.style.display;
        const computedDisplay = window.getComputedStyle(advancedOptions).display;

        console.log('Current display style:', currentDisplay);
        console.log('Computed display style:', computedDisplay);

        // Check if element is currently visible (either computed display is not 'none' or no inline style set)
        const isVisible = computedDisplay !== 'none';

        console.log('isVisible:', isVisible);

        if (isVisible) {
            console.log('Hiding advanced options');
            advancedOptions.style.display = 'none';
            toggleBtn.classList.remove('active');
            toggleBtn.title = 'উন্নত অপশন দেখান';
        } else {
            console.log('Showing advanced options');
            advancedOptions.style.display = 'block';
            toggleBtn.classList.add('active');
            toggleBtn.title = 'উন্নত অপশন লুকান';
        }

        console.log('After toggle - display style:', advancedOptions.style.display);
        console.log('After toggle - computed display:', window.getComputedStyle(advancedOptions).display);
    } else {
        console.error('Missing elements - advancedOptions:', !!advancedOptions, 'toggleBtn:', !!toggleBtn);
    }
}

// ===== SCROLL FUNCTIONALITY =====
function setupScrollButtons() {
    const scrollToTopBtn = document.getElementById('scrollToTop');
    const scrollToBottomBtn = document.getElementById('scrollToBottom');

    if (scrollToTopBtn) {
        scrollToTopBtn.addEventListener('click', scrollToTop);
    }

    if (scrollToBottomBtn) {
        scrollToBottomBtn.addEventListener('click', scrollToBottom);
    }

    // Show/hide scroll buttons based on scroll position
    window.addEventListener('scroll', handleScrollButtonVisibility);
    window.addEventListener('scroll', updateScrollProgress);

    // Initial check
    handleScrollButtonVisibility();
    updateScrollProgress();

    // Add pulse animation to scroll buttons after page load
    setTimeout(() => {
        if (scrollToTopBtn) scrollToTopBtn.classList.add('pulse');
        if (scrollToBottomBtn) scrollToBottomBtn.classList.add('pulse');

        // Remove pulse after animation
        setTimeout(() => {
            if (scrollToTopBtn) scrollToTopBtn.classList.remove('pulse');
            if (scrollToBottomBtn) scrollToBottomBtn.classList.remove('pulse');
        }, 2000);
    }, 3000);
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function scrollToBottom() {
    window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'smooth'
    });
}

function handleScrollButtonVisibility() {
    const scrollToTopBtn = document.getElementById('scrollToTop');
    const scrollToBottomBtn = document.getElementById('scrollToBottom');

    if (!scrollToTopBtn || !scrollToBottomBtn) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    const scrollBottom = scrollTop + windowHeight;

    // Show scroll to top button when scrolled down more than 300px
    if (scrollTop > 300) {
        scrollToTopBtn.classList.add('visible');
    } else {
        scrollToTopBtn.classList.remove('visible');
    }

    // Show scroll to bottom button when not at the bottom (with 100px threshold)
    if (scrollBottom < documentHeight - 100) {
        scrollToBottomBtn.classList.add('visible');
    } else {
        scrollToBottomBtn.classList.remove('visible');
    }

    // Hide both buttons if document is too short to scroll
    if (documentHeight <= windowHeight + 50) {
        scrollToTopBtn.classList.remove('visible');
        scrollToBottomBtn.classList.remove('visible');
    }
}

function updateScrollProgress() {
    const scrollProgress = document.getElementById('scrollProgress');
    if (!scrollProgress) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    const scrollableHeight = documentHeight - windowHeight;

    if (scrollableHeight <= 0) {
        scrollProgress.style.width = '0%';
        return;
    }

    const scrollPercentage = (scrollTop / scrollableHeight) * 100;
    scrollProgress.style.width = Math.min(scrollPercentage, 100) + '%';
}

// Enhanced keyboard shortcuts for scrolling
function handleScrollKeyboardShortcuts(e) {
    // Only handle shortcuts when not in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.contentEditable === 'true') {
        return;
    }

    switch(e.key) {
        case 'Home':
            if (e.ctrlKey) {
                e.preventDefault();
                scrollToTop();
                showNotification('স্ক্রোল', 'পেজের শুরুতে চলে গেছেন', 'info');
            }
            break;
        case 'End':
            if (e.ctrlKey) {
                e.preventDefault();
                scrollToBottom();
                showNotification('স্ক্রোল', 'পেজের শেষে চলে গেছেন', 'info');
            }
            break;
        case 'PageUp':
            e.preventDefault();
            window.scrollBy({
                top: -window.innerHeight * 0.8,
                behavior: 'smooth'
            });
            break;
        case 'PageDown':
            e.preventDefault();
            window.scrollBy({
                top: window.innerHeight * 0.8,
                behavior: 'smooth'
            });
            break;
        case 'ArrowUp':
            if (e.ctrlKey && e.shiftKey) {
                e.preventDefault();
                scrollToTop();
            }
            break;
        case 'ArrowDown':
            if (e.ctrlKey && e.shiftKey) {
                e.preventDefault();
                scrollToBottom();
            }
            break;
    }
}

// Add scroll keyboard shortcuts to existing keyboard handler
document.addEventListener('keydown', handleScrollKeyboardShortcuts);

// ===== SCROLL SPY FUNCTIONALITY =====
function setupScrollSpy() {
    const scrollSpy = document.getElementById('scrollSpy');
    if (!scrollSpy) {
        console.log('Scroll spy element not found');
        return;
    }

    // Force show scroll spy
    scrollSpy.style.display = 'flex';
    scrollSpy.style.opacity = '1';
    scrollSpy.style.visibility = 'visible';

    const spyItems = scrollSpy.querySelectorAll('.scroll-spy-item');
    console.log('Found scroll spy items:', spyItems.length);

    // Add click handlers for scroll spy items
    spyItems.forEach((item, index) => {
        console.log(`Setting up spy item ${index}:`, item.dataset.target);

        item.addEventListener('click', () => {
            const targetId = item.dataset.target;
            const targetElement = document.getElementById(targetId);

            console.log('=== Scroll Spy Click ===');
            console.log('Clicked section:', item.dataset.section);
            console.log('Target ID:', targetId);
            console.log('Element found:', !!targetElement);
            if (targetElement) {
                console.log('Element details:', {
                    tag: targetElement.tagName,
                    class: targetElement.className,
                    id: targetElement.id,
                    offsetTop: targetElement.offsetTop,
                    offsetHeight: targetElement.offsetHeight
                });
            }

            // Add clicked animation
            item.classList.add('clicked');
            setTimeout(() => item.classList.remove('clicked'), 600);

            // Handle different target types
            if (targetId === 'header') {
                // Always scroll to top for header
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                showNotification('নেভিগেশন', `${item.dataset.section} সেকশনে চলে গেছেন`, 'info');
            } else if (targetElement) {
                // Scroll to specific element
                const elementTop = targetElement.offsetTop;
                const headerHeight = 100; // Account for fixed header

                window.scrollTo({
                    top: elementTop - headerHeight,
                    behavior: 'smooth'
                });
                showNotification('নেভিগেশন', `${item.dataset.section} সেকশনে চলে গেছেন`, 'info');
            } else {
                console.error('Target element not found:', targetId);

                // Fallback: try alternative elements
                let fallbackElement = null;
                if (targetId === 'main') {
                    // Try to find notes grid or container
                    fallbackElement = document.getElementById('notesGrid') ||
                                    document.querySelector('.notes-grid') ||
                                    document.querySelector('.main') ||
                                    document.querySelector('main');
                }

                if (fallbackElement) {
                    console.log('Using fallback element:', fallbackElement);
                    const elementTop = fallbackElement.offsetTop;
                    const headerHeight = 100;

                    window.scrollTo({
                        top: elementTop - headerHeight,
                        behavior: 'smooth'
                    });
                    showNotification('নেভিগেশন', `${item.dataset.section} সেকশনে চলে গেছেন`, 'info');
                } else {
                    showNotification('ত্রুটি', 'সেকশন খুঁজে পাওয়া যায়নি', 'error');
                }
            }
        });
    });

    // Update scroll spy on scroll
    window.addEventListener('scroll', updateScrollSpy);
    updateScrollSpy();

    console.log('Scroll spy setup complete');

    // Debug: Test all target elements
    console.log('=== Scroll Spy Debug Info ===');
    spyItems.forEach((item, index) => {
        const targetId = item.dataset.target;
        const targetElement = document.getElementById(targetId);
        console.log(`Spy item ${index}:`);
        console.log(`  - Section: "${item.dataset.section}"`);
        console.log(`  - Target ID: "${targetId}"`);
        console.log(`  - Element found: ${!!targetElement}`);
        if (targetElement) {
            console.log(`  - Element tag: ${targetElement.tagName}`);
            console.log(`  - Element class: ${targetElement.className}`);
            console.log(`  - Element position: top=${targetElement.offsetTop}, height=${targetElement.offsetHeight}`);
        } else {
            console.error(`  - ERROR: Element with ID "${targetId}" not found!`);
        }
        console.log('---');
    });
    console.log('=== End Debug Info ===');
}

function updateScrollSpy() {
    const scrollSpy = document.getElementById('scrollSpy');
    if (!scrollSpy) return;

    const spyItems = scrollSpy.querySelectorAll('.scroll-spy-item');
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    // Always show scroll spy if document is scrollable
    if (documentHeight > windowHeight + 100) {
        scrollSpy.style.opacity = '1';
        scrollSpy.style.visibility = 'visible';
        scrollSpy.classList.add('visible');
    } else {
        scrollSpy.style.opacity = '0.5';
        scrollSpy.style.visibility = 'visible';
        scrollSpy.classList.add('visible');
    }

    // Update active spy item based on scroll position
    spyItems.forEach((item) => {
        const targetId = item.dataset.target;

        // Special handling for different sections
        if (targetId === 'header') {
            // Header is active when at the top of the page
            if (scrollTop < 200) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        } else {
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const elementTop = targetElement.offsetTop;
                const elementHeight = targetElement.offsetHeight;

                // Check if element is in viewport with better detection
                const viewportTop = scrollTop;
                const viewportBottom = scrollTop + windowHeight;
                const elementCenter = elementTop + (elementHeight / 2);

                // Check if element center is in viewport or if we're close to it
                if (elementCenter >= viewportTop - 200 && elementCenter <= viewportBottom + 200) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            }
        }
    });
}

// ===== ANIMATED FOOTER FUNCTIONALITY =====
function setupAnimatedFooter() {
    const footer = document.getElementById('footer');
    const socialLinks = document.querySelectorAll('.social-link');
    const particles = document.querySelectorAll('.particle');

    if (!footer) return;

    // Add click handlers for social links
    socialLinks.forEach((link, index) => {
        link.addEventListener('click', () => {
            // Add click animation
            link.style.transform = 'translateY(-5px) scale(1.2)';
            setTimeout(() => {
                link.style.transform = '';
            }, 300);

            // Show notification based on social link
            const socialNames = ['GitHub', 'LinkedIn', 'Twitter', 'Email'];
            showNotification('সোশ্যাল', `${socialNames[index]} লিংক ক্লিক করা হয়েছে`, 'info');
        });

        // Add hover sound effect (visual feedback)
        link.addEventListener('mouseenter', () => {
            link.style.boxShadow = '0 15px 35px rgba(255, 255, 255, 0.3)';
        });

        link.addEventListener('mouseleave', () => {
            link.style.boxShadow = '';
        });
    });

    // Add intersection observer for footer animation
    const footerObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Trigger footer animations when it comes into view
                footer.classList.add('footer-visible');

                // Animate particles with stagger effect
                particles.forEach((particle, index) => {
                    setTimeout(() => {
                        particle.style.animationPlayState = 'running';
                        particle.style.opacity = '1';
                    }, index * 200);
                });

                // Add typing effect to developer name
                const developerName = document.querySelector('.developer-name');
                if (developerName) {
                    typeWriterEffect(developerName, 'MD Fahim Haque');
                }
            }
        });
    }, {
        threshold: 0.3
    });

    footerObserver.observe(footer);

    // Add parallax effect to footer waves
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const footerWaves = document.querySelectorAll('.footer-wave');

        footerWaves.forEach((wave, index) => {
            const speed = 0.5 + (index * 0.2);
            wave.style.transform = `translateX(-50%) translateY(${scrolled * speed * 0.1}px)`;
        });
    });
}

function typeWriterEffect(element, text) {
    const originalText = element.textContent;
    element.textContent = '';

    let i = 0;
    const typeInterval = setInterval(() => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
        } else {
            clearInterval(typeInterval);

            // Add blinking cursor effect
            const cursor = document.createElement('span');
            cursor.textContent = '|';
            cursor.style.animation = 'blink 1s infinite';
            element.appendChild(cursor);

            // Remove cursor after 3 seconds
            setTimeout(() => {
                if (cursor.parentNode) {
                    cursor.parentNode.removeChild(cursor);
                }
            }, 3000);
        }
    }, 100);
}

// Add CSS for blinking cursor
const style = document.createElement('style');
style.textContent = `
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }

    .footer-visible .footer-main > * {
        animation-play-state: running;
    }
`;
document.head.appendChild(style);

// ===== SORT FUNCTIONALITY =====
function handleSortChange() {
    // Save the selected sort option to localStorage
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        localStorage.setItem('noteSortOption', sortSelect.value);

        // Re-render notes with new sorting
        updateNotesDisplay();

        // Show notification about sort change
        const sortOptions = {
            'priority': 'অগ্রাধিকার ভিত্তিক',
            'updated': 'সর্বশেষ আপডেট',
            'created': 'তৈরির তারিখ (নতুন)',
            'alphabetical': 'বর্ণানুক্রমিক (A-Z)',
            'reverse-alphabetical': 'বিপরীত বর্ণানুক্রমিক (Z-A)',
            'oldest': 'পুরাতন আগে'
        };

        const selectedOption = sortOptions[sortSelect.value] || 'সর্বশেষ আপডেট';
        addNotification('সাজানো পরিবর্তিত', `নোটগুলো এখন "${selectedOption}" অনুযায়ী সাজানো হয়েছে`, 'info', 'sort-change');
    }
}

// Load saved sort option on page load
function loadSortOption() {
    const savedSortOption = localStorage.getItem('noteSortOption') || 'updated';
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.value = savedSortOption;
    }
}

// ===== NOTE MANAGEMENT =====
function createNewNote() {
    currentNoteId = null;
    modalTitle.textContent = 'নতুন নোট';
    deleteNoteBtn.style.display = 'none';
    printCurrentNoteBtn.style.display = 'none';
    pdfCurrentNoteBtn.style.display = 'none';
    document.getElementById('shareNoteBtn').style.display = 'none';
    document.getElementById('downloadTxtBtn').style.display = 'none';
    joditEditor.value = '';
    document.getElementById('noteTitle').value = '';
    notePriority.value = 'medium';
    lastSavedContent = '';
    hasUnsavedChanges = false;

    // Initialize tabs for new note
    editorTabs = [];
    currentActiveTabId = null;
    createNewTab('নতুন ট্যাব', '', true);

    // Clear attachments for new note
    attachedFiles.clear();
    const attachedFilesContainer = document.getElementById('attachedFiles');
    if (attachedFilesContainer) {
        attachedFilesContainer.innerHTML = '';
    }

    // Clear reminder for new note
    const reminderInput = document.getElementById('noteReminder');
    if (reminderInput) {
        reminderInput.value = '';
    }

    // Reset password fields for new note
    resetPasswordFields();

    updateAutoSaveStatus();
    openNoteModal();
}

function editNote(noteId) {
    const note = notes.find(n => n.id === noteId);
    if (!note) return;

    // Check if note is encrypted and needs password
    if (note.encrypted && !note.tempDecryptedContent) {
        openPasswordModal(noteId);
        return;
    }

    currentNoteId = noteId;
    modalTitle.textContent = 'নোট সম্পাদনা';
    deleteNoteBtn.style.display = 'block';
    printCurrentNoteBtn.style.display = 'block';
    pdfCurrentNoteBtn.style.display = 'block';
    document.getElementById('shareNoteBtn').style.display = 'block';
    document.getElementById('downloadTxtBtn').style.display = 'block';

    document.getElementById('noteTitle').value = note.title || '';
    notePriority.value = note.priority || 'medium';

    // Load tabs from note
    loadTabsFromNote(note);

    // Load attachments
    loadNoteAttachments(note);

    // Load reminder
    const reminderInput = document.getElementById('noteReminder');
    if (reminderInput && note.reminder) {
        try {
            // Handle both ISO string and datetime-local format
            let reminderValue = note.reminder;

            // If it's already in datetime-local format, use it directly
            if (typeof reminderValue === 'string' && reminderValue.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
                reminderInput.value = reminderValue;
            } else {
                // Convert from ISO string to datetime-local format
                const reminderDate = new Date(reminderValue);
                if (!isNaN(reminderDate.getTime())) {
                    // Convert to local timezone for datetime-local input
                    const localDateTime = new Date(reminderDate.getTime() - reminderDate.getTimezoneOffset() * 60000);
                    reminderInput.value = localDateTime.toISOString().slice(0, 16);
                } else {
                    reminderInput.value = '';
                }
            }
        } catch (error) {
            console.error('Error loading reminder:', error);
            reminderInput.value = '';
        }
    } else if (reminderInput) {
        reminderInput.value = '';
    }

    lastSavedContent = note.content;
    hasUnsavedChanges = false;
    updateAutoSaveStatus();

    openNoteModal();
}

function saveNote(isAutoSave = false, skipDisplayUpdate = false) {
    // Save current tab content before saving note
    saveCurrentTabContent();

    const title = document.getElementById('noteTitle').value.trim();

    // Get all tabs content
    const tabsData = getAllTabsContent();

    // Check if we have any content in any tab
    const hasContent = tabsData.some(tab => tab.content && tab.content.trim());

    if (!hasContent && !title) {
        if (!isAutoSave) {
            showNotification('ত্রুটি', 'নোট খালি রাখা যাবে না', 'error');
        }
        return false;
    }

    // Check password validation (only for manual saves, not auto-saves)
    if (!isAutoSave) {
        const passwordValidation = isPasswordValid();
        if (!passwordValidation.valid) {
            showNotification('ত্রুটি', passwordValidation.message, 'error');
            return false;
        }
    }

    const now = new Date();
    const priority = notePriority.value;
    const tags = [];
    const reminderInput = document.getElementById('noteReminder');
    let reminder = null;

    if (reminderInput && reminderInput.value) {
        try {
            // Convert datetime-local to ISO string
            const reminderDate = new Date(reminderInput.value);
            if (!isNaN(reminderDate.getTime())) {
                reminder = reminderDate.toISOString();
            }
        } catch (error) {
            console.error('Error processing reminder:', error);
        }
    }



    if (currentNoteId) {
        // Update existing note
        const noteIndex = notes.findIndex(n => n.id === currentNoteId);
        if (noteIndex !== -1) {
            const existingNote = notes[noteIndex];

            // Prepare note data
            const noteData = {
                id: currentNoteId,
                title: title,
                priority: priority,
                tags: tags,
                reminder: reminder,
                tabs: tabsData,
                attachments: Array.from(attachedFiles.values()),
                createdAt: existingNote.createdAt,
                updatedAt: now.toISOString(),
                content: tabsData.length > 0 ? tabsData[0].content : ''
            };

            // Handle password protection
            console.log('Checking password validation for existing note...');
            const passwordValidation = isPasswordValid();
            console.log('Password validation result:', passwordValidation);

            if (passwordValidation.valid && passwordValidation.password) {
                // Encrypt the note
                console.log('Encrypting existing note with password...');
                const encryptedNote = encryptNoteData(noteData, passwordValidation.password);
                notes[noteIndex] = encryptedNote;
            } else if (passwordValidation.valid) {
                // Save as regular note (remove encryption if it was encrypted before)
                console.log('Saving existing note without encryption');
                if (existingNote.encrypted) {
                    console.log('Removing encryption from previously encrypted note');
                    // Remove encryption fields
                    delete noteData.encrypted;
                    delete noteData.encryptedContent;
                    delete noteData.encryptedTabs;
                    delete noteData.passwordHash;
                }
                notes[noteIndex] = noteData;
            } else {
                // Password validation failed, don't save
                console.log('Password validation failed, not saving existing note');
                return false;
            }

            // Update reminder
            if (reminder) {
                setReminderForNote(currentNoteId, reminder);
            } else if (reminderTimers.has(currentNoteId)) {
                clearTimeout(reminderTimers.get(currentNoteId));
                reminderTimers.delete(currentNoteId);
            }

            if (!isAutoSave) {
                showNotification('সফল', 'নোট আপডেট হয়েছে', 'success');
                addNotification('নোট আপডেট', 'একটি নোট সফলভাবে আপডেট হয়েছে', 'success', 'note_updated', currentNoteId);
            }
        }
    } else {
        // Create new note
        const noteData = {
            id: generateId(),
            title: title,
            priority: priority,
            tags: tags,
            reminder: reminder,
            tabs: tabsData,
            attachments: Array.from(attachedFiles.values()),
            createdAt: now.toISOString(),
            updatedAt: now.toISOString(),
            content: tabsData.length > 0 ? tabsData[0].content : ''
        };

        // Handle password protection
        console.log('Checking password validation for new note...');
        const passwordValidation = isPasswordValid();
        console.log('Password validation result:', passwordValidation);

        if (!passwordValidation.valid) {
            // Password validation failed, don't save
            console.log('Password validation failed, not saving note');
            return false;
        }

        let finalNote;
        if (passwordValidation.password) {
            // Encrypt the note
            console.log('Encrypting new note with password...');
            finalNote = encryptNoteData(noteData, passwordValidation.password);
        } else {
            console.log('Saving new note without encryption');
            finalNote = noteData;
        }

        notes.unshift(finalNote);
        currentNoteId = finalNote.id; // Set the current note ID for future auto-saves

        // Set reminder for new note
        if (reminder) {
            setReminderForNote(finalNote.id, reminder);
        }

        // Show print/PDF/Share/Download buttons for new note
        printCurrentNoteBtn.style.display = 'block';
        pdfCurrentNoteBtn.style.display = 'block';
        document.getElementById('shareNoteBtn').style.display = 'block';
        document.getElementById('downloadTxtBtn').style.display = 'block';

        if (!isAutoSave) {
            showNotification('সফল', 'নতুন নোট তৈরি হয়েছে', 'success');
            addNotification('নতুন নোট', 'একটি নতুন নোট সফলভাবে তৈরি হয়েছে', 'success', 'note_created', finalNote.id);
        }
    }

    lastSavedContent = tabsData.length > 0 ? tabsData[0].content : '';
    hasUnsavedChanges = false;
    updateAutoSaveStatus();

    saveNotes();

    // Only update display if not skipping (to prevent modal shaking)
    if (!skipDisplayUpdate) {
        updateNotesDisplay();
    }

    // Update reminder indicators after saving
    setTimeout(() => {
        updateAllNoteThumbnailsWithReminders();
    }, 100);

    // Update dashboard if it's open
    const dashboardModal = document.getElementById('dashboardModal');
    if (dashboardModal && dashboardModal.style.display === 'flex') {
        updateDashboardStats();
        updateRecentActivity();
    }

    if (!isAutoSave) {
        // Reset password fields after successful save
        resetPasswordFields();
        closeNoteModal();
    }

    return true;
}

function deleteNote() {
    if (!currentNoteId) return;

    if (confirm('আপনি কি নিশ্চিত যে এই নোটটি ডিলেট করতে চান?')) {
        notes = notes.filter(n => n.id !== currentNoteId);
        saveNotes();
        updateNotesDisplay();

        // Update dashboard if it's open
        const dashboardModal = document.getElementById('dashboardModal');
        if (dashboardModal && dashboardModal.style.display === 'flex') {
            updateDashboardStats();
            updateRecentActivity();
        }

        closeNoteModal();
        showNotification('সফল', 'নোট ডিলেট হয়েছে', 'success');
        addNotification('নোট ডিলেট', 'একটি নোট ডিলেট করা হয়েছে', 'warning', 'note_deleted', currentNoteId);
    }
}

// ===== LOCAL STORAGE =====
function saveNotes() {
    try {
        const notesData = JSON.stringify(notes);
        localStorage.setItem('notes', notesData);
    } catch (error) {
        if (error.name === 'QuotaExceededError') {
            showNotification('ত্রুটি', 'স্টোরেজ সীমা অতিক্রম হয়েছে। কিছু পুরানো নোট ডিলেট করুন।', 'error');

            // Try to free up space by removing old notes
            if (notes.length > 50) {
                notes.splice(50); // Keep only first 50 notes
                try {
                    localStorage.setItem('notes', JSON.stringify(notes));
                    showNotification('সফল', 'পুরানো নোট ডিলেট করে স্থান তৈরি করা হয়েছে।', 'success');
                } catch (retryError) {
                    showNotification('ত্রুটি', 'স্টোরেজ সমস্যা সমাধান করা যায়নি।', 'error');
                }
            }
        } else {
            showNotification('ত্রুটি', 'নোট সংরক্ষণে সমস্যা হয়েছে।', 'error');
        }
    }
}

function loadNotes() {
    const savedNotes = localStorage.getItem('notes');
    if (savedNotes) {
        notes = JSON.parse(savedNotes);

        // Update reminder indicators after loading notes
        setTimeout(() => {
            updateAllNoteThumbnailsWithReminders();
        }, 500);
    }
}

// ===== DISPLAY FUNCTIONS =====
function updateNotesDisplay() {
    // Skip DOM updates if modal is open to prevent shaking
    const isModalOpen = document.body.classList.contains('modal-open');
    if (isModalOpen) {
        // Schedule update for when modal closes
        scheduleNotesDisplayUpdate();
        return;
    }

    if (notes.length === 0) {
        notesGrid.style.display = 'none';
        emptyState.style.display = 'block';
    } else {
        notesGrid.style.display = 'grid';
        emptyState.style.display = 'none';
        renderNotes(notes);
    }
}

// Schedule notes display update for when modal closes
let pendingNotesUpdate = false;
function scheduleNotesDisplayUpdate() {
    pendingNotesUpdate = true;
}

// Execute pending notes update
function executePendingNotesUpdate() {
    if (pendingNotesUpdate) {
        pendingNotesUpdate = false;
        if (notes.length === 0) {
            notesGrid.style.display = 'none';
            emptyState.style.display = 'block';
        } else {
            notesGrid.style.display = 'grid';
            emptyState.style.display = 'none';
            renderNotes(notes);
        }
    }
}

function renderNotes(notesToRender) {
    notesGrid.innerHTML = '';

    let filteredNotes = notesToRender || notes;

    // Get current sort option
    const sortSelect = document.getElementById('sortSelect');
    const sortOption = sortSelect ? sortSelect.value : 'updated';

    // Sort notes based on selected option
    const sortedNotes = sortNotes([...filteredNotes], sortOption);

    sortedNotes.forEach(note => {
        const noteCard = createNoteCard(note);
        notesGrid.appendChild(noteCard);
    });
}

// New sorting function
function sortNotes(notesToSort, sortOption) {
    const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };

    return notesToSort.sort((a, b) => {
        switch (sortOption) {
            case 'priority':
                // Sort by priority first, then by updated date
                const priorityA = priorityOrder[a.priority || 'medium'];
                const priorityB = priorityOrder[b.priority || 'medium'];
                if (priorityA !== priorityB) {
                    return priorityA - priorityB;
                }
                return new Date(b.updatedAt) - new Date(a.updatedAt);

            case 'updated':
                // Sort by last updated date (newest first)
                return new Date(b.updatedAt) - new Date(a.updatedAt);

            case 'created':
                // Sort by creation date (newest first)
                return new Date(b.createdAt) - new Date(a.createdAt);

            case 'alphabetical':
                // Sort alphabetically by content (A-Z)
                const contentA = getTextPreview(a.content).toLowerCase();
                const contentB = getTextPreview(b.content).toLowerCase();
                return contentA.localeCompare(contentB, 'bn');

            case 'reverse-alphabetical':
                // Sort reverse alphabetically by content (Z-A)
                const contentA2 = getTextPreview(a.content).toLowerCase();
                const contentB2 = getTextPreview(b.content).toLowerCase();
                return contentB2.localeCompare(contentA2, 'bn');

            case 'oldest':
                // Sort by creation date (oldest first)
                return new Date(a.createdAt) - new Date(b.createdAt);

            default:
                // Default to updated date sorting
                return new Date(b.updatedAt) - new Date(a.updatedAt);
        }
    });
}

function createNoteCard(note) {
    const card = document.createElement('div');
    card.className = `note-card slide-up priority-${note.priority || 'medium'} ${note.encrypted ? 'encrypted' : ''}`;
    card.onclick = () => editNote(note.id);

    // Handle encrypted notes
    let preview, noteTitle, externalTitle;
    if (note.encrypted) {
        preview = '🔒 এই নোটটি এনক্রিপ্টেড। দেখতে পাসওয়ার্ড প্রয়োজন।';
        noteTitle = note.title || 'এনক্রিপ্টেড নোট';
        externalTitle = 'Password Protected'; // External title shows "Password Protected"
    } else {
        preview = getTextPreview(note.content);
        noteTitle = note.title || 'শিরোনামহীন নোট';
        externalTitle = noteTitle; // External title shows actual note title
    }

    const fullDateUpdated = formatDate(note.updatedAt);
    const fullDateCreated = formatDate(note.createdAt);
    const detailedDateUpdated = formatDateDetailed(note.updatedAt);
    const detailedDateCreated = formatDateDetailed(note.createdAt);
    const priorityEmoji = getPriorityEmoji(note.priority || 'medium');
    const lastSavedText = formatLastSavedText(note.updatedAt);

    card.innerHTML = `
        <div class="note-external-title">
            <h3 class="external-title-text">${externalTitle}</h3>
            <div class="external-title-decoration">
                <span class="priority-badge priority-${note.priority || 'medium'}">${priorityEmoji}</span>
                ${note.encrypted ? '<i class="fas fa-lock external-lock-icon"></i>' : ''}
            </div>
        </div>
        <div class="note-card-header">
            <div class="note-date-section">
                <div class="note-date created-date" title="তৈরির সময়: ${detailedDateCreated}">
                    <i class="fas fa-plus-circle"></i>
                    <span class="date-label bengali-text">তৈরি:</span>
                    <span class="date-value">${fullDateCreated}</span>
                </div>
                <div class="note-date updated-date" title="সর্বশেষ আপডেট: ${detailedDateUpdated}">
                    <i class="fas fa-edit"></i>
                    <span class="date-label bengali-text">আপডেট:</span>
                    <span class="date-value">${fullDateUpdated}</span>
                </div>
            </div>
        </div>
        <div class="note-content">
            <div class="note-title">${noteTitle}</div>
            <div class="note-preview">${preview}</div>

            ${note.reminder ? `
                <div class="note-reminder">
                    <i class="fas fa-bell"></i>
                    ${formatReminderText(note.reminder)}
                </div>
            ` : ''}
            <div class="note-last-saved">
                <i class="fas fa-save"></i>
                ${lastSavedText}
            </div>
        </div>
        <div class="note-actions-footer">
            <div class="note-actions">
                <button class="btn-action" onclick="event.stopPropagation(); editNote('${note.id}')" title="সম্পাদনা">
                    <i class="fas fa-edit"></i>
                    <span class="bengali-text">সম্পাদনা</span>
                </button>
                <button class="btn-action" onclick="event.stopPropagation(); duplicateNote('${note.id}')" title="ডুপ্লিকেট করুন">
                    <i class="fas fa-copy"></i>
                    <span class="bengali-text">কপি</span>
                </button>
                <button class="btn-action" onclick="event.stopPropagation(); shareNoteFromCard('${note.id}')" title="শেয়ার করুন">
                    <i class="fas fa-share-alt"></i>
                    <span class="bengali-text">শেয়ার</span>
                </button>
                <button class="btn-action" onclick="event.stopPropagation(); printNote('${note.id}')" title="প্রিন্ট">
                    <i class="fas fa-print"></i>
                    <span class="bengali-text">প্রিন্ট</span>
                </button>
                <button class="btn-action" onclick="event.stopPropagation(); downloadNotePDF('${note.id}')" title="PDF ডাউনলোড">
                    <i class="fas fa-file-pdf"></i>
                    <span class="bengali-text">PDF</span>
                </button>
                <button class="btn-action btn-danger" onclick="event.stopPropagation(); deleteNoteConfirm('${note.id}')" title="ডিলেট">
                    <i class="fas fa-trash"></i>
                    <span class="bengali-text">ডিলেট</span>
                </button>
            </div>
        </div>
    `;

    // Add reminder indicator after creating the card
    setTimeout(() => {
        updateNoteThumbnailWithReminder(card, note);
    }, 10);

    return card;
}

function duplicateNote(noteId) {
    const originalNote = notes.find(n => n.id === noteId);
    if (!originalNote) {
        showNotification('ত্রুটি', 'নোট পাওয়া যায়নি', 'error');
        return;
    }

    // Show confirmation dialog
    const confirmMessage = `আপনি কি "${originalNote.title || 'শিরোনামহীন নোট'}" নোটটি ডুপ্লিকেট করতে চান?`;
    if (!confirm(confirmMessage)) {
        return;
    }

    // Check how many copies already exist to create a unique title
    const baseTitle = originalNote.title || 'শিরোনামহীন নোট';
    const existingCopies = notes.filter(note =>
        note.title && note.title.startsWith(baseTitle) && note.title.includes('কপি')
    ).length;

    let newTitle;
    if (existingCopies === 0) {
        newTitle = `${baseTitle} (কপি)`;
    } else {
        newTitle = `${baseTitle} (কপি ${existingCopies + 1})`;
    }

    // Create a duplicate note with new ID and updated timestamps
    const duplicatedNote = {
        ...originalNote,
        id: generateId(),
        title: newTitle,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // If the note has tabs, duplicate them too
    if (duplicatedNote.tabs && duplicatedNote.tabs.length > 0) {
        duplicatedNote.tabs = duplicatedNote.tabs.map(tab => ({
            ...tab,
            id: generateId()
        }));
    }

    // If the note has attachments, duplicate them too
    if (duplicatedNote.attachments && duplicatedNote.attachments.length > 0) {
        duplicatedNote.attachments = duplicatedNote.attachments.map(attachment => ({
            ...attachment,
            id: generateId()
        }));
    }

    // Add the duplicated note to the beginning of the notes array
    notes.unshift(duplicatedNote);

    // Save and update display
    saveNotes();
    updateNotesDisplay();

    // Show success notification with more details
    const successMessage = `"${duplicatedNote.title}" নোটটি সফলভাবে ডুপ্লিকেট হয়েছে`;
    showNotification('সফল', successMessage, 'success');
    addNotification('নোট ডুপ্লিকেট', 'একটি নোট সফলভাবে ডুপ্লিকেট করা হয়েছে', 'success', 'note_duplicated', duplicatedNote.id);

    // Update dashboard if it's open
    const dashboardModal = document.getElementById('dashboardModal');
    if (dashboardModal && dashboardModal.style.display === 'flex') {
        updateDashboardStats();
        updateRecentActivity();
    }

    // Add a visual effect to highlight the new duplicated note
    setTimeout(() => {
        const newNoteCard = document.querySelector(`[onclick*="${duplicatedNote.id}"]`);
        if (newNoteCard) {
            newNoteCard.style.animation = 'pulse 1s ease-in-out';
            newNoteCard.style.border = '2px solid var(--success-color)';

            // Remove the highlight after 3 seconds
            setTimeout(() => {
                newNoteCard.style.animation = '';
                newNoteCard.style.border = '';
            }, 3000);
        }
    }, 100);
}

function deleteNoteConfirm(noteId) {
    if (confirm('আপনি কি নিশ্চিত যে এই নোটটি ডিলেট করতে চান?')) {
        notes = notes.filter(n => n.id !== noteId);
        saveNotes();
        updateNotesDisplay();
        showNotification('সফল', 'নোট ডিলেট হয়েছে', 'success');
        addNotification('নোট ডিলেট', 'একটি নোট ডিলেট করা হয়েছে', 'warning', 'note_deleted', noteId);
    }
}

function shareNoteFromCard(noteId) {
    const note = notes.find(n => n.id === noteId);
    if (!note) return;

    // Set the current note for sharing
    currentSharedNote = note;
    currentNoteId = noteId;

    // Open the share modal
    openShareModal();
}

// ===== MODAL FUNCTIONS =====
function openNoteModal() {
    // Use requestAnimationFrame to prevent forced reflow
    requestAnimationFrame(() => {
        // Batch DOM operations
        noteModal.classList.add('active');
        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';
        updateAutoSaveButton();

        // Focus editor in next frame to avoid layout thrashing
        requestAnimationFrame(() => {
            if (joditEditor && joditEditor.focus) {
                joditEditor.focus();
            }
        });
    });
}

function closeNoteModal() {
    // Clear auto-save timer
    clearAutoSaveTimer();

    // Remove symbols toolbar if exists
    const symbolsToolbar = document.getElementById('symbolsToolbar');
    if (symbolsToolbar) {
        symbolsToolbar.remove();
    }

    // Clear temporary decrypted data for security
    if (currentNoteId) {
        const note = notes.find(n => n.id === currentNoteId);
        if (note && note.encrypted) {
            // Remove temporary decrypted content
            delete note.tempDecryptedContent;
            delete note.tempDecryptedTabs;
        }
    }

    noteModal.classList.remove('active');
    noteModal.classList.remove('fullscreen');
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    currentNoteId = null;

    // Execute any pending notes display updates
    setTimeout(() => {
        executePendingNotesUpdate();
    }, 100);
    lastSavedContent = '';
    hasUnsavedChanges = false;

    // Hide print/PDF/Share buttons
    printCurrentNoteBtn.style.display = 'none';
    pdfCurrentNoteBtn.style.display = 'none';
    document.getElementById('shareNoteBtn').style.display = 'none';

    // Reset fullscreen button
    fullscreenBtn.classList.remove('active');
    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    fullscreenBtn.title = 'ফুল স্ক্রিন';
}

function openSearchModal(tab = 'notes') {
    searchModal.classList.add('active');
    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';

    // Switch to the specified tab
    if (window.bookmarkManager) {
        window.bookmarkManager.switchSearchTab(tab);
    }

    if (tab === 'notes') {
        searchInput.focus();
        performSearch('');
    } else if (tab === 'bookmarks') {
        const bookmarkSearchInput = document.getElementById('bookmarkSearchInput');
        if (bookmarkSearchInput) {
            bookmarkSearchInput.focus();
        }
    }
}

function closeSearchModal() {
    searchModal.classList.remove('active');
    searchModal.classList.remove('fullscreen'); // Reset fullscreen mode
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';

    // Execute any pending notes display updates
    setTimeout(() => {
        executePendingNotesUpdate();
    }, 100);
    searchInput.value = '';
    clearSearchBtn.style.display = 'none';

    // Clear bookmark search input too
    const bookmarkSearchInput = document.getElementById('bookmarkSearchInput');
    if (bookmarkSearchInput) {
        bookmarkSearchInput.value = '';
    }
    const clearBookmarkSearchBtn = document.getElementById('clearBookmarkSearchBtn');
    if (clearBookmarkSearchBtn) {
        clearBookmarkSearchBtn.style.display = 'none';
    }

    // Reset fullscreen button
    searchFullscreenBtn.classList.remove('active');
    searchFullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    searchFullscreenBtn.title = 'ফুল স্ক্রিন';
}

function openEmojiModal() {
    emojiModal.classList.add('active');
}

function closeEmojiModal() {
    emojiModal.classList.remove('active');
}

// ===== FULLSCREEN FUNCTIONALITY =====
function toggleFullscreen() {
    const isFullscreen = noteModal.classList.contains('fullscreen');

    if (isFullscreen) {
        // Exit fullscreen
        noteModal.classList.remove('fullscreen');
        fullscreenBtn.classList.remove('active');
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'ফুল স্ক্রিন';

        // Reset editor to normal size
        setTimeout(() => {
            resizeEditorToNormal();
        }, 100);
    } else {
        // Enter fullscreen
        noteModal.classList.add('fullscreen');
        fullscreenBtn.classList.add('active');
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'ফুল স্ক্রিন বন্ধ';

        // Resize editor to fullscreen
        setTimeout(() => {
            resizeEditorToFullscreen();
        }, 100);
    }
}

// ===== SEARCH FULLSCREEN FUNCTIONALITY =====
function toggleSearchFullscreen() {
    const isFullscreen = searchModal.classList.contains('fullscreen');

    if (isFullscreen) {
        // Exit fullscreen
        searchModal.classList.remove('fullscreen');
        searchFullscreenBtn.classList.remove('active');
        searchFullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        searchFullscreenBtn.title = 'ফুল স্ক্রিন';
    } else {
        // Enter fullscreen
        searchModal.classList.add('fullscreen');
        searchFullscreenBtn.classList.add('active');
        searchFullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        searchFullscreenBtn.title = 'ফুল স্ক্রিন বন্ধ';
    }
}

// ===== EDITOR RESIZE FUNCTIONS =====
function resizeEditorToFullscreen() {
    if (!joditEditor) return;

    // Calculate fullscreen height outside of callbacks
    const fullscreenHeight = window.innerHeight - 300;

    // Use requestAnimationFrame to prevent forced reflow
    requestAnimationFrame(() => {
        // Batch all style changes to minimize reflows
        const container = joditEditor.container;
        const workplace = joditEditor.workplace;
        const wysiwyg = joditEditor.editor;

        // Apply all styles in one batch
        if (container) {
            container.style.height = (fullscreenHeight + 50) + 'px';
        }

        if (workplace) {
            workplace.style.cssText += `
                height: ${fullscreenHeight}px;
                min-height: ${fullscreenHeight}px;
            `;
        }

        if (wysiwyg) {
            wysiwyg.style.cssText += `
                height: ${fullscreenHeight}px;
                min-height: ${fullscreenHeight}px;
                max-height: ${fullscreenHeight}px;
            `;
        }

        // Fire resize event in next frame
        requestAnimationFrame(() => {
            if (joditEditor.events) {
                joditEditor.events.fire('resize');
            }
        });
    });

    // Additional force resize for contenteditable
    // Multiple attempts to ensure resize
    const attempts = [50, 100, 200, 500];
    attempts.forEach(delay => {
        setTimeout(() => {
            const editableArea = joditEditor.container.querySelector('.jodit-wysiwyg');
            if (editableArea) {
                editableArea.style.height = fullscreenHeight + 'px';
                editableArea.style.minHeight = fullscreenHeight + 'px';
                editableArea.style.maxHeight = fullscreenHeight + 'px';
            }

            // Also try to find iframe content
            const iframe = joditEditor.container.querySelector('iframe');
            if (iframe && iframe.contentDocument) {
                const body = iframe.contentDocument.body;
                if (body) {
                    body.style.minHeight = fullscreenHeight + 'px';
                }
            }
        }, delay);
    });
}

function resizeEditorToNormal() {
    if (!joditEditor) return;

    // Reset to normal size
    joditEditor.container.style.height = 'auto';

    if (joditEditor.workplace) {
        joditEditor.workplace.style.height = '400px';
        joditEditor.workplace.style.minHeight = '400px';
    }

    const wysiwyg = joditEditor.editor;
    if (wysiwyg) {
        wysiwyg.style.height = '400px';
        wysiwyg.style.minHeight = '400px';
        wysiwyg.style.maxHeight = 'none';
    }

    joditEditor.events.fire('resize');

    setTimeout(() => {
        const editableArea = joditEditor.container.querySelector('.jodit-wysiwyg');
        if (editableArea) {
            editableArea.style.height = '400px';
            editableArea.style.minHeight = '400px';
            editableArea.style.maxHeight = 'none';
        }
    }, 50);
}

// ===== WINDOW RESIZE HANDLER =====
function handleWindowResize() {
    if (noteModal.classList.contains('fullscreen') && joditEditor) {
        setTimeout(() => {
            resizeEditorToFullscreen();
        }, 100);
    }
}

// ===== ADVANCED SEARCH FUNCTIONALITY =====
let currentSearchMode = 'normal';
let searchStartTime = 0;

function handleSearch() {
    const query = searchInput.value.trim();
    clearSearchBtn.style.display = query ? 'block' : 'none';

    if (query) {
        performAdvancedSearch(query);
    } else {
        clearSearchResults();
    }
}

function clearSearch() {
    searchInput.value = '';
    clearSearchBtn.style.display = 'none';
    clearSearchResults();
}

function clearSearchResults() {
    searchResults.innerHTML = '<p class="text-center text-muted">সার্চ করুন...</p>';
    hideSearchStats();
}

function performAdvancedSearch(query) {
    searchStartTime = performance.now();

    try {
        let filteredNotes = [];
        let filteredBookmarks = [];
        const caseSensitive = document.getElementById('caseSensitiveSearch')?.checked || false;
        const wholeWord = document.getElementById('wholeWordSearch')?.checked || false;

        // Clear any previous regex errors
        clearRegexError();

        // Search in notes
        switch (currentSearchMode) {
            case 'normal':
                filteredNotes = performNormalSearch(query, caseSensitive, wholeWord);
                break;
            case 'regex':
                filteredNotes = performRegexSearch(query, caseSensitive);
                break;
            case 'fulltext':
                filteredNotes = performFullTextSearch(query, caseSensitive, wholeWord);
                break;
        }

        // Search in bookmarks
        if (window.bookmarkManager && window.bookmarkManager.bookmarks) {
            filteredBookmarks = performBookmarkSearch(query, caseSensitive, wholeWord);
        }

        renderCombinedSearchResults(filteredNotes, filteredBookmarks, query);
        showSearchStats(filteredNotes.length + filteredBookmarks.length);

    } catch (error) {
        if (currentSearchMode === 'regex') {
            showRegexError(error.message);
        }
        console.error('Search error:', error);
    }
}

function performNormalSearch(query, caseSensitive, wholeWord) {
    const searchQuery = caseSensitive ? query : query.toLowerCase();

    return notes.filter(note => {
        let content = getTextPreview(note.content);
        let title = note.title || '';

        if (!caseSensitive) {
            content = content.toLowerCase();
            title = title.toLowerCase();
        }

        let matches = false;

        if (wholeWord) {
            const regex = new RegExp(`\\b${escapeRegex(searchQuery)}\\b`, caseSensitive ? 'g' : 'gi');
            matches = regex.test(content) || regex.test(title);
        } else {
            matches = content.includes(searchQuery) || title.includes(searchQuery);
        }



        return matches;
    });
}

function performRegexSearch(query, caseSensitive) {
    const flags = caseSensitive ? 'g' : 'gi';
    const regex = new RegExp(query, flags);

    return notes.filter(note => {
        const content = getTextPreview(note.content);
        const title = note.title || '';

        let matches = regex.test(content) || regex.test(title);



        return matches;
    });
}

function performFullTextSearch(query, caseSensitive, wholeWord) {
    const searchTerms = query.split(/\s+/).filter(term => term.length > 0);

    return notes.filter(note => {
        const content = getTextPreview(note.content);
        const title = note.title || '';
        const searchableText = caseSensitive ? `${title} ${content}` : `${title} ${content}`.toLowerCase();

        let score = 0;

        searchTerms.forEach(term => {
            const searchTerm = caseSensitive ? term : term.toLowerCase();

            if (wholeWord) {
                const regex = new RegExp(`\\b${escapeRegex(searchTerm)}\\b`, caseSensitive ? 'g' : 'gi');
                const matches = searchableText.match(regex);
                if (matches) score += matches.length;
            } else {
                const occurrences = (searchableText.match(new RegExp(escapeRegex(searchTerm), 'g')) || []).length;
                score += occurrences;
            }
        });



        return score > 0;
    }).sort((a, b) => {
        // Sort by relevance score (higher score first)
        const scoreA = calculateRelevanceScore(a, searchTerms, caseSensitive, wholeWord);
        const scoreB = calculateRelevanceScore(b, searchTerms, caseSensitive, wholeWord);
        return scoreB - scoreA;
    });
}

function calculateRelevanceScore(note, searchTerms, caseSensitive, wholeWord) {
    const content = getTextPreview(note.content);
    const title = note.title || '';
    const searchableText = caseSensitive ? `${title} ${content}` : `${title} ${content}`.toLowerCase();

    let score = 0;

    searchTerms.forEach(term => {
        const searchTerm = caseSensitive ? term : term.toLowerCase();

        // Title matches get higher score
        if (title.toLowerCase().includes(searchTerm.toLowerCase())) {
            score += 10;
        }

        // Content matches
        if (wholeWord) {
            const regex = new RegExp(`\\b${escapeRegex(searchTerm)}\\b`, caseSensitive ? 'g' : 'gi');
            const matches = searchableText.match(regex);
            if (matches) score += matches.length;
        } else {
            const occurrences = (searchableText.match(new RegExp(escapeRegex(searchTerm), 'g')) || []).length;
            score += occurrences;
        }
    });

    return score;
}

function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}



function showSearchStats(resultCount) {
    const searchTime = performance.now() - searchStartTime;
    const statsElement = document.getElementById('searchStats');
    const countElement = document.getElementById('searchResultCount');
    const timeElement = document.getElementById('searchTime');

    if (statsElement && countElement && timeElement) {
        countElement.textContent = `${resultCount} টি ফলাফল`;
        timeElement.textContent = `${Math.round(searchTime)}ms এ`;
        statsElement.style.display = 'flex';
    }
}

function hideSearchStats() {
    const statsElement = document.getElementById('searchStats');
    if (statsElement) {
        statsElement.style.display = 'none';
    }
}

function showRegexError(message) {
    const searchContainer = document.querySelector('.search-container');
    let errorElement = document.querySelector('.regex-error');

    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'regex-error';
        searchContainer.appendChild(errorElement);
    }

    errorElement.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        রেগেক্স এরর: ${message}
    `;
}

function clearRegexError() {
    const errorElement = document.querySelector('.regex-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function setSearchMode(mode) {
    currentSearchMode = mode;

    // Update UI
    document.querySelectorAll('.search-mode-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.mode === mode) {
            btn.classList.add('active');
        }
    });

    // Re-search if there's a query
    const query = document.getElementById('searchInput').value.trim();
    if (query) {
        performAdvancedSearch(query);
    }
}

function renderSearchResults(results, query = '') {
    if (results.length === 0) {
        searchResults.innerHTML = '<p class="text-center text-muted">কোন নোট পাওয়া যায়নি</p>';
        return;
    }

    // Apply current sorting to search results
    const sortSelect = document.getElementById('sortSelect');
    const sortOption = sortSelect ? sortSelect.value : 'updated';
    const sortedResults = sortNotes([...results], sortOption);

    searchResults.innerHTML = '';
    sortedResults.forEach(note => {
        const resultItem = createSearchResultItem(note, query);
        searchResults.appendChild(resultItem);
    });
}

function createSearchResultItem(note, query = '') {
    const item = document.createElement('div');
    item.className = 'search-result-item';
    item.onclick = () => {
        closeSearchModal();
        editNote(note.id);
    };

    let preview = getTextPreview(note.content);
    let title = note.title || preview.substring(0, 50);

    // Highlight search terms
    if (query && currentSearchMode !== 'regex') {
        title = highlightSearchTerms(title, query);
        preview = highlightSearchTerms(preview, query);
    }

    const fullDate = formatDate(note.updatedAt);
    const priorityIcon = getPriorityIcon(note.priority);
    const tagsHtml = '';

    item.innerHTML = `
        <div class="search-result-header">
            <div class="search-result-title">${title}</div>
            <div class="search-result-priority">${priorityIcon}</div>
        </div>
        <div class="search-result-content">${preview}</div>
        ${tagsHtml}
        <div class="search-result-date">${fullDate}</div>
    `;

    return item;
}

function highlightSearchTerms(text, query) {
    if (!query || currentSearchMode === 'regex') return text;

    const caseSensitive = document.getElementById('caseSensitiveSearch')?.checked || false;
    const wholeWord = document.getElementById('wholeWordSearch')?.checked || false;

    let searchTerms = [query];
    if (currentSearchMode === 'fulltext') {
        searchTerms = query.split(/\s+/).filter(term => term.length > 0);
    }

    let highlightedText = text;

    searchTerms.forEach(term => {
        const flags = caseSensitive ? 'g' : 'gi';
        const pattern = wholeWord ? `\\b${escapeRegex(term)}\\b` : escapeRegex(term);
        const regex = new RegExp(pattern, flags);

        highlightedText = highlightedText.replace(regex, match =>
            `<span class="search-result-highlight">${match}</span>`
        );
    });

    return highlightedText;
}

function setupAdvancedSearchListeners() {
    // Search input
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    // Clear search button
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
    }

    // Search mode buttons
    document.querySelectorAll('.search-mode-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const mode = e.target.closest('.search-mode-btn').dataset.mode;
            setSearchMode(mode);
        });
    });

    // Search option checkboxes
    const searchOptions = ['caseSensitiveSearch', 'wholeWordSearch'];
    searchOptions.forEach(optionId => {
        const checkbox = document.getElementById(optionId);
        if (checkbox) {
            checkbox.addEventListener('change', () => {
                const query = searchInput?.value.trim();
                if (query) {
                    performAdvancedSearch(query);
                }
            });
        }
    });

    // Search filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', handleAdvancedFilter);
    });



    // Search fullscreen button
    const searchFullscreenBtn = document.getElementById('searchFullscreenBtn');
    if (searchFullscreenBtn) {
        searchFullscreenBtn.addEventListener('click', toggleSearchFullscreen);
    }
}

// Search in bookmarks
function performBookmarkSearch(query, caseSensitive, wholeWord) {
    if (!window.bookmarkManager || !window.bookmarkManager.bookmarks) {
        return [];
    }

    const searchQuery = caseSensitive ? query : query.toLowerCase();

    return window.bookmarkManager.bookmarks.filter(bookmark => {
        let title = bookmark.title || '';
        let url = bookmark.url || '';
        let description = bookmark.description || '';
        let category = window.bookmarkManager.getCategoryName(bookmark.category) || '';
        let tags = (bookmark.tags || []).join(' ');

        if (!caseSensitive) {
            title = title.toLowerCase();
            url = url.toLowerCase();
            description = description.toLowerCase();
            category = category.toLowerCase();
            tags = tags.toLowerCase();
        }

        const searchableText = `${title} ${url} ${description} ${category} ${tags}`;

        if (wholeWord) {
            const regex = new RegExp(`\\b${escapeRegex(searchQuery)}\\b`, caseSensitive ? 'g' : 'gi');
            return regex.test(searchableText);
        } else {
            return searchableText.includes(searchQuery);
        }
    });
}

// Render combined search results (notes + bookmarks)
function renderCombinedSearchResults(noteResults, bookmarkResults, query = '') {
    if (noteResults.length === 0 && bookmarkResults.length === 0) {
        searchResults.innerHTML = '<p class="text-center text-muted">কোন ফলাফল পাওয়া যায়নি</p>';
        return;
    }

    searchResults.innerHTML = '';

    // Add notes section if there are note results
    if (noteResults.length > 0) {
        const notesSection = document.createElement('div');
        notesSection.className = 'search-section';
        notesSection.innerHTML = `<h5 class="search-section-title"><i class="fas fa-sticky-note"></i> নোট (${noteResults.length})</h5>`;

        // Apply current sorting to search results
        const sortSelect = document.getElementById('sortSelect');
        const sortOption = sortSelect ? sortSelect.value : 'updated';
        const sortedResults = sortNotes([...noteResults], sortOption);

        sortedResults.forEach(note => {
            const resultItem = createSearchResultItem(note, query);
            notesSection.appendChild(resultItem);
        });

        searchResults.appendChild(notesSection);
    }

    // Add bookmarks section if there are bookmark results
    if (bookmarkResults.length > 0) {
        const bookmarksSection = document.createElement('div');
        bookmarksSection.className = 'search-section';
        bookmarksSection.innerHTML = `<h5 class="search-section-title"><i class="fas fa-bookmark"></i> বুকমার্ক (${bookmarkResults.length})</h5>`;

        bookmarkResults.forEach(bookmark => {
            const resultItem = createBookmarkSearchResultItem(bookmark, query);
            bookmarksSection.appendChild(resultItem);
        });

        searchResults.appendChild(bookmarksSection);
    }
}

// Create bookmark search result item
function createBookmarkSearchResultItem(bookmark, query = '') {
    const item = document.createElement('div');
    item.className = 'search-result-item bookmark-result';
    item.onclick = () => {
        closeSearchModal();
        window.bookmarkManager.visitBookmark(bookmark.id);
    };

    let title = bookmark.title || bookmark.url;
    let url = bookmark.url || '';
    let category = window.bookmarkManager.getCategoryName(bookmark.category) || '';

    // Highlight search terms
    if (query && currentSearchMode !== 'regex') {
        title = highlightSearchTerms(title, query);
        url = highlightSearchTerms(url, query);
        category = highlightSearchTerms(category, query);
    }

    item.innerHTML = `
        <div class="search-result-content">
            <div class="search-result-title">
                <i class="fas fa-bookmark search-result-icon"></i>
                ${title}
            </div>
            <div class="search-result-url">${url}</div>
            <div class="search-result-meta">
                <span class="search-result-category">${category}</span>
                ${bookmark.isFavorite ? '<span class="search-result-favorite"><i class="fas fa-heart"></i> ফেভরিট</span>' : ''}
                <span class="search-result-visits">ভিজিট: ${bookmark.visitCount || 0}</span>
            </div>
        </div>
    `;

    return item;
}

function handleAdvancedFilter(event) {
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const filter = event.target.dataset.filter;
    const query = document.getElementById('searchInput')?.value.trim();

    let filteredNotes = notes;

    // Apply search query first if exists
    if (query) {
        const caseSensitive = document.getElementById('caseSensitiveSearch')?.checked || false;
        const wholeWord = document.getElementById('wholeWordSearch')?.checked || false;


        switch (currentSearchMode) {
            case 'normal':
                filteredNotes = performNormalSearch(query, caseSensitive, wholeWord);
                break;
            case 'regex':
                filteredNotes = performRegexSearch(query, caseSensitive);
                break;
            case 'fulltext':
                filteredNotes = performFullTextSearch(query, caseSensitive, wholeWord);
                break;
        }
    }

    // Apply date/priority filters
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    switch (filter) {
        case 'today':
            filteredNotes = filteredNotes.filter(note => {
                const noteDate = new Date(note.createdAt);
                return noteDate >= today;
            });
            break;
        case 'week':
            filteredNotes = filteredNotes.filter(note => {
                const noteDate = new Date(note.createdAt);
                return noteDate >= weekAgo;
            });
            break;
        case 'month':
            filteredNotes = filteredNotes.filter(note => {
                const noteDate = new Date(note.createdAt);
                return noteDate >= monthAgo;
            });
            break;
        case 'priority-high':
            filteredNotes = filteredNotes.filter(note => note.priority === 'high');
            break;
        case 'priority-urgent':
            filteredNotes = filteredNotes.filter(note => note.priority === 'urgent');
            break;
        case 'all':
        default:
            // No additional filtering
            break;
    }

    renderSearchResults(filteredNotes, query);
    showSearchStats(filteredNotes.length);
}



// ===== FILE ATTACHMENT FUNCTIONALITY =====
function setupFileAttachmentListeners() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');

    if (fileUploadArea && fileInput) {
        // Click to upload
        fileUploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', handleFileSelection);

        // Drag and drop
        fileUploadArea.addEventListener('dragover', handleDragOver);
        fileUploadArea.addEventListener('dragleave', handleDragLeave);
        fileUploadArea.addEventListener('drop', handleFileDrop);
    }
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');

    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
}

function handleFileSelection(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
    e.target.value = ''; // Reset input
}

function processFiles(files) {
    const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain', 'application/rtf', 'text/csv',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
        'audio/mpeg', 'audio/wav', 'audio/ogg',
        'video/mp4', 'video/webm', 'video/ogg'
    ];

    files.forEach(file => {
        // Validate file type
        if (!allowedTypes.includes(file.type)) {
            showFileError(`ফাইল টাইপ "${file.type}" সাপোর্টেড নয়`);
            return;
        }

        uploadFile(file);
    });
}

function uploadFile(file) {
    const fileId = generateId();
    const fileData = {
        id: fileId,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadDate: new Date().toISOString(),
        data: null // Will be populated after reading
    };

    // Show upload progress
    showUploadProgress(fileId, file.name);

    // Read file as base64
    const reader = new FileReader();
    reader.onload = function(e) {
        fileData.data = e.target.result;
        attachedFiles.set(fileId, fileData);
        hideUploadProgress(fileId);
        displayAttachedFile(fileData);
        showNotification('সফল', `ফাইল "${file.name}" সফলভাবে আপলোড হয়েছে`, 'success');
    };

    reader.onerror = function() {
        hideUploadProgress(fileId);
        showFileError(`ফাইল "${file.name}" আপলোড করতে সমস্যা হয়েছে`);
    };

    reader.readAsDataURL(file);
}

function showUploadProgress(fileId, fileName) {
    const attachedFilesContainer = document.getElementById('attachedFiles');
    const progressElement = document.createElement('div');
    progressElement.id = `upload-${fileId}`;
    progressElement.className = 'attached-file uploading';

    progressElement.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-upload"></i>
        </div>
        <div class="file-details">
            <div class="file-name">${fileName}</div>
            <div class="upload-progress">
                <div class="upload-progress-bar"></div>
            </div>
        </div>
    `;

    attachedFilesContainer.appendChild(progressElement);

    // Simulate progress animation
    const progressBar = progressElement.querySelector('.upload-progress-bar');
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);
        }
        progressBar.style.width = progress + '%';
    }, 100);
}

function hideUploadProgress(fileId) {
    const progressElement = document.getElementById(`upload-${fileId}`);
    if (progressElement) {
        progressElement.remove();
    }
}

function displayAttachedFile(fileData) {
    const attachedFilesContainer = document.getElementById('attachedFiles');
    const fileElement = document.createElement('div');
    fileElement.className = 'attached-file';
    fileElement.dataset.fileId = fileData.id;

    const fileIcon = getFileIcon(fileData.type);
    const fileSize = formatFileSize(fileData.size);

    fileElement.innerHTML = `
        <div class="file-icon ${getFileIconClass(fileData.type)}">
            <i class="${fileIcon}"></i>
        </div>
        <div class="file-details">
            <div class="file-name" title="${fileData.name}">${fileData.name}</div>
            <div class="file-size">${fileSize}</div>
        </div>
        <div class="file-actions">
            <button class="file-action-btn" onclick="previewFile('${fileData.id}')" title="প্রিভিউ">
                <i class="fas fa-eye"></i>
            </button>
            <button class="file-action-btn" onclick="downloadFile('${fileData.id}')" title="ডাউনলোড">
                <i class="fas fa-download"></i>
            </button>
            <button class="file-action-btn delete" onclick="removeAttachedFile('${fileData.id}')" title="মুছুন">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    attachedFilesContainer.appendChild(fileElement);
}

function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return 'fas fa-image';
    if (mimeType === 'application/pdf') return 'fas fa-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
    if (mimeType === 'text/plain') return 'fas fa-file-alt';
    return 'fas fa-file';
}

function getFileIconClass(mimeType) {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType === 'application/pdf') return 'pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'document';
    if (mimeType === 'text/plain') return 'text';
    return 'default';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showFileError(message) {
    const fileUploadArea = document.getElementById('fileUploadArea');
    let errorElement = fileUploadArea.querySelector('.file-upload-error');

    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'file-upload-error';
        fileUploadArea.appendChild(errorElement);
    }

    errorElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (errorElement.parentNode) {
            errorElement.remove();
        }
    }, 5000);
}

function previewFile(fileId) {
    const fileData = attachedFiles.get(fileId);
    if (!fileData) return;

    currentFilePreview = fileData;

    const modal = document.getElementById('filePreviewModal');
    const title = document.getElementById('filePreviewTitle');
    const container = document.getElementById('filePreviewContainer');
    const info = document.getElementById('fileInfo');

    title.textContent = fileData.name;

    // Clear previous content
    container.innerHTML = '';

    if (fileData.type.startsWith('image/')) {
        // Image preview
        const img = document.createElement('img');
        img.src = fileData.data;
        img.alt = fileData.name;
        container.appendChild(img);
    } else if (fileData.type === 'application/pdf') {
        // PDF preview
        const iframe = document.createElement('iframe');
        iframe.src = fileData.data;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        container.appendChild(iframe);
    } else if (fileData.type === 'text/plain') {
        // Text file preview
        fetch(fileData.data)
            .then(response => response.text())
            .then(text => {
                const pre = document.createElement('pre');
                pre.style.padding = '1rem';
                pre.style.whiteSpace = 'pre-wrap';
                pre.style.maxHeight = '100%';
                pre.style.overflow = 'auto';
                pre.textContent = text;
                container.appendChild(pre);
            })
            .catch(() => {
                showPreviewPlaceholder(container, 'টেক্সট ফাইল প্রিভিউ করা যায়নি');
            });
    } else {
        // Unsupported file type
        showPreviewPlaceholder(container, 'এই ফাইল টাইপের প্রিভিউ সাপোর্টেড নয়');
    }

    // Show file info
    info.innerHTML = `
        <div class="file-info-grid">
            <div class="file-info-item">
                <span class="file-info-label">ফাইলের নাম:</span>
                <span class="file-info-value">${fileData.name}</span>
            </div>
            <div class="file-info-item">
                <span class="file-info-label">সাইজ:</span>
                <span class="file-info-value">${formatFileSize(fileData.size)}</span>
            </div>
            <div class="file-info-item">
                <span class="file-info-label">টাইপ:</span>
                <span class="file-info-value">${fileData.type}</span>
            </div>
            <div class="file-info-item">
                <span class="file-info-label">আপলোড:</span>
                <span class="file-info-value">${formatDate(fileData.uploadDate)}</span>
            </div>
        </div>
    `;

    modal.style.display = 'flex';
}

function showPreviewPlaceholder(container, message) {
    container.innerHTML = `
        <div class="preview-placeholder">
            <i class="fas fa-file"></i>
            <p>${message}</p>
        </div>
    `;
}

function closeFilePreviewModal() {
    const modal = document.getElementById('filePreviewModal');
    modal.style.display = 'none';
    currentFilePreview = null;
}

function downloadFile(fileId) {
    const fileData = attachedFiles.get(fileId);
    if (!fileData) return;

    const link = document.createElement('a');
    link.href = fileData.data;
    link.download = fileData.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('সফল', `ফাইল "${fileData.name}" ডাউনলোড শুরু হয়েছে`, 'success');
}

function removeAttachedFile(fileId) {
    if (confirm('আপনি কি এই ফাইলটি মুছে ফেলতে চান?')) {
        attachedFiles.delete(fileId);
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }
        showNotification('সফল', 'ফাইল মুছে ফেলা হয়েছে', 'success');
    }
}

function toggleFilePreviewFullscreen() {
    const modal = document.getElementById('filePreviewModal');
    const btn = document.getElementById('filePreviewFullscreenBtn');
    const icon = btn.querySelector('i');

    modal.classList.toggle('fullscreen');

    if (modal.classList.contains('fullscreen')) {
        icon.className = 'fas fa-compress';
        btn.title = 'ছোট করুন';
    } else {
        icon.className = 'fas fa-expand';
        btn.title = 'ফুল স্ক্রিন';
    }
}

function setupFilePreviewListeners() {
    const downloadBtn = document.getElementById('downloadFileBtn');
    const fullscreenBtn = document.getElementById('filePreviewFullscreenBtn');

    if (downloadBtn) {
        downloadBtn.addEventListener('click', () => {
            if (currentFilePreview) {
                downloadFile(currentFilePreview.id);
            }
        });
    }

    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFilePreviewFullscreen);
    }
}

// Update note saving to include attachments
function saveNoteWithAttachments() {
    const reminderInput = document.getElementById('noteReminder');
    let reminder = null;

    if (reminderInput && reminderInput.value) {
        try {
            const reminderDate = new Date(reminderInput.value);
            if (!isNaN(reminderDate.getTime())) {
                reminder = reminderDate.toISOString();
            }
        } catch (error) {
            console.error('Error processing reminder in saveNoteWithAttachments:', error);
        }
    }

    const noteData = {
        id: currentNoteId || generateId(),
        title: noteTitle.value.trim(),
        content: joditEditor.value,
        priority: notePriority.value,
        tags: [],
        reminder: reminder,
        attachments: Array.from(attachedFiles.values()),
        createdAt: currentNoteId ? notes.find(n => n.id === currentNoteId)?.createdAt || new Date().toISOString() : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    if (currentNoteId) {
        const index = notes.findIndex(note => note.id === currentNoteId);
        if (index !== -1) {
            notes[index] = noteData;
        }
    } else {
        notes.unshift(noteData);
        currentNoteId = noteData.id;
    }

    saveNotesToStorage();
    renderNotes();
    updateDashboardStats();

    // Clear attachments for new note
    if (!currentNoteId) {
        attachedFiles.clear();
        document.getElementById('attachedFiles').innerHTML = '';
    }

    showNotification('সফল', 'নোট সংরক্ষণ করা হয়েছে', 'success');
    closeNoteModal();
}

// Update note loading to include attachments
function loadNoteAttachments(note) {
    attachedFiles.clear();
    const attachedFilesContainer = document.getElementById('attachedFiles');
    attachedFilesContainer.innerHTML = '';

    if (note.attachments && note.attachments.length > 0) {
        note.attachments.forEach(fileData => {
            attachedFiles.set(fileData.id, fileData);
            displayAttachedFile(fileData);
        });
    }
}














// ===== SHARING & EXPORT FUNCTIONALITY =====
let currentSharedNote = null;
let selectedExportFormat = 'markdown';

function setupSharingListeners() {
    const shareNoteBtn = document.getElementById('shareNoteBtn');
    const exportNoteBtn = document.getElementById('exportNoteBtn');
    const copyShareLinkBtn = document.getElementById('copyShareLinkBtn');
    const shareFullscreenBtn = document.getElementById('shareFullscreenBtn');

    if (shareNoteBtn) {
        shareNoteBtn.addEventListener('click', openShareModal);
    }

    if (exportNoteBtn) {
        exportNoteBtn.addEventListener('click', openExportModal);
    }

    if (copyShareLinkBtn) {
        copyShareLinkBtn.addEventListener('click', copyShareLink);
    }

    if (shareFullscreenBtn) {
        shareFullscreenBtn.addEventListener('click', toggleShareFullscreen);
    }

    // Share options
    // const shareWithPassword = document.getElementById('shareWithPassword'); // Not used currently
    const shareExpiry = document.getElementById('shareExpiry');
    const shareExpirySection = document.getElementById('shareExpirySection');

    if (shareExpiry) {
        shareExpiry.addEventListener('change', (e) => {
            shareExpirySection.style.display = e.target.checked ? 'block' : 'none';
        });
    }

    // Social share buttons
    const socialButtons = ['Facebook', 'Twitter', 'WhatsApp', 'Telegram', 'Email'];
    socialButtons.forEach(platform => {
        const btn = document.getElementById(`share${platform}Btn`);
        if (btn) {
            btn.addEventListener('click', () => shareToSocial(platform.toLowerCase()));
        }
    });

    // Custom domain update button
    const updateDomainBtn = document.getElementById('updateDomainBtn');
    if (updateDomainBtn) {
        updateDomainBtn.addEventListener('click', updateCustomDomain);
    }

    // QR Code buttons
    const downloadQRBtn = document.getElementById('downloadQRBtn');
    const printQRBtn = document.getElementById('printQRBtn');

    if (downloadQRBtn) {
        downloadQRBtn.addEventListener('click', downloadQRCode);
    }

    if (printQRBtn) {
        printQRBtn.addEventListener('click', printQRCode);
    }

    // Export format selection
    document.querySelectorAll('.format-option').forEach(option => {
        option.addEventListener('click', (e) => {
            document.querySelectorAll('.format-option').forEach(opt => opt.classList.remove('selected'));
            e.currentTarget.classList.add('selected');
            selectedExportFormat = e.currentTarget.dataset.format;
        });
    });

    // Export button
    const exportNoteFileBtn = document.getElementById('exportNoteFileBtn');
    if (exportNoteFileBtn) {
        exportNoteFileBtn.addEventListener('click', exportNoteFile);
    }
}

function openShareModal() {
    if (!currentNoteId) return;

    const note = notes.find(n => n.id === currentNoteId);
    if (!note) return;

    currentSharedNote = note;

    const modal = document.getElementById('shareModal');
    const shareLink = document.getElementById('shareLink');
    const customDomainInput = document.getElementById('customDomainInput');

    // Load saved custom domain
    const savedDomain = localStorage.getItem('customShareDomain');
    if (customDomainInput && savedDomain) {
        customDomainInput.value = savedDomain;
    }

    // Generate share link
    const shareUrl = generateShareLink(note);
    shareLink.value = shareUrl;

    // Generate QR code
    generateQRCode(shareUrl);

    modal.style.display = 'flex';
}

function closeShareModal() {
    const modal = document.getElementById('shareModal');
    modal.style.display = 'none';
    currentSharedNote = null;
}

function openExportModal() {
    if (!currentNoteId) return;

    const modal = document.getElementById('exportModal');

    // Reset format selection
    document.querySelectorAll('.format-option').forEach(opt => opt.classList.remove('selected'));
    document.querySelector(`[data-format="${selectedExportFormat}"]`)?.classList.add('selected');

    modal.style.display = 'flex';
}

function closeExportModal() {
    const modal = document.getElementById('exportModal');
    modal.style.display = 'none';
}

function generateShareLink(note) {
    // Generate a proper web-friendly share URL
    const shareId = btoa(note.id).replace(/[^a-zA-Z0-9]/g, '');

    // Get custom domain from localStorage or use default
    const customDomain = localStorage.getItem('customShareDomain') || 'https://my-smart-notes.app';

    // Check if we're running locally or on a web server
    if (window.location.protocol === 'file:') {
        // For local development, use custom domain or default
        return `${customDomain}/share/${shareId}`;
    } else {
        // For web deployment, use actual domain
        const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '');
        return `${baseUrl}/share/${shareId}`;
    }
}

function copyShareLink() {
    const shareLink = document.getElementById('shareLink');
    const copyBtn = document.getElementById('copyShareLinkBtn');

    shareLink.select();
    shareLink.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');

        // Visual feedback
        shareLink.classList.add('copied');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> কপি হয়েছে!';

        setTimeout(() => {
            shareLink.classList.remove('copied');
            copyBtn.innerHTML = originalText;
        }, 2000);

        showNotification('সফল', 'শেয়ার লিংক কপি করা হয়েছে', 'success');
    } catch (err) {
        showNotification('ত্রুটি', 'লিংক কপি করতে সমস্যা হয়েছে', 'error');
    }
}

function generateQRCode(url) {
    const qrContainer = document.getElementById('qrCode');

    // Clear previous QR code
    qrContainer.innerHTML = '';

    try {
        // Check if QRious library is available
        if (typeof QRious !== 'undefined') {
            // Create canvas element
            const canvas = document.createElement('canvas');
            qrContainer.appendChild(canvas);

            // Generate QR code using QRious
            new QRious({
                element: canvas,
                value: url,
                size: 200,
                level: 'M',
                background: '#ffffff',
                foreground: '#000000'
            });

            console.log('QR Code generated successfully with QRious');
        } else {
            console.warn('QRious library not loaded, using Google Charts API');
            generateQRWithGoogleAPI(url, qrContainer);
        }
    } catch (error) {
        console.error('QR Code generation error:', error);
        generateQRWithGoogleAPI(url, qrContainer);
    }
}

function generateQRWithGoogleAPI(url, container) {
    // Use Google Charts QR API as fallback
    const qrImg = document.createElement('img');
    const encodedUrl = encodeURIComponent(url);
    qrImg.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodedUrl}`;
    qrImg.style.width = '200px';
    qrImg.style.height = '200px';
    qrImg.style.border = '2px solid #000';
    qrImg.style.borderRadius = '8px';
    qrImg.alt = 'QR Code';

    qrImg.onload = function() {
        console.log('QR Code generated successfully with Google API');
    };

    qrImg.onerror = function() {
        console.error('Failed to load QR code from Google API, using fallback');
        generateFallbackQR(url, container);
    };

    container.appendChild(qrImg);
}

function generateFallbackQR(url, container) {
    const qrText = document.createElement('div');
    qrText.style.cssText = `
        width: 200px;
        height: 200px;
        background: white;
        border: 2px solid #000;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 12px;
        word-break: break-all;
        padding: 10px;
        box-sizing: border-box;
        color: #000;
    `;
    qrText.innerHTML = `<div>QR Code<br><small>${url}</small></div>`;
    container.appendChild(qrText);
}

function downloadQRCode() {
    const qrContainer = document.getElementById('qrCode');
    const canvas = qrContainer.querySelector('canvas');
    const img = qrContainer.querySelector('img');

    if (canvas) {
        // Download the QR code canvas directly
        const link = document.createElement('a');
        link.download = `qr-code-${currentSharedNote?.title || 'note'}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();

        showNotification('সফল', 'QR কোড ডাউনলোড হয়েছে', 'success');
    } else if (img) {
        // Create a canvas to convert the image to downloadable format
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = 200;
        canvas.height = 200;

        // Draw the QR code image on canvas
        ctx.drawImage(img, 0, 0, 200, 200);

        // Download the canvas as PNG
        const link = document.createElement('a');
        link.download = `qr-code-${currentSharedNote?.title || 'note'}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();

        showNotification('সফল', 'QR কোড ডাউনলোড হয়েছে', 'success');
    } else {
        // Fallback: create a simple canvas if no QR code exists
        const fallbackCanvas = document.createElement('canvas');
        const ctx = fallbackCanvas.getContext('2d');

        fallbackCanvas.width = 200;
        fallbackCanvas.height = 200;

        // Simple QR code representation
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = 'black';
        ctx.strokeRect(0, 0, 200, 200);

        ctx.fillStyle = 'black';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('QR Code', 100, 100);

        // Download
        const link = document.createElement('a');
        link.download = `qr-code-${currentSharedNote?.title || 'note'}.png`;
        link.href = fallbackCanvas.toDataURL('image/png');
        link.click();

        showNotification('তথ্য', 'QR কোড ডাউনলোড হয়েছে (ফলব্যাক)', 'info');
    }
}

function printQRCode() {
    const qrContainer = document.getElementById('qrCode');
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <html>
            <head>
                <title>QR Code - ${currentSharedNote?.title || 'Note'}</title>
                <style>
                    body { text-align: center; font-family: Arial, sans-serif; }
                    .qr-container { margin: 20px; }
                    h1 { color: #333; }
                </style>
            </head>
            <body>
                <h1>${currentSharedNote?.title || 'Note'}</h1>
                <div class="qr-container">
                    ${qrContainer.innerHTML}
                </div>
                <p>Scan this QR code to view the note</p>
            </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

function shareToSocial(platform) {
    if (!currentSharedNote) return;

    const shareUrl = document.getElementById('shareLink').value;
    const title = currentSharedNote.title || 'Shared Note';
    const text = `Check out this note: ${title}`;

    let socialUrl = '';

    switch (platform) {
        case 'facebook':
            socialUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
            break;
        case 'twitter':
            socialUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareUrl)}`;
            break;
        case 'whatsapp':
            socialUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + shareUrl)}`;
            break;
        case 'telegram':
            socialUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(text)}`;
            break;
        case 'email':
            socialUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(text + '\n\n' + shareUrl)}`;
            break;
    }

    if (socialUrl) {
        window.open(socialUrl, '_blank', 'width=600,height=400');
    }
}

function updateCustomDomain() {
    const customDomainInput = document.getElementById('customDomainInput');
    const shareLink = document.getElementById('shareLink');

    if (!customDomainInput || !currentSharedNote) return;

    const newDomain = customDomainInput.value.trim();

    // Validate URL format
    if (newDomain && !isValidUrl(newDomain)) {
        showNotification('ত্রুটি', 'অবৈধ URL ফরম্যাট। সঠিক URL দিন (যেমন: https://example.com)', 'error');
        return;
    }

    // Save to localStorage
    if (newDomain) {
        localStorage.setItem('customShareDomain', newDomain);
        showNotification('সফল', 'কাস্টম ডোমেইন আপডেট হয়েছে', 'success');
    } else {
        localStorage.removeItem('customShareDomain');
        showNotification('তথ্য', 'কাস্টম ডোমেইন রিমুভ করা হয়েছে', 'info');
    }

    // Regenerate share link and QR code
    const shareUrl = generateShareLink(currentSharedNote);
    shareLink.value = shareUrl;
    generateQRCode(shareUrl);
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function toggleShareFullscreen() {
    const modal = document.getElementById('shareModal');
    const btn = document.getElementById('shareFullscreenBtn');
    const icon = btn.querySelector('i');

    modal.classList.toggle('fullscreen');

    if (modal.classList.contains('fullscreen')) {
        icon.className = 'fas fa-compress';
        btn.title = 'ছোট করুন';
    } else {
        icon.className = 'fas fa-expand';
        btn.title = 'ফুল স্ক্রিন';
    }
}

function exportNoteFile() {
    if (!currentNoteId) return;

    const note = notes.find(n => n.id === currentNoteId);
    if (!note) return;

    const includeAttachments = document.getElementById('includeAttachments').checked;
    const includeMetadata = document.getElementById('includeMetadata').checked;
    const preserveFormatting = document.getElementById('preserveFormatting').checked;

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (selectedExportFormat) {
        case 'markdown':
            content = exportToMarkdown(note, includeMetadata, includeAttachments);
            filename = `${note.title || 'note'}.md`;
            mimeType = 'text/markdown';
            break;
        case 'html':
            content = exportToHTML(note, includeMetadata, includeAttachments, preserveFormatting);
            filename = `${note.title || 'note'}.html`;
            mimeType = 'text/html';
            break;
        case 'txt':
            content = exportToText(note, includeMetadata);
            filename = `${note.title || 'note'}.txt`;
            mimeType = 'text/plain';
            break;
        case 'pdf':
            exportToPDF(note, includeMetadata, includeAttachments);
            return;
    }

    // Download file
    const blob = new Blob([content], { type: mimeType });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    closeExportModal();
    showNotification('সফল', `নোট ${selectedExportFormat.toUpperCase()} ফরম্যাটে এক্সপোর্ট হয়েছে`, 'success');
}

function exportToMarkdown(note, includeMetadata, includeAttachments) {
    let content = '';

    // Title
    if (note.title) {
        content += `# ${note.title}\n\n`;
    }

    // Metadata
    if (includeMetadata) {
        content += '## মেটাডেটা\n\n';
        content += `- **তৈরি:** ${formatDate(note.createdAt)}\n`;
        content += `- **আপডেট:** ${formatDate(note.updatedAt)}\n`;
        content += `- **অগ্রাধিকার:** ${getPriorityText(note.priority)}\n`;



        if (note.reminder) {
            content += `- **রিমাইন্ডার:** ${formatDate(note.reminder)}\n`;
        }

        content += '\n';
    }

    // Content
    content += '## বিষয়বস্তু\n\n';
    content += convertHTMLToMarkdown(note.content);

    // Attachments
    if (includeAttachments && note.attachments && note.attachments.length > 0) {
        content += '\n\n## সংযুক্ত ফাইল\n\n';
        note.attachments.forEach(file => {
            content += `- [${file.name}](data:${file.type};base64,${file.data.split(',')[1]})\n`;
        });
    }

    return content;
}

function exportToHTML(note, includeMetadata, includeAttachments, preserveFormatting) {
    let html = `
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${note.title || 'Note'}</title>
    <style>
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        .metadata {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .metadata h2 {
            margin-top: 0;
        }
        .attachments {
            border-top: 1px solid #ddd;
            padding-top: 20px;
            margin-top: 20px;
        }
        .attachment {
            display: inline-block;
            margin: 5px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            text-decoration: none;
            color: #495057;
        }
    </style>
</head>
<body>
`;

    // Title
    if (note.title) {
        html += `    <h1>${note.title}</h1>\n`;
    }

    // Metadata
    if (includeMetadata) {
        html += `    <div class="metadata">
        <h2>মেটাডেটা</h2>
        <p><strong>তৈরি:</strong> ${formatDate(note.createdAt)}</p>
        <p><strong>আপডেট:</strong> ${formatDate(note.updatedAt)}</p>
        <p><strong>অগ্রাধিকার:</strong> ${getPriorityText(note.priority)}</p>`;



        if (note.reminder) {
            html += `        <p><strong>রিমাইন্ডার:</strong> ${formatDate(note.reminder)}</p>`;
        }

        html += `    </div>\n`;
    }

    // Content
    html += `    <div class="content">\n`;
    if (preserveFormatting) {
        html += note.content;
    } else {
        html += `        <p>${getTextPreview(note.content)}</p>`;
    }
    html += `    </div>\n`;

    // Attachments
    if (includeAttachments && note.attachments && note.attachments.length > 0) {
        html += `    <div class="attachments">
        <h2>সংযুক্ত ফাইল</h2>`;

        note.attachments.forEach(file => {
            html += `        <a href="${file.data}" download="${file.name}" class="attachment">
            📎 ${file.name} (${formatFileSize(file.size)})
        </a>`;
        });

        html += `    </div>\n`;
    }

    html += `</body>
</html>`;

    return html;
}

function exportToText(note, includeMetadata) {
    let content = '';

    // Title
    if (note.title) {
        content += `${note.title}\n`;
        content += '='.repeat(note.title.length) + '\n\n';
    }

    // Metadata
    if (includeMetadata) {
        content += 'মেটাডেটা:\n';
        content += '-'.repeat(10) + '\n';
        content += `তৈরি: ${formatDate(note.createdAt)}\n`;
        content += `আপডেট: ${formatDate(note.updatedAt)}\n`;
        content += `অগ্রাধিকার: ${getPriorityText(note.priority)}\n`;



        if (note.reminder) {
            content += `রিমাইন্ডার: ${formatDate(note.reminder)}\n`;
        }

        content += '\n';
    }

    // Content
    content += 'বিষয়বস্তু:\n';
    content += '-'.repeat(10) + '\n';
    content += getTextPreview(note.content);

    return content;
}

function exportToPDF(note, includeMetadata, includeAttachments) {
    // For PDF export, we'll use the browser's print functionality
    const printWindow = window.open('', '_blank');
    const htmlContent = exportToHTML(note, includeMetadata, includeAttachments, true);

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    printWindow.onload = function() {
        printWindow.print();
        setTimeout(() => {
            printWindow.close();
        }, 1000);
    };

    closeExportModal();
    showNotification('তথ্য', 'PDF এক্সপোর্টের জন্য প্রিন্ট ডায়ালগ খোলা হয়েছে', 'info');
}

function convertHTMLToMarkdown(html) {
    // Simple HTML to Markdown conversion
    let markdown = html;

    // Remove HTML tags and convert to plain text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    markdown = tempDiv.textContent || tempDiv.innerText || '';

    return markdown;
}

function getPriorityText(priority) {
    const priorities = {
        'low': 'কম 🟢',
        'medium': 'মাধ্যম 🟡',
        'high': 'উচ্চ 🟠',
        'urgent': 'জরুরি 🔴'
    };
    return priorities[priority] || 'মাধ্যম 🟡';
}

// ===== FILTER FUNCTIONALITY =====
function handleFilter(event) {
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const filter = event.target.dataset.filter;
    let filteredNotes = notes;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    switch (filter) {
        case 'today':
            filteredNotes = notes.filter(note => {
                const noteDate = new Date(note.updatedAt);
                return noteDate >= today;
            });
            break;
        case 'week':
            filteredNotes = notes.filter(note => {
                const noteDate = new Date(note.updatedAt);
                return noteDate >= weekAgo;
            });
            break;
        case 'month':
            filteredNotes = notes.filter(note => {
                const noteDate = new Date(note.updatedAt);
                return noteDate >= monthAgo;
            });
            break;
        default:
            filteredNotes = notes;
    }

    renderSearchResults(filteredNotes);
}

// ===== EMOJI FUNCTIONALITY =====
function loadEmojis(category) {
    emojiGrid.innerHTML = '';
    const emojis = emojiData[category] || [];

    emojis.forEach(emoji => {
        const emojiBtn = document.createElement('button');
        emojiBtn.className = 'emoji-item';
        emojiBtn.textContent = emoji;
        emojiBtn.onclick = () => insertEmoji(emoji);
        emojiGrid.appendChild(emojiBtn);
    });
}

function handleEmojiCategory(event) {
    document.querySelectorAll('.emoji-category').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const category = event.target.dataset.category;
    loadEmojis(category);
}

function insertEmoji(emoji) {
    joditEditor.selection.insertHTML(emoji);
    closeEmojiModal();
}

// ===== SYMBOLS FUNCTIONALITY =====
function insertSymbols(editor) {
    // Check if symbols toolbar already exists
    let existingToolbar = document.getElementById('symbolsToolbar');
    if (existingToolbar) {
        existingToolbar.remove();
        return;
    }

    const symbols = [
        '★', '☆', '♠', '♣', '♥', '♦', '♪', '♫', '☀', '☁', '☂', '☃', '☄', '⚡', '❄', '🔥', '💧', '🌟',
        '→', '←', '↑', '↓', '↔', '↕', '⇒', '⇐', '⇑', '⇓', '⇔', '⇕', '∞', '≠', '≤', '≥', '±', '×', '÷', '√',
        '©', '®', '™', '§', '¶', '†', '‡', '•', '‰', '′', '″', '‴', '※', '‼', '⁇', '⁈', '⁉', '‽',
        'α', 'β', 'γ', 'δ', 'ε', 'ζ', 'η', 'θ', 'ι', 'κ', 'λ', 'μ', 'ν', 'ξ', 'ο', 'π', 'ρ', 'σ',
        '¢', '£', '¤', '¥', '€', '₹', '₽', '₩', '₪', '₫', '₦', '₡', '₨', '₱', '₵', '₴', '₸', '₼'
    ];

    // Create symbols toolbar
    const symbolsToolbar = document.createElement('div');
    symbolsToolbar.id = 'symbolsToolbar';
    symbolsToolbar.className = 'symbols-toolbar';

    // Create title
    const title = document.createElement('span');
    title.className = 'symbols-title';
    title.textContent = 'সিমবল নির্বাচন করুন:';

    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'symbols-close-btn';
    closeBtn.innerHTML = '<i class="fas fa-times"></i>';
    closeBtn.onclick = () => symbolsToolbar.remove();

    // Create symbols container
    const symbolsContainer = document.createElement('div');
    symbolsContainer.className = 'symbols-container';

    symbols.forEach(symbol => {
        const symbolBtn = document.createElement('button');
        symbolBtn.className = 'symbol-btn';
        symbolBtn.textContent = symbol;
        symbolBtn.onclick = () => insertSymbolToEditor(symbol);
        symbolsContainer.appendChild(symbolBtn);
    });

    symbolsToolbar.appendChild(title);
    symbolsToolbar.appendChild(symbolsContainer);
    symbolsToolbar.appendChild(closeBtn);

    // Insert toolbar after modal header
    const modalHeader = document.querySelector('.modal-header');
    if (modalHeader) {
        modalHeader.insertAdjacentElement('afterend', symbolsToolbar);
    }
}

function insertSymbolToEditor(symbol) {
    joditEditor.selection.insertHTML(symbol);
    // Keep the toolbar open for multiple insertions
}

// ===== HORIZONTAL LINE INSERTION =====
function insertHorizontalLineAtCursor(editor) {
    try {
        // Focus the editor first
        editor.focus();

        // Use Jodit's built-in command system for better compatibility
        const hrHTML = '<hr style="border: none; border-top: 2px solid #ccc; margin: 15px 0; width: 100%; display: block;">';

        // Method 1: Use Jodit's insertHTML method
        if (editor.selection && editor.selection.insertHTML) {
            // Insert HR at current cursor position
            editor.selection.insertHTML(hrHTML);

            // Add a paragraph after HR for better cursor positioning
            editor.selection.insertHTML('<p><br></p>');

            // Move cursor to the new paragraph
            setTimeout(() => {
                const selection = editor.selection;
                if (selection && selection.range) {
                    const range = selection.range;
                    range.collapse(false); // Move to end
                    selection.selectRange(range);
                }
            }, 10);

        } else {
            // Method 2: Fallback using document.execCommand
            try {
                const doc = editor.editorDocument;
                if (doc && doc.execCommand) {
                    doc.execCommand('insertHTML', false, hrHTML + '<p><br></p>');
                } else {
                    throw new Error('execCommand not available');
                }
            } catch (execError) {
                // Method 3: Direct DOM manipulation
                const selection = editor.selection;
                const range = selection.range;

                if (range) {
                    // Create HR element
                    const hr = editor.create.element('hr');
                    hr.style.cssText = 'border: none; border-top: 2px solid #ccc; margin: 15px 0; width: 100%; display: block;';

                    // Create paragraph element
                    const p = editor.create.element('p');
                    p.innerHTML = '<br>';

                    // Insert elements
                    range.deleteContents();
                    range.insertNode(p);
                    range.insertNode(hr);

                    // Position cursor after the paragraph
                    range.setStartAfter(p);
                    range.collapse(true);
                    selection.selectRange(range);
                } else {
                    // Last resort: append to editor content
                    editor.value += hrHTML + '<p><br></p>';
                }
            }
        }

        // Trigger change event to update editor state
        if (editor.events) {
            editor.events.fire('change');
        }

        // Ensure editor remains focused
        setTimeout(() => {
            editor.focus();
        }, 50);

    } catch (error) {
        console.error('Error inserting horizontal line:', error);

        // Ultimate fallback
        try {
            const currentContent = editor.value || '';
            editor.value = currentContent + '<hr style="border: none; border-top: 2px solid #ccc; margin: 15px 0; width: 100%;"><p><br></p>';
            editor.focus();
        } catch (fallbackError) {
            console.error('All horizontal line insertion methods failed:', fallbackError);
            alert('হরাইজন্টাল লাইন যোগ করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।');
        }
    }
}

// ===== DATE & TIME FUNCTIONS =====
function insertCurrentDate() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const dateString = now.toLocaleDateString('bn-BD', options);
    joditEditor.selection.insertHTML(`<span class="date">${dateString}</span> `);
}

function insertCurrentTime() {
    const now = new Date();
    const options = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };
    const timeString = now.toLocaleTimeString('bn-BD', options);
    joditEditor.selection.insertHTML(`<span class="time">${timeString}</span> `);
}

function insertCurrentDateTime() {
    const now = new Date();
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };

    const dateString = now.toLocaleDateString('bn-BD', dateOptions);
    const timeString = now.toLocaleTimeString('bn-BD', timeOptions);
    const fullDateTime = `${dateString}, ${timeString}`;

    joditEditor.selection.insertHTML(`<span class="datetime">${fullDateTime}</span> `);
}

function formatDate(dateString) {
    const date = new Date(dateString);

    // Full date and time format with English numerals and full month names
    const dateOptions = {
        year: 'numeric',
        month: 'long',  // Full month name (January, February, etc.)
        day: 'numeric'
    };

    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };

    const formattedDate = date.toLocaleDateString('en-US', dateOptions);
    const formattedTime = date.toLocaleTimeString('en-US', timeOptions);
    const weekday = date.toLocaleDateString('en-US', { weekday: 'short' });

    return `${weekday}, ${formattedDate} ${formattedTime}`;
}

// Function for detailed Bengali date format (for tooltips)
function formatDateDetailed(dateString) {
    const date = new Date(dateString);

    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };

    const formattedDate = date.toLocaleDateString('bn-BD', dateOptions);
    const formattedTime = date.toLocaleTimeString('bn-BD', timeOptions);

    return `${formattedDate}, ${formattedTime}`;
}

function formatDateShort(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date >= today) {
        return 'আজ ' + date.toLocaleTimeString('bn-BD', { hour: '2-digit', minute: '2-digit' });
    } else if (date >= yesterday) {
        return 'গতকাল ' + date.toLocaleTimeString('bn-BD', { hour: '2-digit', minute: '2-digit' });
    } else {
        return date.toLocaleDateString('bn-BD', { year: 'numeric', month: 'short', day: 'numeric' });
    }
}

function formatLastSavedText(dateString) {
    const date = new Date(dateString);

    // Format for English locale to match the example format
    const dateOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    };

    const formattedDate = date.toLocaleDateString('en-US', dateOptions);
    const formattedTime = date.toLocaleTimeString('en-US', timeOptions);

    return `সর্বশেষ সংরক্ষন - ${formattedDate} ${formattedTime}`;
}

// ===== PATH COPY FUNCTIONALITY =====
function copyPathToClipboard() {
    const pathText = document.getElementById('pathText');
    if (!pathText) return;

    const path = pathText.textContent;

    // Try to use the modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(path).then(() => {
            showNotification('সফল', 'পাথ ক্লিপবোর্ডে কপি হয়েছে', 'success');

            // Visual feedback
            const copyBtn = document.getElementById('copyPathBtn');
            const originalIcon = copyBtn.querySelector('i').className;
            copyBtn.querySelector('i').className = 'fas fa-check';
            copyBtn.style.background = '#28a745';

            setTimeout(() => {
                copyBtn.querySelector('i').className = originalIcon;
                copyBtn.style.background = '';
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy path: ', err);
            fallbackCopyPath(path);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyPath(path);
    }
}

function fallbackCopyPath(path) {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = path;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);

    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showNotification('সফল', 'পাথ ক্লিপবোর্ডে কপি হয়েছে', 'success');

        // Visual feedback
        const copyBtn = document.getElementById('copyPathBtn');
        const originalIcon = copyBtn.querySelector('i').className;
        copyBtn.querySelector('i').className = 'fas fa-check';
        copyBtn.style.background = '#28a745';

        setTimeout(() => {
            copyBtn.querySelector('i').className = originalIcon;
            copyBtn.style.background = '';
        }, 2000);
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        showNotification('ত্রুটি', 'পাথ কপি করতে সমস্যা হয়েছে', 'error');
    }

    document.body.removeChild(textArea);
}



// ===== NOTIFICATION SYSTEM =====
function showNotification(title, message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas ${getNotificationIcon(type)}"></i>
            </div>
            <div class="notification-text">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
        </div>
        <button class="notification-close" onclick="closeNotification(this)" title="বন্ধ করুন">
            <i class="fas fa-times"></i>
        </button>
    `;

    notificationContainer.appendChild(notification);

    // Play notification sound
    if (notificationSound) {
        notificationSound.play();
    }

    // Animate notification bell
    if (notificationBell) {
        notificationBell.classList.add('has-notifications');
        setTimeout(() => {
            notificationBell.classList.remove('has-notifications');
        }, 500);
    }

    // Auto remove after 6 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            closeNotification(notification.querySelector('.notification-close'));
        }
    }, 6000);
}

function closeNotification(button) {
    const notification = button.parentElement;
    notification.classList.add('fade-out');
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 400);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        case 'info': return 'fa-info-circle';
        default: return 'fa-info-circle';
    }
}

// ===== DRAG AND DROP FUNCTIONALITY =====
function toggleDragMode() {
    isDragMode = !isDragMode;
    const toggleBtn = document.getElementById('toggleDragBtn');
    const resetBtn = document.getElementById('resetOrderBtn');

    if (isDragMode) {
        enableDragMode();
        toggleBtn.classList.add('active');
        toggleBtn.title = 'ড্র্যাগ মোড বন্ধ করুন';
        if (resetBtn) resetBtn.style.display = 'inline-block';
        showDragIndicator();
        showNotification('ড্র্যাগ মোড', 'নোট ড্র্যাগ করে সাজান', 'info');
    } else {
        disableDragMode();
        toggleBtn.classList.remove('active');
        toggleBtn.title = 'ড্র্যাগ মোড চালু করুন';
        if (resetBtn) resetBtn.style.display = 'none';
        hideDragIndicator();
        showNotification('ড্র্যাগ মোড', 'ড্র্যাগ মোড বন্ধ করা হয়েছে', 'info');
    }
}

function enableDragMode() {
    const notesGrid = document.getElementById('notesGrid');
    if (!notesGrid) return;

    // Store original order
    originalNotesOrder = [...notes];

    // Add drag mode class
    notesGrid.classList.add('drag-mode');

    // Initialize Sortable
    if (sortableInstance) {
        sortableInstance.destroy();
    }

    sortableInstance = new Sortable(notesGrid, {
        animation: 300,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        onStart: function(evt) {
            document.body.style.cursor = 'grabbing';
        },
        onEnd: function(evt) {
            document.body.style.cursor = '';
            const oldIndex = evt.oldIndex;
            const newIndex = evt.newIndex;

            if (oldIndex !== newIndex) {
                // Reorder notes array
                const movedNote = notes.splice(oldIndex, 1)[0];
                notes.splice(newIndex, 0, movedNote);

                // Save the new order
                saveNotes();

                // Show success notification
                showNotification('সফল', 'নোটের ক্রম পরিবর্তন করা হয়েছে', 'success');

                // Update dashboard if open
                const dashboardModal = document.getElementById('dashboardModal');
                if (dashboardModal && dashboardModal.style.display === 'flex') {
                    updateDashboardStats();
                    updateRecentActivity();
                }
            }
        }
    });
}

function disableDragMode() {
    const notesGrid = document.getElementById('notesGrid');
    if (!notesGrid) return;

    // Remove drag mode class
    notesGrid.classList.remove('drag-mode');

    // Destroy Sortable instance
    if (sortableInstance) {
        sortableInstance.destroy();
        sortableInstance = null;
    }

    document.body.style.cursor = '';
}

function showDragIndicator() {
    // Remove existing indicator
    hideDragIndicator();

    const indicator = document.createElement('div');
    indicator.className = 'drag-indicator';
    indicator.id = 'dragIndicator';
    indicator.innerHTML = '<i class="fas fa-arrows-alt"></i> ড্র্যাগ মোড সক্রিয়';

    document.body.appendChild(indicator);
}

function hideDragIndicator() {
    const indicator = document.getElementById('dragIndicator');
    if (indicator) {
        indicator.remove();
    }
}

function resetNotesOrder() {
    if (originalNotesOrder.length > 0) {
        notes = [...originalNotesOrder];
        saveNotes();
        updateNotesDisplay();
        showNotification('রিসেট', 'নোটের ক্রম পূর্বের অবস্থায় ফিরিয়ে আনা হয়েছে', 'info');
    }
}

// ===== HELP MODAL FUNCTIONS =====
function openHelpModal() {
    const helpModal = document.getElementById('helpModal');
    if (helpModal) {
        helpModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeHelpModal() {
    const helpModal = document.getElementById('helpModal');
    if (helpModal) {
        helpModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function toggleHelpFullscreen() {
    const helpModal = document.getElementById('helpModal');
    if (helpModal) {
        helpModal.classList.toggle('fullscreen');
    }
}

// ===== KEYBOARD SHORTCUTS =====
function handleKeyboardShortcuts(event) {
    // Escape key to close modals
    if (event.key === 'Escape') {
        if (noteModal.classList.contains('active')) closeNoteModal();
        if (searchModal.classList.contains('active')) closeSearchModal();
        if (emojiModal.classList.contains('active')) closeEmojiModal();
        if (document.getElementById('helpModal').style.display === 'flex') closeHelpModal();
    }

    // Ctrl+S to save note
    if (event.ctrlKey && event.key === 's' && noteModal.classList.contains('active')) {
        event.preventDefault();
        saveNote(false);
    }

    // Ctrl+F to open search
    if (event.ctrlKey && event.key === 'f' && !noteModal.classList.contains('active')) {
        event.preventDefault();
        openSearchModal();
    }

    // Ctrl+D to duplicate current note (when editing)
    if (event.ctrlKey && event.key === 'd' && noteModal.classList.contains('active') && currentNoteId) {
        event.preventDefault();
        duplicateNote(currentNoteId);
    }

    // Alt+D to toggle drag mode
    if (event.altKey && event.key === 'd' && !noteModal.classList.contains('active')) {
        event.preventDefault();
        toggleDragMode();
    }
}

// ===== DOWNLOAD FUNCTIONS =====
function downloadNoteAsTxt() {
    if (!currentNoteId) {
        showNotification('ত্রুটি', 'কোন নোট নির্বাচিত নেই', 'error');
        return;
    }

    const note = notes.find(n => n.id === currentNoteId);
    if (!note) {
        showNotification('ত্রুটি', 'নোট খুঁজে পাওয়া যায়নি', 'error');
        return;
    }

    // Check if note is encrypted and needs password
    if (note.encrypted && !note.tempDecryptedContent && !note.tempDecryptedTabs) {
        showNotification('ত্রুটি', 'এনক্রিপ্টেড নোট ডাউনলোড করার জন্য প্রথমে পাসওয়ার্ড দিয়ে খুলুন', 'error');
        return;
    }

    // Get note content
    let content = '';

    // Add title
    if (note.title) {
        content += `${note.title}\n`;
        content += '='.repeat(note.title.length) + '\n\n';
    }

    // Add metadata
    const createdDate = new Date(note.createdAt).toLocaleDateString('bn-BD');
    const updatedDate = new Date(note.updatedAt).toLocaleDateString('bn-BD');
    content += `তৈরি: ${createdDate}\n`;
    content += `আপডেট: ${updatedDate}\n`;

    if (note.priority) {
        const priorityText = {
            'low': 'কম',
            'medium': 'মাধ্যম',
            'high': 'উচ্চ',
            'urgent': 'জরুরি'
        };
        content += `অগ্রাধিকার: ${priorityText[note.priority] || note.priority}\n`;
    }

    content += '\n' + '-'.repeat(50) + '\n\n';

    // Add content from tabs or main content
    let tabsToUse = note.tabs;
    let contentToUse = note.content;

    // Use decrypted content if available (for encrypted notes)
    if (note.encrypted) {
        if (note.tempDecryptedTabs) {
            tabsToUse = note.tempDecryptedTabs;
        }
        if (note.tempDecryptedContent) {
            contentToUse = note.tempDecryptedContent;
        }
    }

    if (tabsToUse && tabsToUse.length > 0) {
        tabsToUse.forEach((tab, index) => {
            if (tab.title) {
                content += `[${tab.title}]\n\n`;
            }

            // Remove HTML tags and convert to plain text
            const plainText = stripHtmlTags(tab.content || '');
            if (plainText.trim()) {
                content += plainText + '\n\n';
            }

            if (index < tabsToUse.length - 1) {
                content += '-'.repeat(30) + '\n\n';
            }
        });
    } else if (contentToUse) {
        // Remove HTML tags and convert to plain text
        const plainText = stripHtmlTags(contentToUse);
        content += plainText;
    }

    // Create and download file
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${note.title || 'নোট'}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('সফল', 'নোট TXT ফাইল হিসেবে ডাউনলোড হয়েছে', 'success');
}

function stripHtmlTags(html) {
    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Convert common HTML elements to plain text equivalents
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach(p => {
        p.innerHTML = p.innerHTML + '\n\n';
    });

    const breaks = tempDiv.querySelectorAll('br');
    breaks.forEach(br => {
        br.outerHTML = '\n';
    });

    const headers = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headers.forEach(h => {
        const level = parseInt(h.tagName.charAt(1));
        const prefix = '#'.repeat(level) + ' ';
        h.innerHTML = prefix + h.innerHTML + '\n\n';
    });

    const lists = tempDiv.querySelectorAll('ul, ol');
    lists.forEach(list => {
        const items = list.querySelectorAll('li');
        items.forEach((item, index) => {
            const bullet = list.tagName === 'UL' ? '• ' : `${index + 1}. `;
            item.innerHTML = bullet + item.innerHTML + '\n';
        });
        list.innerHTML = list.innerHTML + '\n';
    });

    // Get text content and clean up extra whitespace
    return tempDiv.textContent || tempDiv.innerText || '';
}

// ===== AUTO SAVE FUNCTIONS =====
function handleContentChange(newValue) {
    if (newValue !== lastSavedContent) {
        hasUnsavedChanges = true;
        updateAutoSaveStatus();
        resetAutoSaveTimer();
    }
}

function resetAutoSaveTimer() {
    clearAutoSaveTimer();
    if (autoSaveEnabled && hasUnsavedChanges) {
        if (autoSaveInterval === 0) {
            // Real-time save - save immediately
            performAutoSave();
        } else {
            // Delayed save with timer
            autoSaveTimer = setTimeout(() => {
                performAutoSave();
            }, autoSaveInterval);
        }
    }
}

function clearAutoSaveTimer() {
    if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = null;
    }
}

function performAutoSave() {
    if (!hasUnsavedChanges || !currentNoteId && !joditEditor.value.trim()) {
        return;
    }

    // Save note data without updating display if modal is open
    const isModalOpen = document.body.classList.contains('modal-open');
    const success = saveNote(true, isModalOpen); // true indicates auto-save, second param prevents display update
    if (success) {
        showAutoSaveNotification();
    }
}

function showAutoSaveNotification() {
    // Remove any existing auto-save indicators
    const existingIndicators = document.querySelectorAll('.auto-save-indicator');
    existingIndicators.forEach(indicator => indicator.remove());

    // Create a subtle auto-save indicator
    const indicator = document.createElement('div');
    indicator.className = 'auto-save-indicator';
    indicator.innerHTML = '<i class="fas fa-check"></i> অটো সেভ হয়েছে';

    // Position it at the top of the page
    document.body.appendChild(indicator);

    // Start fade-out animation after 1.5 seconds
    setTimeout(() => {
        if (indicator.parentElement) {
            indicator.classList.add('fade-out');
        }
    }, 1500);

    // Remove after fade-out animation completes
    setTimeout(() => {
        if (indicator.parentElement) {
            indicator.remove();
        }
    }, 2000);
}

function updateAutoSaveStatus() {
    // Update modal title to show unsaved changes
    const title = modalTitle.textContent;
    if (hasUnsavedChanges && !title.includes('*')) {
        if (autoSaveInterval === 0) {
            // For real-time save, don't show asterisk as it saves immediately
            modalTitle.textContent = title;
        } else {
            modalTitle.textContent = title + ' *';
        }
    } else if (!hasUnsavedChanges && title.includes('*')) {
        modalTitle.textContent = title.replace(' *', '');
    }

    // Update auto save status indicator if exists
    const autoSaveStatus = document.getElementById('autoSaveStatus');
    if (autoSaveStatus) {
        if (autoSaveInterval === 0) {
            autoSaveStatus.textContent = 'রিয়েল টাইম সেভ';
            autoSaveStatus.className = 'auto-save-status real-time';
        } else if (autoSaveEnabled) {
            autoSaveStatus.textContent = `অটো সেভ: ${autoSaveInterval / 1000}সে`;
            autoSaveStatus.className = 'auto-save-status enabled';
        } else {
            autoSaveStatus.textContent = 'অটো সেভ বন্ধ';
            autoSaveStatus.className = 'auto-save-status disabled';
        }
    }
}

function toggleAutoSave() {
    autoSaveEnabled = !autoSaveEnabled;
    if (!autoSaveEnabled) {
        clearAutoSaveTimer();
    } else if (hasUnsavedChanges) {
        resetAutoSaveTimer();
    }

    // Update UI to show auto-save status
    updateAutoSaveButton();
}

function updateAutoSaveButton() {
    const autoSaveBtn = document.getElementById('autoSaveBtn');
    if (autoSaveBtn) {
        if (autoSaveEnabled) {
            autoSaveBtn.classList.add('active');
            autoSaveBtn.title = 'অটো সেভ বন্ধ করুন';
            autoSaveBtn.innerHTML = '<i class="fas fa-save"></i>';
        } else {
            autoSaveBtn.classList.remove('active');
            autoSaveBtn.title = 'অটো সেভ চালু করুন';
            autoSaveBtn.innerHTML = '<i class="far fa-save"></i>';
        }
    }
}

// ===== BACKUP & RESTORE FUNCTIONS =====

// Debug function to check bookmark status
function debugBookmarkStatus() {
    console.log('=== BOOKMARK DEBUG INFO ===');
    console.log('Bookmark Manager exists:', !!window.bookmarkManager);

    if (window.bookmarkManager) {
        console.log('Bookmarks in memory:', window.bookmarkManager.bookmarks.length);
        console.log('First bookmark:', window.bookmarkManager.bookmarks[0]);
    }

    const localStorageBookmarks = localStorage.getItem('smartNoteBookmarks');
    console.log('Bookmarks in localStorage:', localStorageBookmarks ? 'YES' : 'NO');

    if (localStorageBookmarks) {
        try {
            const parsed = JSON.parse(localStorageBookmarks);
            console.log('Parsed bookmarks count:', parsed.length);
            console.log('First parsed bookmark:', parsed[0]);
        } catch (e) {
            console.error('Error parsing localStorage bookmarks:', e);
        }
    }

    console.log('All localStorage keys:', Object.keys(localStorage));
    console.log('=== END BOOKMARK DEBUG ===');
}

// Test bookmark backup functionality
function testBookmarkBackup() {
    console.log('=== TESTING BOOKMARK BACKUP ===');

    // Create test bookmarks if none exist
    if (!localStorage.getItem('smartNoteBookmarks')) {
        const testBookmarks = [
            {
                id: 'test-1',
                title: 'টেস্ট বুকমার্ক ১',
                url: 'https://example1.com',
                category: 'test',
                createdAt: new Date().toISOString()
            },
            {
                id: 'test-2',
                title: 'টেস্ট বুকমার্ক ২',
                url: 'https://example2.com',
                category: 'test',
                createdAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('smartNoteBookmarks', JSON.stringify(testBookmarks));
        console.log('Created test bookmarks');
    }

    // Force bookmark manager to save
    if (window.bookmarkManager) {
        window.bookmarkManager.forceSaveAllData();
    }

    // Check localStorage
    debugBookmarkStatus();

    // Try creating backup
    console.log('Attempting to create backup...');
    createBackup();
}
async function createBackup() {
    try {
        // Ensure bookmark manager is initialized and has saved data
        if (window.bookmarkManager) {
            window.bookmarkManager.forceSaveAllData();
            console.log('Forced bookmark save before backup. Bookmarks count:', window.bookmarkManager.bookmarks.length);
        } else {
            console.warn('Bookmark manager not initialized yet');
        }
        // Collect all localStorage data
        const allLocalStorageData = {};
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                allLocalStorageData[key] = localStorage.getItem(key);
            }
        }

        // Debug: Check if bookmarks exist in localStorage
        console.log('localStorage keys:', Object.keys(allLocalStorageData));
        console.log('Bookmarks in localStorage:', allLocalStorageData.smartNoteBookmarks ? 'YES' : 'NO');
        if (allLocalStorageData.smartNoteBookmarks) {
            try {
                const bookmarks = JSON.parse(allLocalStorageData.smartNoteBookmarks);
                console.log('Number of bookmarks:', bookmarks.length);
            } catch (e) {
                console.error('Error parsing bookmarks:', e);
            }
        }

        // Prepare comprehensive backup data
        const backupData = {
            // Core data
            notes: notes,
            notifications: notifications,
            attachedFiles: Array.from(attachedFiles.entries()),

            // All settings from localStorage
            localStorage: allLocalStorageData,

            // Specific settings for backward compatibility
            settings: {
                autoSaveEnabled: autoSaveEnabled,
                autoSaveInterval: autoSaveInterval,
                theme: localStorage.getItem('noteAppTheme') || 'dark',
                colorTheme: localStorage.getItem('noteAppColorTheme') || 'blue',
                fontSize: localStorage.getItem('noteAppFontSize') || '18',
                fontFamily: localStorage.getItem('noteAppFontFamily') || 'Kalpurush',
                fontWeight: localStorage.getItem('noteAppFontWeight') || '400',
                sortOption: localStorage.getItem('noteSortOption') || 'updated',
                notificationVolume: localStorage.getItem('noteAppNotificationVolume') || '1.0',
                spellCheck: localStorage.getItem('noteAppSpellCheck') || 'true',
                pathDisplayState: localStorage.getItem('pathDisplayState') || 'hidden'
            },

            // Editor tabs if any
            editorTabs: editorTabs,
            currentActiveTabId: currentActiveTabId,

            // Backup metadata
            timestamp: new Date().toISOString(),
            version: '2.0',
            backupType: 'complete',
            totalItems: {
                notes: notes.length,
                notifications: notifications.length,
                attachments: attachedFiles.size,
                settings: Object.keys(allLocalStorageData).length,
                bookmarks: allLocalStorageData.smartNoteBookmarks ?
                    JSON.parse(allLocalStorageData.smartNoteBookmarks).length : 0
            }
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const currentDate = new Date().toISOString().split('T')[0];
        const defaultFileName = `notes-backup-${currentDate}.json`;

        // Check if File System Access API is supported
        if ('showSaveFilePicker' in window) {
            try {
                // Use modern File System Access API
                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: defaultFileName,
                    types: [{
                        description: 'JSON ব্যাকআপ ফাইল',
                        accept: {
                            'application/json': ['.json']
                        }
                    }]
                });

                const writable = await fileHandle.createWritable();
                await writable.write(dataStr);
                await writable.close();

                const totalItems = backupData.totalItems;
                const backupSummary = `${totalItems.notes}টি নোট, ${totalItems.notifications}টি নোটিফিকেশন, ${totalItems.bookmarks}টি বুকমার্ক, ${totalItems.attachments}টি সংযুক্তি এবং ${totalItems.settings}টি সেটিংস`;
                showNotification('সফল', 'সম্পূর্ণ প্রজেক্ট ব্যাকআপ সংরক্ষিত হয়েছে', 'success');
                addNotification('সম্পূর্ণ ব্যাকআপ তৈরি', `সব ডেটা ব্যাকআপ করা হয়েছে: ${backupSummary}`, 'success', 'complete_backup_created');

            } catch (err) {
                if (err.name !== 'AbortError') {
                    console.error('File save error:', err);
                    // Fallback to download
                    fallbackDownload(dataStr, defaultFileName);
                }
                // If AbortError, user cancelled - do nothing
            }
        } else {
            // Fallback for browsers that don't support File System Access API
            fallbackDownload(dataStr, defaultFileName);
        }
    } catch (error) {
        console.error('Backup error:', error);
        showNotification('ত্রুটি', 'ব্যাকআপ তৈরিতে সমস্যা হয়েছে', 'error');
    }
}

function fallbackDownload(dataStr, fileName) {
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = fileName;
    link.click();

    // Parse backup data to get summary
    try {
        const backupData = JSON.parse(dataStr);
        const totalItems = backupData.totalItems;
        const backupSummary = `${totalItems.notes}টি নোট, ${totalItems.notifications}টি নোটিফিকেশন, ${totalItems.bookmarks}টি বুকমার্ক, ${totalItems.attachments}টি সংযুক্তি এবং ${totalItems.settings}টি সেটিংস`;
        showNotification('সফল', 'সম্পূর্ণ প্রজেক্ট ব্যাকআপ ডাউনলোড হয়েছে', 'success');
        addNotification('সম্পূর্ণ ব্যাকআপ তৈরি', `সব ডেটা ব্যাকআপ করা হয়েছে: ${backupSummary}`, 'success', 'complete_backup_created');
    } catch (error) {
        showNotification('সফল', 'ব্যাকআপ ফাইল ডাউনলোড হয়েছে', 'success');
        addNotification('ব্যাকআপ তৈরি', 'সব নোট এবং সেটিংস ব্যাকআপ করা হয়েছে', 'success', 'backup_created');
    }

    URL.revokeObjectURL(link.href);
}

function restoreFromBackup(backupData) {
    try {
        // Validate backup data structure
        if (!backupData || typeof backupData !== 'object') {
            throw new Error('Invalid backup data format');
        }

        console.log('Backup data received:', backupData);

        let restoredItems = [];

        // Restore notes with validation
        if (backupData.notes && Array.isArray(backupData.notes)) {
            // Validate each note structure
            const validNotes = backupData.notes.filter(note => {
                return note &&
                       typeof note === 'object' &&
                       typeof note.id === 'string' &&
                       typeof note.title === 'string' &&
                       typeof note.content === 'string';
            });

            if (validNotes.length > 0) {
                notes = validNotes;
                saveNotesToFile();
                restoredItems.push(`${validNotes.length}টি নোট`);
            }
        }

        // Restore notifications with validation
        if (backupData.notifications && Array.isArray(backupData.notifications)) {
            const validNotifications = backupData.notifications.filter(notification => {
                return notification &&
                       typeof notification === 'object' &&
                       typeof notification.id === 'string' &&
                       typeof notification.title === 'string';
            });

            if (validNotifications.length > 0) {
                notifications = validNotifications;
                saveNotifications();
                restoredItems.push(`${validNotifications.length}টি নোটিফিকেশন`);
            }
        }

        // Restore settings with validation
        if (backupData.settings && typeof backupData.settings === 'object') {
            const settings = backupData.settings;
            let settingsRestored = 0;

            if (settings.theme && typeof settings.theme === 'string') {
                localStorage.setItem('noteAppTheme', settings.theme);
                settingsRestored++;
            }
            if (settings.fontSize && typeof settings.fontSize === 'string') {
                localStorage.setItem('noteAppFontSize', settings.fontSize);
                settingsRestored++;
            }
            if (settings.fontFamily && typeof settings.fontFamily === 'string') {
                localStorage.setItem('noteAppFontFamily', settings.fontFamily);
                settingsRestored++;
            }
            if (settings.fontWeight && typeof settings.fontWeight === 'string') {
                localStorage.setItem('noteAppFontWeight', settings.fontWeight);
                settingsRestored++;
            }
            if (settings.sortOption && typeof settings.sortOption === 'string') {
                localStorage.setItem('noteSortOption', settings.sortOption);
                settingsRestored++;
            }

            if (settingsRestored > 0) {
                restoredItems.push('সেটিংস');
            }
        }

        // Restore attached files with validation
        if (backupData.attachedFiles && Array.isArray(backupData.attachedFiles)) {
            try {
                const validAttachedFiles = backupData.attachedFiles.filter(item => {
                    return Array.isArray(item) && item.length === 2 && typeof item[0] === 'string';
                });

                if (validAttachedFiles.length > 0) {
                    attachedFiles = new Map(validAttachedFiles);
                    restoredItems.push(`${validAttachedFiles.length}টি সংযুক্ত ফাইল`);
                }
            } catch (fileError) {
                console.warn('Error restoring attached files:', fileError);
            }
        }

        // Restore localStorage data (including bookmarks and other settings)
        if (backupData.localStorage && typeof backupData.localStorage === 'object') {
            let localStorageRestored = 0;
            Object.keys(backupData.localStorage).forEach(key => {
                try {
                    localStorage.setItem(key, backupData.localStorage[key]);
                    localStorageRestored++;
                    console.log(`Restored localStorage key: ${key}`);
                } catch (error) {
                    console.error(`Error restoring localStorage key ${key}:`, error);
                }
            });

            if (localStorageRestored > 0) {
                restoredItems.push(`${localStorageRestored}টি সেটিংস এবং কনফিগারেশন`);
            }

            // Specifically handle bookmarks restoration
            if (backupData.localStorage.smartNoteBookmarks) {
                try {
                    const bookmarksData = JSON.parse(backupData.localStorage.smartNoteBookmarks);
                    if (Array.isArray(bookmarksData)) {
                        restoredItems.push(`${bookmarksData.length}টি বুকমার্ক`);
                        console.log(`Restored ${bookmarksData.length} bookmarks`);

                        // Refresh bookmark manager if it exists
                        if (window.bookmarkManager) {
                            window.bookmarkManager.loadBookmarksFromStorage();
                            window.bookmarkManager.renderBookmarksInSearch();
                            window.bookmarkManager.renderBookmarksList();
                            window.bookmarkManager.updateStatistics();
                        }
                    }
                } catch (error) {
                    console.error('Error processing restored bookmarks:', error);
                }
            }

            // Handle bookmark folders
            if (backupData.localStorage.smartNoteBookmarkFolders) {
                try {
                    const foldersData = JSON.parse(backupData.localStorage.smartNoteBookmarkFolders);
                    if (Array.isArray(foldersData)) {
                        console.log(`Restored ${foldersData.length} bookmark folders`);
                    }
                } catch (error) {
                    console.error('Error processing restored bookmark folders:', error);
                }
            }
        }

        // Refresh the UI
        displayNotes();
        updateNotificationBadge();
        loadSettings();

        if (restoredItems.length > 0) {
            const restoredText = restoredItems.join(', ');
            showNotification('সফল', `পুনরুদ্ধার সম্পন্ন: ${restoredText}`, 'success');
            addNotification('ডেটা পুনরুদ্ধার', `ব্যাকআপ থেকে পুনরুদ্ধার করা হয়েছে: ${restoredText}`, 'success', 'data_restored');
        } else {
            showNotification('সতর্কতা', 'কোনো বৈধ ডেটা পাওয়া যায়নি', 'warning');
        }

    } catch (error) {
        console.error('Restore error:', error);
        showNotification('ত্রুটি', `ব্যাকআপ পুনরুদ্ধারে সমস্যা: ${error.message}`, 'error');
    }
}

function showRestoreModal() {
    restoreFileInput.onchange = handleRestoreFile;
    restoreFileInput.click();
}

function handleRestoreFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.name.endsWith('.json')) {
        showNotification('ত্রুটি', 'শুধুমাত্র JSON ফাইল সাপোর্টেড', 'error');
        return;
    }

    // Show loading notification
    showNotification('তথ্য', 'ব্যাকআপ ফাইল পড়া হচ্ছে...', 'info');

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            let fileContent = e.target.result;

            // Clean up any potential BOM (Byte Order Mark) characters
            if (fileContent.charCodeAt(0) === 0xFEFF) {
                fileContent = fileContent.slice(1);
            }

            // Remove any non-printable characters that might cause issues
            fileContent = fileContent.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

            // Validate JSON structure before parsing
            if (!fileContent.trim().startsWith('{') || !fileContent.trim().endsWith('}')) {
                throw new Error('Invalid JSON structure');
            }

            const backupData = JSON.parse(fileContent);

            // Validate backup data structure
            if (!backupData || typeof backupData !== 'object') {
                throw new Error('Invalid backup data structure');
            }

            // Check for required properties
            if (!backupData.hasOwnProperty('notes') || !backupData.hasOwnProperty('version')) {
                throw new Error('Missing required backup properties');
            }

            restoreFromBackup(backupData);

        } catch (error) {
            console.error('Restore error:', error);
            let errorMessage = 'ব্যাকআপ ফাইল ফরম্যাট সঠিক নয়';

            if (error.message.includes('Unexpected token')) {
                errorMessage = 'JSON ফাইল corrupted বা ভুল ফরম্যাটে আছে';
            } else if (error.message.includes('Invalid JSON structure')) {
                errorMessage = 'ফাইলটি সঠিক JSON ফরম্যাটে নেই';
            } else if (error.message.includes('Missing required')) {
                errorMessage = 'ব্যাকআপ ফাইলে প্রয়োজনীয় ডেটা নেই';
            }

            showNotification('ত্রুটি', errorMessage, 'error');
        }
    };

    reader.onerror = function(error) {
        console.error('File read error:', error);
        showNotification('ত্রুটি', 'ফাইল পড়তে সমস্যা হয়েছে', 'error');
    };

    // Use UTF-8 encoding explicitly
    reader.readAsText(file, 'UTF-8');

    // Clear the file input for next use
    event.target.value = '';
}

function restoreFromBackup(backupData) {
    if (!backupData || typeof backupData !== 'object') {
        showNotification('ত্রুটি', 'অবৈধ ব্যাকআপ ফাইল ফরম্যাট', 'error');
        return;
    }

    if (!backupData.notes || !Array.isArray(backupData.notes)) {
        showNotification('ত্রুটি', 'ব্যাকআপ ফাইলে নোট ডাটা পাওয়া যায়নি', 'error');
        return;
    }

    if (confirm('এটি বর্তমান সব ডাটা মুছে দিয়ে ব্যাকআপ থেকে রিস্টোর করবে। আপনি কি নিশ্চিত?')) {
        try {

            // Clear current data
            notes = [];
            notifications = [];
            attachedFiles.clear();

            // Clear current tabs
            editorTabs = [];
            currentActiveTabId = null;

            // Restore notes with backward compatibility and encryption support
            notes = backupData.notes.map(note => {
                // Ensure each note has required properties
                const restoredNote = {
                    id: note.id || generateId(),
                    title: note.title || '',
                    priority: note.priority || 'medium',
                    tags: note.tags || [],
                    reminder: note.reminder || null,
                    createdAt: note.createdAt || new Date().toISOString(),
                    updatedAt: note.updatedAt || new Date().toISOString(),
                    attachments: note.attachments || []
                };

                // Handle encrypted notes - preserve encryption
                if (note.encrypted) {
                    restoredNote.encrypted = true;
                    restoredNote.passwordHash = note.passwordHash;
                    restoredNote.encryptedContent = note.encryptedContent;

                    // Handle encrypted tabs
                    if (note.encryptedTabs) {
                        restoredNote.encryptedTabs = note.encryptedTabs;
                    }

                    // Don't restore decrypted content or tabs
                    // User will need to unlock with password
                } else {
                    // Handle regular (non-encrypted) notes
                    restoredNote.content = note.content || '';

                    // Handle tabs data - if no tabs, create default tab with content
                    if (note.tabs && Array.isArray(note.tabs) && note.tabs.length > 0) {
                        restoredNote.tabs = note.tabs;
                    } else if (note.content) {
                        // Create default tab for backward compatibility
                        restoredNote.tabs = [{
                            title: 'মূল নোট',
                            content: note.content
                        }];
                    } else {
                        restoredNote.tabs = [{
                            title: 'ট্যাব ১',
                            content: ''
                        }];
                    }
                }

                return restoredNote;
            });

            // Restore notifications if available
            if (backupData.notifications && Array.isArray(backupData.notifications)) {
                notifications = backupData.notifications;
            }

            // Restore all localStorage data if available (new format)
            if (backupData.localStorage && typeof backupData.localStorage === 'object') {
                // Clear existing localStorage (except for critical system data)
                const keysToPreserve = ['noteAppVersion', 'noteAppInstallDate'];
                const currentPreservedData = {};
                keysToPreserve.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) currentPreservedData[key] = value;
                });

                // Clear localStorage
                localStorage.clear();

                // Restore preserved data
                Object.entries(currentPreservedData).forEach(([key, value]) => {
                    localStorage.setItem(key, value);
                });

                // Restore all backup data
                Object.entries(backupData.localStorage).forEach(([key, value]) => {
                    if (!keysToPreserve.includes(key)) {
                        localStorage.setItem(key, value);
                    }
                });

                console.log('Restored localStorage data:', Object.keys(backupData.localStorage));

                // Specifically handle bookmarks restoration
                if (backupData.localStorage.smartNoteBookmarks) {
                    try {
                        const bookmarksData = JSON.parse(backupData.localStorage.smartNoteBookmarks);
                        if (Array.isArray(bookmarksData)) {
                            console.log(`Restored ${bookmarksData.length} bookmarks`);

                            // Refresh bookmark manager if it exists
                            if (window.bookmarkManager) {
                                window.bookmarkManager.loadBookmarksFromStorage();
                                window.bookmarkManager.renderBookmarksInSearch();
                                window.bookmarkManager.renderBookmarksList();
                                window.bookmarkManager.updateStatistics();
                            }
                        }
                    } catch (error) {
                        console.error('Error processing restored bookmarks:', error);
                    }
                }
            }

            // Restore header footer settings if available
            if (backupData.headerFooterSettings && typeof backupData.headerFooterSettings === 'object') {
                headerFooterSettings = { ...headerFooterSettings, ...backupData.headerFooterSettings };
                saveHeaderFooterSettings();
                console.log('Restored header footer settings');
            }

            // Restore editor tabs if available
            if (backupData.editorTabs && Array.isArray(backupData.editorTabs)) {
                editorTabs = backupData.editorTabs;
                currentActiveTabId = backupData.currentActiveTabId || null;
                console.log('Restored editor tabs:', editorTabs.length);
            }

            // Restore settings if available (backward compatibility)
            if (backupData.settings && typeof backupData.settings === 'object') {
                autoSaveEnabled = backupData.settings.autoSaveEnabled !== undefined ?
                    backupData.settings.autoSaveEnabled : true;
                autoSaveInterval = backupData.settings.autoSaveInterval !== undefined ?
                    backupData.settings.autoSaveInterval : 0;

                // Restore theme and font settings if not already restored from localStorage
                if (backupData.settings.theme && !backupData.localStorage) {
                    localStorage.setItem('noteAppTheme', backupData.settings.theme);
                    currentTheme = backupData.settings.theme;
                    applyTheme(backupData.settings.theme === 'auto' ?
                        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') :
                        backupData.settings.theme);
                }
                if (backupData.settings.fontSize) {
                    localStorage.setItem('noteAppFontSize', backupData.settings.fontSize);
                }
                if (backupData.settings.fontFamily) {
                    localStorage.setItem('noteAppFontFamily', backupData.settings.fontFamily);
                }
                if (backupData.settings.fontWeight) {
                    localStorage.setItem('noteAppFontWeight', backupData.settings.fontWeight);
                }
                if (backupData.settings.sortOption) {
                    localStorage.setItem('noteSortOption', backupData.settings.sortOption);
                }

                // Apply font settings
                loadSettings();
            }

            // Restore attached files if available
            if (backupData.attachedFiles && Array.isArray(backupData.attachedFiles)) {
                attachedFiles.clear();
                backupData.attachedFiles.forEach(([key, value]) => {
                    if (key && value) {
                        attachedFiles.set(key, value);
                    }
                });
            }

            // Save to localStorage
            saveNotes();
            saveNotifications();

            // Reload all settings and apply them
            loadSettings();

            // Apply theme if restored
            const restoredTheme = localStorage.getItem('noteAppTheme');
            if (restoredTheme) {
                currentTheme = restoredTheme;
                applyTheme(restoredTheme === 'auto' ?
                    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') :
                    restoredTheme);
            }

            // Apply font settings if restored
            applyFontSettings();

            // Update UI
            updateNotesDisplay();
            updateNotificationBadge();
            updateNotificationPanel();

            // Update dashboard if open
            const dashboardModal = document.getElementById('dashboardModal');
            if (dashboardModal && dashboardModal.style.display === 'flex') {
                updateDashboardStats();
                updateRecentActivity();
            }

            // Force update header and footer
            setTimeout(() => {
                applyHeaderFooterSettings();
                forceUpdateFooter();
            }, 500);

            // Create restore summary
            const restoreSummary = [];
            if (notes.length > 0) restoreSummary.push(`${notes.length}টি নোট`);
            if (notifications.length > 0) restoreSummary.push(`${notifications.length}টি নোটিফিকেশন`);
            if (attachedFiles.size > 0) restoreSummary.push(`${attachedFiles.size}টি সংযুক্তি`);

            // Add bookmarks count to summary
            if (backupData.localStorage && backupData.localStorage.smartNoteBookmarks) {
                try {
                    const bookmarksData = JSON.parse(backupData.localStorage.smartNoteBookmarks);
                    if (Array.isArray(bookmarksData) && bookmarksData.length > 0) {
                        restoreSummary.push(`${bookmarksData.length}টি বুকমার্ক`);
                    }
                } catch (error) {
                    console.error('Error counting restored bookmarks:', error);
                }
            }

            const settingsCount = backupData.localStorage ? Object.keys(backupData.localStorage).length :
                                 (backupData.settings ? Object.keys(backupData.settings).length : 0);
            if (settingsCount > 0) restoreSummary.push(`${settingsCount}টি সেটিংস`);

            const summaryText = restoreSummary.join(', ');

            showNotification('সফল', 'সম্পূর্ণ প্রজেক্ট রিস্টোর হয়েছে', 'success');
            addNotification('সম্পূর্ণ ডাটা রিস্টোর', `ব্যাকআপ থেকে রিস্টোর করা হয়েছে: ${summaryText}`, 'success', 'complete_data_restored');

        } catch (error) {
            console.error('Restore error:', error);
            showNotification('ত্রুটি', `ডাটা রিস্টোর করতে সমস্যা হয়েছে: ${error.message}`, 'error');
        }
    }
}

// ===== PRINT & PDF FUNCTIONS =====
function printNote(noteId) {
    const note = notes.find(n => n.id === noteId);
    if (!note) {
        showNotification('ত্রুটি', 'নোট পাওয়া যায়নি', 'error');
        return;
    }

    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent(note);

    printWindow.document.write(printContent);
    printWindow.document.close();

    printWindow.onload = function() {
        printWindow.print();
        printWindow.onafterprint = function() {
            printWindow.close();
        };
    };
}

function downloadNotePDF(noteId) {
    const note = notes.find(n => n.id === noteId);
    if (!note) {
        showNotification('ত্রুটি', 'নোট পাওয়া যায়নি', 'error');
        return;
    }

    // Create a temporary iframe for PDF generation
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.left = '-9999px';
    iframe.style.width = '210mm';
    iframe.style.height = '297mm';
    document.body.appendChild(iframe);

    const printContent = generatePrintContent(note, true);
    iframe.contentDocument.write(printContent);
    iframe.contentDocument.close();

    iframe.onload = function() {
        try {
            iframe.contentWindow.print();
            showNotification('তথ্য', 'PDF ডাউনলোড শুরু হয়েছে', 'info');
        } catch (error) {
            console.error('PDF generation error:', error);
            showNotification('ত্রুটি', 'PDF তৈরিতে সমস্যা হয়েছে', 'error');
        }

        setTimeout(() => {
            document.body.removeChild(iframe);
        }, 1000);
    };
}

function generatePrintContent(note, isPDF = false) {
    const preview = getTextPreview(note.content);
    const fullDate = formatDate(note.updatedAt);
    const priorityText = getPriorityText(note.priority || 'medium');
    const priorityEmoji = getPriorityEmoji(note.priority || 'medium');

    return `
        <!DOCTYPE html>
        <html lang="bn">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>নোট প্রিন্ট - ${preview.substring(0, 50)}</title>
            <link href="https://fonts.googleapis.com/css2?family=Kalpurush:wght@400&display=swap" rel="stylesheet">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Kalpurush', sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                    padding: 20mm;
                    background: white;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #4a6bdf;
                    padding-bottom: 20px;
                }

                .print-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #4a6bdf;
                    margin-bottom: 10px;
                }

                .print-meta {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                }

                .print-priority {
                    font-weight: bold;
                    color: ${getPriorityColor(note.priority || 'medium')};
                }

                .print-content {
                    margin-top: 30px;
                    min-height: 400px;
                }

                .print-content h1, .print-content h2, .print-content h3 {
                    color: #4a6bdf;
                    margin: 20px 0 10px 0;
                }

                .print-content p {
                    margin-bottom: 15px;
                }

                .print-content ul, .print-content ol {
                    margin: 15px 0 15px 30px;
                }

                .print-content li {
                    margin-bottom: 8px;
                }

                .print-footer {
                    margin-top: 50px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }

                @media print {
                    body {
                        padding: 15mm;
                    }

                    .print-header {
                        margin-bottom: 20px;
                    }

                    .print-content {
                        page-break-inside: avoid;
                    }
                }

                ${isPDF ? `
                    @page {
                        size: A4;
                        margin: 15mm;
                    }
                ` : ''}
            </style>
        </head>
        <body>
            <div class="print-header">
                <div class="print-title">📝 আমার স্মার্ট নোট</div>
                <div style="font-size: 14px; color: #666;">নোট প্রিন্ট কপি</div>
            </div>

            <div class="print-meta">
                <div>
                    <strong>অগ্রাধিকার:</strong>
                    <span class="print-priority">${priorityEmoji} ${priorityText}</span>
                </div>
                <div>
                    <strong>সর্বশেষ সংরক্ষন:</strong> ${fullDate}
                </div>
            </div>

            <div class="print-content">
                ${note.content}
            </div>

            <div class="print-footer">
                <p>এই নোটটি "আমার স্মার্ট নোট" অ্যাপ থেকে প্রিন্ট করা হয়েছে</p>
                <p>প্রিন্ট তারিখ: ${new Date().toLocaleDateString('bn-BD', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })}</p>
            </div>
        </body>
        </html>
    `;
}

function getPriorityColor(priority) {
    switch (priority) {
        case 'low': return '#28a745';
        case 'medium': return '#ffc107';
        case 'high': return '#fd7e14';
        case 'urgent': return '#dc3545';
        default: return '#ffc107';
    }
}

// ===== UTILITY FUNCTIONS =====
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

function getTextPreview(html) {
    try {
        if (typeof html !== 'string') {
            return '';
        }

        // Only remove specific malicious patterns, not all template literals
        let sanitizedHtml = html.replace(/\{[^}]*showDetailsData[^}]*\}/g, '');

        // Remove all img tags to prevent loading issues
        sanitizedHtml = sanitizedHtml.replace(/<img[^>]*>/gi, '');

        // Fix any broken alt attributes that might cause URL errors
        sanitizedHtml = sanitizedHtml.replace(/alt=""/g, 'alt="image"');

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = sanitizedHtml;
        return tempDiv.textContent || tempDiv.innerText || '';
    } catch (error) {
        console.warn('Error in getTextPreview:', error);
        return '';
    }
}

// ===== AUDIO CONTEXT MANAGEMENT =====
function initializeAudioContext() {
    if (!audioContextInitialized && !globalAudioContext) {
        try {
            globalAudioContext = new (window.AudioContext || window.webkitAudioContext)();
            audioContextInitialized = true;
            console.log('AudioContext initialized successfully');
        } catch (error) {
            console.warn('AudioContext not supported:', error);
        }
    }

    // Resume audio context if suspended
    if (globalAudioContext && globalAudioContext.state === 'suspended') {
        globalAudioContext.resume().then(() => {
            console.log('AudioContext resumed');
        }).catch(error => {
            console.warn('Failed to resume AudioContext:', error);
        });
    }
}

// ===== NOTIFICATION SYSTEM =====
function initializeNotificationSound() {
    // Initialize AudioContext only after user interaction
    if (!audioContextInitialized) {
        // Create a fallback notification sound
        notificationSound = {
            play: function() {
                console.log('Notification sound would play here (AudioContext not initialized)');
            }
        };
        return;
    }

    try {
        notificationSound = {
            play: function() {
                try {
                    // Resume audio context if suspended
                    if (globalAudioContext && globalAudioContext.state === 'suspended') {
                        globalAudioContext.resume().then(() => {
                            playNotificationTones();
                        });
                    } else {
                        playNotificationTones();
                    }
                } catch (error) {
                    console.log('Error playing notification sound:', error);
                }
            }
        };

        function playNotificationTones() {
            if (!globalAudioContext) return;

            // Create a more complex notification sound with multiple tones
            const playTone = (frequency, startTime, duration, volume) => {
                try {
                    const oscillator = globalAudioContext.createOscillator();
                    const gainNode = globalAudioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(globalAudioContext.destination);

                    oscillator.frequency.setValueAtTime(frequency, startTime);
                    oscillator.type = 'sine'; // Smoother sound

                    gainNode.gain.setValueAtTime(0, startTime);
                    gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

                    oscillator.start(startTime);
                    oscillator.stop(startTime + duration);
                } catch (error) {
                    console.log('Error playing tone:', error);
                }
            };

            const currentTime = globalAudioContext.currentTime;

            // Play a sequence of tones for a more prominent notification sound
            // Apply global volume setting to all tones
            const baseVolume = notificationVolume * 0.3; // Reduce volume to prevent being too loud

            // First tone - high pitch (maximum volume)
            playTone(1200, currentTime, 0.2, baseVolume * 1.0);

            // Second tone - medium-high pitch
            playTone(900, currentTime + 0.15, 0.2, baseVolume * 0.9);

            // Third tone - medium pitch for emphasis
            playTone(700, currentTime + 0.3, 0.25, baseVolume * 0.8);

            // Fourth tone - final emphasis
            playTone(1000, currentTime + 0.5, 0.15, baseVolume * 0.7);

            // Add a more prominent echo effect
            setTimeout(() => {
                if (globalAudioContext) {
                    playTone(900, globalAudioContext.currentTime, 0.12, baseVolume * 0.5);
                    setTimeout(() => {
                        if (globalAudioContext) {
                            playTone(700, globalAudioContext.currentTime, 0.1, baseVolume * 0.3);
                        }
                    }, 100);
                }
            }, 400);
        }

    } catch (e) {
        console.log('Audio not supported:', e);
        notificationSound = { play: function() { console.log('Notification sound would play here'); } };
    }
}

function loadNotifications() {
    const savedNotifications = localStorage.getItem('notifications');
    if (savedNotifications) {
        notifications = JSON.parse(savedNotifications);
    }
}

function saveNotifications() {
    localStorage.setItem('notifications', JSON.stringify(notifications));
}

function addNotification(title, message, type = 'info', action = null, targetId = null) {
    const notification = {
        id: generateId(),
        title: title,
        message: message,
        type: type,
        action: action,
        targetId: targetId,
        timestamp: new Date().toISOString(),
        read: false
    };

    notifications.unshift(notification);
    saveNotifications();
    updateNotificationBadge();
    updateNotificationPanel();

    // Play sound and animate bell
    if (notificationSound) {
        notificationSound.play();
    }

    notificationBell.classList.add('has-notifications');
    setTimeout(() => {
        notificationBell.classList.remove('has-notifications');
    }, 500);

    // Also show toast notification
    showNotification(title, message, type);
}

function updateNotificationBadge() {
    const unreadCount = notifications.filter(n => !n.read).length;
    notificationBadge.textContent = unreadCount;

    if (unreadCount > 0) {
        notificationBadge.classList.add('show');
        notificationBell.classList.add('has-notifications');
    } else {
        notificationBadge.classList.remove('show');
        notificationBell.classList.remove('has-notifications');
    }
}

function toggleNotificationPanel() {
    // Play sound when clicking notification bell
    if (notificationSound) {
        notificationSound.play();
    }

    if (notificationPanel.classList.contains('active')) {
        closeNotificationPanel();
    } else {
        openNotificationPanel();
    }
}

function openNotificationPanel() {
    notificationPanel.classList.add('active');
    document.body.style.overflow = 'hidden';
    updateNotificationPanel();

    // Mark all as read when panel is opened
    setTimeout(() => {
        markAllAsRead();
    }, 500);
}

function closeNotificationPanel() {
    notificationPanel.classList.remove('active');
    document.body.style.overflow = '';
}

function markAllAsRead() {
    notifications.forEach(n => n.read = true);
    saveNotifications();
    updateNotificationBadge();
    updateNotificationPanel();
}

function updateNotificationPanel() {
    if (notifications.length === 0) {
        notificationPanelBody.innerHTML = `
            <div class="no-notifications">
                <i class="fas fa-bell-slash"></i>
                <p>কোন নোটিফিকেশন নেই</p>
            </div>
        `;
        return;
    }

    notificationPanelBody.innerHTML = '';
    notifications.forEach(notification => {
        const notificationElement = createNotificationElement(notification);
        notificationPanelBody.appendChild(notificationElement);
    });
}

function createNotificationElement(notification) {
    const element = document.createElement('div');
    element.className = `notification-item ${notification.read ? 'read' : 'unread'} ${notification.type}`;
    element.onclick = () => handleNotificationClick(notification);

    const fullDateTime = getTimeAgo(notification.timestamp);
    const relativeTime = getRelativeTime(notification.timestamp);
    const actionText = getActionText(notification.action);

    element.innerHTML = `
        <div class="notification-item-header">
            <h4 class="notification-item-title">${notification.title}</h4>
            <span class="notification-item-time" title="${fullDateTime}">${relativeTime}</span>
        </div>
        <p class="notification-item-message">${notification.message}</p>
        <div class="notification-item-actions">
            <span class="notification-item-type">${actionText}</span>
            <button class="notification-item-delete" onclick="event.stopPropagation(); deleteNotification('${notification.id}')" title="ডিলেট করুন">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="notification-item-full-time">
            <small>${fullDateTime}</small>
        </div>
    `;

    return element;
}

function handleNotificationClick(notification) {
    // Mark as read
    notification.read = true;
    saveNotifications();
    updateNotificationBadge();
    updateNotificationPanel();

    // Handle different actions
    switch (notification.action) {
        case 'note_created':
        case 'note_updated':
            if (notification.targetId) {
                closeNotificationPanel();
                editNote(notification.targetId);
            }
            break;
        case 'note_deleted':
            // Show a message that note was deleted
            showNotification('তথ্য', 'এই নোটটি ইতিমধ্যে ডিলেট করা হয়েছে', 'info');
            break;
        case 'sample_loaded':
            closeNotificationPanel();
            // Scroll to notes grid
            document.getElementById('notesGrid').scrollIntoView({ behavior: 'smooth' });
            break;
        case 'welcome':
        case 'feature':
            // Just close the panel
            closeNotificationPanel();
            break;
        default:
            closeNotificationPanel();
    }
}

function deleteNotification(notificationId) {
    notifications = notifications.filter(n => n.id !== notificationId);
    saveNotifications();
    updateNotificationBadge();
    updateNotificationPanel();
    showNotification('সফল', 'নোটিফিকেশন ডিলেট হয়েছে', 'success');
}

function clearAllNotificationItems() {
    if (notifications.length === 0) {
        showNotification('তথ্য', 'কোন নোটিফিকেশন নেই', 'info');
        return;
    }

    if (confirm('সব নোটিফিকেশন ক্লিয়ার করতে চান?')) {
        notifications = [];
        saveNotifications();
        updateNotificationBadge();
        updateNotificationPanel();
        showNotification('সফল', 'সব নোটিফিকেশন ক্লিয়ার হয়েছে', 'success');
    }
}

function getTimeAgo(timestamp) {
    const time = new Date(timestamp);

    // Return full date and time
    const dateOptions = {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };

    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    };

    const formattedDate = time.toLocaleDateString('bn-BD', dateOptions);
    const formattedTime = time.toLocaleTimeString('bn-BD', timeOptions);

    return `${formattedDate}, ${formattedTime}`;
}

function getRelativeTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now - time) / (1000 * 60));

    if (diffInMinutes < 1) return 'এখনই';
    if (diffInMinutes < 60) return `${diffInMinutes} মিনিট আগে`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} ঘন্টা আগে`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} দিন আগে`;

    return time.toLocaleDateString('bn-BD');
}

function getActionText(action) {
    switch (action) {
        case 'note_created': return 'নোট তৈরি';
        case 'note_updated': return 'নোট আপডেট';
        case 'note_deleted': return 'নোট ডিলেট';
        case 'sample_loaded': return 'স্যাম্পল ডাটা';
        case 'welcome': return 'স্বাগতম';
        case 'feature': return 'নতুন ফিচার';
        default: return 'সাধারণ';
    }
}

// ===== SAMPLE DATA FUNCTIONS =====
function loadSampleData() {
    if (confirm('এটি বর্তমান সব নোট মুছে দিয়ে স্যাম্পল ডাটা লোড করবে। আপনি কি নিশ্চিত?')) {
        notes = [...demoNotes];
        saveNotes();
        updateNotesDisplay();
        showNotification('সফল', 'স্যাম্পল ডাটা লোড হয়েছে', 'success');
        addNotification('স্যাম্পল ডাটা লোড', `${notes.length}টি ডেমো নোট সফলভাবে লোড হয়েছে`, 'success', 'sample_loaded');

        // Show sample notifications
        setTimeout(() => {
            addNotification('স্বাগতম', 'নোট অর্গানাইজারে আপনাকে স্বাগতম! 🎉', 'success', 'welcome');
        }, 1000);

        setTimeout(() => {
            addNotification('টিপস', 'প্রায়োরিটি অনুযায়ী নোট সাজানো হয়েছে', 'info', 'feature');
        }, 2000);

        setTimeout(() => {
            addNotification('সাহায্য', 'Ctrl+F দিয়ে সার্চ করুন, Ctrl+S দিয়ে সেভ করুন', 'info', 'feature');
        }, 3000);
    }
}

function clearSampleData() {
    if (confirm('এটি সব নোট, নোটিফিকেশন এবং সেমপল ডাটা মুছে দেবে। আপনি কি নিশ্চিত?')) {
        // Clear all notes
        notes = [];
        saveNotes();

        // Clear all notifications directly without confirmation
        notifications = [];
        saveNotifications();
        updateNotificationBadge();
        updateNotificationPanel();

        // Clear notification panel
        const notificationPanelBody = document.getElementById('notificationPanelBody');
        if (notificationPanelBody) {
            notificationPanelBody.innerHTML = `
                <div class="no-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>কোন নোটিফিকেশন নেই</p>
                </div>
            `;
        }

        // Reset notification badge
        const notificationBadge = document.getElementById('notificationBadge');
        if (notificationBadge) {
            notificationBadge.textContent = '0';
            notificationBadge.style.display = 'none';
        }

        // Clear any stored notification data
        localStorage.removeItem('notifications');
        localStorage.removeItem('notificationCount');

        // Update display
        updateNotesDisplay();

        // Show success notification
        showNotification('সফল', 'সব সেমপল ডাটা মুছে ফেলা হয়েছে', 'success');

        // Add a single notification about clearing data
        setTimeout(() => {
            addNotification('ডাটা ক্লিয়ার', 'সব সেমপল নোট ও নোটিফিকেশন মুছে ফেলা হয়েছে', 'info', 'data_cleared');
        }, 500);
    }
}

function getPriorityEmoji(priority) {
    switch (priority) {
        case 'low': return '🟢';
        case 'medium': return '🟡';
        case 'high': return '🟠';
        case 'urgent': return '🔴';
        default: return '🟡';
    }
}

function getPriorityText(priority) {
    switch (priority) {
        case 'low': return 'কম';
        case 'medium': return 'মাধ্যম';
        case 'high': return 'উচ্চ';
        case 'urgent': return 'জরুরি';
        default: return 'মাধ্যম';
    }
}

// ===== DASHBOARD FUNCTIONALITY =====
let weeklyTrendChart = null;
let priorityChart = null;
let monthlyChart = null;
let lengthChart = null;
let dailyActivityChart = null;
let categoryChart = null;

function openDashboardModal() {
    const modal = document.getElementById('dashboardModal');
    modal.style.display = 'flex';
    modal.classList.add('show');

    console.log('Opening dashboard modal...');

    // Update statistics immediately
    updateDashboardStats();

    // Update recent activity
    updateRecentActivity();

    // Initialize charts with a longer delay to ensure DOM is ready
    setTimeout(() => {
        console.log('Initializing charts...');
        initializeCharts();
    }, 500);
}

function closeDashboardModal() {
    const modal = document.getElementById('dashboardModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';

        // Destroy charts to prevent memory leaks
        if (weeklyTrendChart) {
            weeklyTrendChart.destroy();
            weeklyTrendChart = null;
        }
        if (priorityChart) {
            priorityChart.destroy();
            priorityChart = null;
        }
        if (monthlyChart) {
            monthlyChart.destroy();
            monthlyChart = null;
        }
        if (lengthChart) {
            lengthChart.destroy();
            lengthChart = null;
        }
        if (dailyActivityChart) {
            dailyActivityChart.destroy();
            dailyActivityChart = null;
        }
        if (categoryChart) {
            categoryChart.destroy();
            categoryChart = null;
        }
    }, 300);
}

function updateDashboardStats() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());

    console.log('Updating dashboard stats...', { totalNotes: notes.length });

    // Total notes
    const totalNotes = notes.length;
    const totalElement = document.getElementById('totalNotesCount');
    if (totalElement) {
        totalElement.textContent = totalNotes;
    }

    // Today's notes
    const todayNotes = notes.filter(note => {
        const noteDate = new Date(note.createdAt);
        return noteDate >= today;
    }).length;
    const todayElement = document.getElementById('todayNotesCount');
    if (todayElement) {
        todayElement.textContent = todayNotes;
    }

    // This week's notes
    const weekNotes = notes.filter(note => {
        const noteDate = new Date(note.createdAt);
        return noteDate >= weekStart;
    }).length;
    const weekElement = document.getElementById('weekNotesCount');
    if (weekElement) {
        weekElement.textContent = weekNotes;
    }

    // Urgent notes
    const urgentNotes = notes.filter(note => note.priority === 'urgent').length;
    const urgentElement = document.getElementById('urgentNotesCount');
    if (urgentElement) {
        urgentElement.textContent = urgentNotes;
    }

    console.log('Dashboard stats updated:', { totalNotes, todayNotes, weekNotes, urgentNotes });
}

function initializeCharts() {
    initializeWeeklyTrendChart();
    initializePriorityChart();
    initializeMonthlyChart();
    initializeLengthChart();
    initializeDailyActivityChart();
    initializeCategoryChart();
}

function initializeWeeklyTrendChart() {
    const ctx = document.getElementById('weeklyTrendChart');
    if (!ctx) {
        console.log('Weekly trend chart canvas not found');
        return;
    }
    console.log('Initializing weekly trend chart...');

    // Get last 7 days data
    const last7Days = [];
    const noteCounts = [];

    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart);
        dayEnd.setDate(dayEnd.getDate() + 1);

        const dayNotes = notes.filter(note => {
            const noteDate = new Date(note.createdAt);
            return noteDate >= dayStart && noteDate < dayEnd;
        }).length;

        last7Days.push(formatBengaliDate(date, 'full'));
        noteCounts.push(dayNotes);
    }

    if (weeklyTrendChart) {
        weeklyTrendChart.destroy();
    }

    weeklyTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: last7Days,
            datasets: [{
                label: 'নোট সংখ্যা',
                data: noteCounts,
                borderColor: 'rgba(255, 255, 255, 0.8)',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(255, 255, 255, 1)',
                pointBorderColor: 'rgba(255, 255, 255, 1)',
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 14
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)',
                        font: {
                            family: 'Kalpurush'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                },
                x: {
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.8)',
                        font: {
                            family: 'Kalpurush'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                }
            }
        }
    });
}

function initializePriorityChart() {
    const ctx = document.getElementById('priorityChart');
    if (!ctx) {
        console.log('Priority chart canvas not found');
        return;
    }
    console.log('Initializing priority chart...');

    // Count notes by priority
    const priorityCounts = {
        low: notes.filter(note => note.priority === 'low').length,
        medium: notes.filter(note => note.priority === 'medium').length,
        high: notes.filter(note => note.priority === 'high').length,
        urgent: notes.filter(note => note.priority === 'urgent').length
    };

    if (priorityChart) {
        priorityChart.destroy();
    }

    priorityChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['কম 🟢', 'মাধ্যম 🟡', 'উচ্চ 🟠', 'জরুরি 🔴'],
            datasets: [{
                data: [priorityCounts.low, priorityCounts.medium, priorityCounts.high, priorityCounts.urgent],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(253, 126, 20, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 12
                        },
                        padding: 20
                    }
                }
            }
        }
    });
}

function initializeMonthlyChart() {
    const ctx = document.getElementById('monthlyChart');
    if (!ctx) {
        console.log('Monthly chart canvas not found');
        return;
    }
    console.log('Initializing monthly chart...');

    // Get last 6 months data
    const last6Months = [];
    const monthlyNoteCounts = [];

    for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        const monthNotes = notes.filter(note => {
            const noteDate = new Date(note.createdAt);
            return noteDate >= monthStart && noteDate <= monthEnd;
        }).length;

        const bengaliMonths = ['জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
                              'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'];

        last6Months.push(bengaliMonths[date.getMonth()]);
        monthlyNoteCounts.push(monthNotes);
    }

    if (monthlyChart) {
        monthlyChart.destroy();
    }

    monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: last6Months,
            datasets: [{
                label: 'নোট সংখ্যা',
                data: monthlyNoteCounts,
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                },
                x: {
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                }
            }
        }
    });
}

function initializeLengthChart() {
    const ctx = document.getElementById('lengthChart');
    if (!ctx) {
        console.log('Length chart canvas not found');
        return;
    }
    console.log('Initializing length chart...');

    // Categorize notes by length
    const lengthCategories = {
        short: notes.filter(note => note.content.length < 100).length,
        medium: notes.filter(note => note.content.length >= 100 && note.content.length < 500).length,
        long: notes.filter(note => note.content.length >= 500 && note.content.length < 1000).length,
        veryLong: notes.filter(note => note.content.length >= 1000).length
    };

    if (lengthChart) {
        lengthChart.destroy();
    }

    lengthChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['ছোট (<১০০)', 'মাঝারি (১০০-৫০০)', 'বড় (৫০০-১০০০)', 'অতি বড় (>১০০০)'],
            datasets: [{
                data: [lengthCategories.short, lengthCategories.medium, lengthCategories.long, lengthCategories.veryLong],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 11
                        },
                        padding: 15
                    }
                }
            }
        }
    });
}

function initializeDailyActivityChart() {
    const ctx = document.getElementById('dailyActivityChart');
    if (!ctx) {
        console.log('Daily activity chart canvas not found');
        return;
    }
    console.log('Initializing daily activity chart...');

    // Get hourly activity data
    const hourlyActivity = new Array(24).fill(0);

    notes.forEach(note => {
        const hour = new Date(note.createdAt).getHours();
        hourlyActivity[hour]++;
    });

    const hours = [];
    for (let i = 0; i < 24; i++) {
        hours.push(`${i}:00`);
    }

    if (dailyActivityChart) {
        dailyActivityChart.destroy();
    }

    dailyActivityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hours,
            datasets: [{
                label: 'নোট তৈরি',
                data: hourlyActivity,
                borderColor: 'rgba(255, 159, 64, 1)',
                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(255, 159, 64, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush'
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                },
                x: {
                    ticks: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush'
                        },
                        maxTicksLimit: 12
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                }
            }
        }
    });
}

function initializeCategoryChart() {
    const ctx = document.getElementById('categoryChart');
    if (!ctx) {
        console.log('Category chart canvas not found');
        return;
    }
    console.log('Initializing category chart...');

    // Categorize notes by content type
    const categories = {
        text: 0,
        list: 0,
        table: 0,
        image: 0,
        mixed: 0
    };

    notes.forEach(note => {
        const content = note.content.toLowerCase();

        if (content.includes('<img') || content.includes('![')) {
            categories.image++;
        } else if (content.includes('<table') || content.includes('|')) {
            categories.table++;
        } else if (content.includes('<ul>') || content.includes('<ol>') || content.includes('- ') || content.includes('* ')) {
            categories.list++;
        } else if (content.includes('<img') && (content.includes('<ul>') || content.includes('<table'))) {
            categories.mixed++;
        } else {
            categories.text++;
        }
    });

    if (categoryChart) {
        categoryChart.destroy();
    }

    categoryChart = new Chart(ctx, {
        type: 'polarArea',
        data: {
            labels: ['টেক্সট 📝', 'তালিকা 📋', 'টেবিল 📊', 'ছবি 🖼️', 'মিশ্র 🎨'],
            datasets: [{
                data: [categories.text, categories.list, categories.table, categories.image, categories.mixed],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 205, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        font: {
                            family: 'Kalpurush',
                            size: 11
                        },
                        padding: 15
                    }
                }
            },
            scales: {
                r: {
                    ticks: {
                        color: 'white',
                        backdropColor: 'transparent'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    },
                    angleLines: {
                        color: 'rgba(255, 255, 255, 0.2)'
                    }
                }
            }
        }
    });
}

function updateRecentActivity() {
    const activityList = document.getElementById('activityList');
    if (!activityList) return;

    // Get recent notes (last 5)
    const recentNotes = [...notes]
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5);

    if (recentNotes.length === 0) {
        activityList.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="activity-content">
                    <h5>কোন সাম্প্রতিক কার্যকলাপ নেই</h5>
                    <p>নতুন নোট তৈরি করুন</p>
                </div>
            </div>
        `;
        return;
    }

    activityList.innerHTML = recentNotes.map(note => {
        const timeAgo = getTimeAgo(note.updatedAt);
        const priorityIcon = getPriorityIcon(note.priority);
        const noteTitle = extractNoteTitle(note.content);

        return `
            <div class="activity-item" onclick="openNoteFromActivity('${note.id}')" style="cursor: pointer;">
                <div class="activity-icon">
                    ${priorityIcon}
                </div>
                <div class="activity-content">
                    <h5>${noteTitle}</h5>
                    <p>সর্বশেষ আপডেট</p>
                </div>
                <div class="activity-time">${timeAgo}</div>
            </div>
        `;
    }).join('');
}

function getPriorityIcon(priority) {
    switch (priority) {
        case 'low': return '<i class="fas fa-circle" style="color: #28a745;"></i>';
        case 'medium': return '<i class="fas fa-circle" style="color: #ffc107;"></i>';
        case 'high': return '<i class="fas fa-circle" style="color: #fd7e14;"></i>';
        case 'urgent': return '<i class="fas fa-circle" style="color: #dc3545;"></i>';
        default: return '<i class="fas fa-circle" style="color: #ffc107;"></i>';
    }
}

function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'এখনই';
    if (diffInMinutes < 60) return `${diffInMinutes} মিনিট আগে`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} ঘন্টা আগে`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} দিন আগে`;

    return formatBengaliDate(date);
}

// Format date in Bengali
function formatBengaliDate(date, format = 'full') {
    const bengaliMonths = [
        'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
        'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
    ];

    const bengaliDays = [
        'রবিবার', 'সোমবার', 'মঙ্গলবার', 'বুধবার', 'বৃহস্পতিবার', 'শুক্রবার', 'শনিবার'
    ];

    const shortDays = ['রবি', 'সোম', 'মঙ্গল', 'বুধ', 'বৃহ', 'শুক্র', 'শনি'];

    const day = date.getDate();
    const month = bengaliMonths[date.getMonth()];
    const year = date.getFullYear();
    const dayName = format === 'short' ? shortDays[date.getDay()] : bengaliDays[date.getDay()];

    if (format === 'short') {
        return shortDays[date.getDay()];
    } else if (format === 'full') {
        return bengaliDays[date.getDay()];
    } else if (format === 'date-only') {
        return `${day} ${month}, ${year}`;
    } else {
        return `${day} ${month}, ${year}`;
    }
}

function extractNoteTitle(content) {
    // Remove HTML tags and get first line or first 50 characters
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    const firstLine = textContent.split('\n')[0];
    return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine || 'শিরোনামহীন নোট';
}

// Open note from activity list
function openNoteFromActivity(noteId) {
    // Close dashboard modal first
    closeDashboardModal();

    // Wait a bit for the modal to close, then open the note
    setTimeout(() => {
        editNote(noteId);
    }, 300);
}

// Dashboard fullscreen functionality
function toggleDashboardFullscreen() {
    const modal = document.getElementById('dashboardModal');
    const fullscreenBtn = document.getElementById('dashboardFullscreenBtn');

    if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'ফুল স্ক্রিন';
    } else {
        modal.classList.add('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'ছোট করুন';
    }

    // Refresh charts after fullscreen toggle
    setTimeout(() => {
        if (weeklyTrendChart) weeklyTrendChart.resize();
        if (priorityChart) priorityChart.resize();
    }, 300);
}

// Settings fullscreen functionality
function toggleSettingsFullscreen() {
    const modal = document.getElementById('settingsModal');
    const fullscreenBtn = document.getElementById('settingsFullscreenBtn');

    if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'ফুল স্ক্রিন';
    } else {
        modal.classList.add('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'ছোট করুন';
    }
}



// Calendar fullscreen functionality
function toggleCalendarFullscreen() {
    const modal = document.getElementById('calendarModal');
    const fullscreenBtn = document.getElementById('calendarFullscreenBtn');

    if (modal.classList.contains('fullscreen')) {
        modal.classList.remove('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'ফুল স্ক্রিন';
    } else {
        modal.classList.add('fullscreen');
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'ছোট করুন';
    }
}



// Test function for debugging
function testDashboard() {
    console.log('Testing dashboard...');
    console.log('Notes array:', notes);
    console.log('Notes length:', notes.length);

    // Test if elements exist
    const elements = [
        'totalNotesCount',
        'todayNotesCount',
        'weekNotesCount',
        'urgentNotesCount',
        'weeklyTrendChart',
        'priorityChart',
        'activityList'
    ];

    elements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`Element ${id}:`, element ? 'Found' : 'Not found');
    });
}



// ===== REMINDER FUNCTIONALITY =====
function setupReminderHandlers() {
    // const reminderInput = document.getElementById('noteReminder'); // Not used currently
    const clearReminderBtn = document.getElementById('clearReminderBtn');

    if (clearReminderBtn) {
        clearReminderBtn.addEventListener('click', clearReminder);
    }

    // Initialize enhanced reminder system
    initializeReminderSystem();

    // Note: Notification permission will be requested when user first sets a reminder
    // This avoids the "Only request notification permission in response to a user gesture" warning

    // Initialize AudioContext on first user interaction
    setupAudioContextInitialization();
}

// Setup AudioContext initialization on user interaction
function setupAudioContextInitialization() {
    const initAudioOnInteraction = () => {
        if (!audioContextInitialized) {
            initializeAudioContext();
            // Remove listeners after first initialization
            document.removeEventListener('click', initAudioOnInteraction);
            document.removeEventListener('keydown', initAudioOnInteraction);
            document.removeEventListener('touchstart', initAudioOnInteraction);
        }
    };

    // Add listeners for various user interactions
    document.addEventListener('click', initAudioOnInteraction, { once: true });
    document.addEventListener('keydown', initAudioOnInteraction, { once: true });
    document.addEventListener('touchstart', initAudioOnInteraction, { once: true });
}

function clearReminder() {
    const reminderInput = document.getElementById('noteReminder');
    if (reminderInput) {
        reminderInput.value = '';
    }
}

function setReminderForNote(noteId, reminderTime) {
    if (reminderTimers.has(noteId)) {
        clearTimeout(reminderTimers.get(noteId));
    }

    // Request notification permission when user sets a reminder using notification manager
    if (window.notificationManager) {
        window.notificationManager.requestPermission().then(permission => {
            if (permission === 'denied') {
                showNotification('নোটিফিকেশন অনুমতি প্রয়োজন', 'রিমাইন্ডারের জন্য নোটিফিকেশন অনুমতি দিন', 'warning');
            }
        }).catch(error => {
            console.error('Error requesting notification permission:', error);
        });
    }

    // Set the actual reminder
    setReminderForNoteWithoutPermission(noteId, reminderTime);
}

function setReminderForNoteWithoutPermission(noteId, reminderTime) {
    if (reminderTimers.has(noteId)) {
        clearTimeout(reminderTimers.get(noteId));
    }

    const now = new Date();
    const reminderDate = new Date(reminderTime);
    const timeDiff = reminderDate.getTime() - now.getTime();

    if (timeDiff > 0) {
        const timerId = setTimeout(() => {
            const note = notes.find(n => n.id === noteId);
            if (note) {
                showReminderNotification(note);
            }
            reminderTimers.delete(noteId);
        }, timeDiff);

        reminderTimers.set(noteId, timerId);
    }
}

function showReminderNotification(note) {
    const message = `রিমাইন্ডার: ${note.title}`;

    // Show in-app notification
    showNotification('রিমাইন্ডার', message, 'info');

    // Show browser notification using notification manager
    if (window.notificationManager && window.notificationManager.canShowNotifications()) {
        window.notificationManager.showNotification('রিমাইন্ডার', message, {
            requireInteraction: true,
            tag: `reminder-${note.id}`,
            icon: 'assets/icon.png'
        });
    }

    // Play special reminder notification sound (louder and more persistent)
    playReminderSound();

    // Add to notification panel
    addNotification({
        id: generateId(),
        type: 'reminder',
        title: 'রিমাইন্ডার',
        message: note.title,
        timestamp: new Date().toISOString(),
        noteId: note.id
    });
}

// Special reminder sound function
function playReminderSound() {
    // Initialize AudioContext if not already done
    if (!audioContextInitialized) {
        initializeAudioContext();
    }

    if (!globalAudioContext) {
        console.log('AudioContext not available for reminder sound');
        return;
    }

    try {

        const playTone = (frequency, startTime, duration, volume) => {
            const oscillator = globalAudioContext.createOscillator();
            const gainNode = globalAudioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(globalAudioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, startTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

            oscillator.start(startTime);
            oscillator.stop(startTime + duration);
        };

        const currentTime = globalAudioContext.currentTime;

        // Play a more urgent reminder sound pattern with full volume
        const reminderVolume = notificationVolume * 1.2; // 20% louder for reminders

        // First sequence - attention grabbing
        playTone(1400, currentTime, 0.15, reminderVolume * 1.0);
        playTone(1000, currentTime + 0.2, 0.15, reminderVolume * 1.0);
        playTone(1400, currentTime + 0.4, 0.15, reminderVolume * 1.0);

        // Second sequence - confirmation
        playTone(800, currentTime + 0.7, 0.2, reminderVolume * 0.9);
        playTone(1200, currentTime + 1.0, 0.25, reminderVolume * 0.8);

        // Third sequence - final emphasis (repeat pattern)
        setTimeout(() => {
            if (globalAudioContext) {
                playTone(1400, globalAudioContext.currentTime, 0.12, reminderVolume * 0.7);
                playTone(1000, globalAudioContext.currentTime + 0.15, 0.12, reminderVolume * 0.7);
            }
        }, 1500);

    } catch (e) {
        // Fallback to regular notification sound
        if (notificationSound) {
            notificationSound.play();
        }
    }
}

// ===== CALENDAR FUNCTIONALITY =====
function openCalendarModal() {
    const modal = document.getElementById('calendarModal');
    modal.style.display = 'flex';
    initializeCalendar();
}

function closeCalendarModal() {
    const modal = document.getElementById('calendarModal');
    modal.style.display = 'none';
}

function initializeCalendar() {
    setupCalendarControls();
    renderCalendar();
}

function setupCalendarControls() {
    const prevBtn = document.getElementById('prevMonthBtn');
    const nextBtn = document.getElementById('nextMonthBtn');
    const todayBtn = document.getElementById('todayBtn');

    if (prevBtn) {
        prevBtn.onclick = () => {
            currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
            renderCalendar();
        };
    }

    if (nextBtn) {
        nextBtn.onclick = () => {
            currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
            renderCalendar();
        };
    }

    if (todayBtn) {
        todayBtn.onclick = () => {
            currentCalendarDate = new Date();
            renderCalendar();
        };
    }
}

function renderCalendar() {
    updateCalendarHeader();
    generateCalendarGrid();
}

function updateCalendarHeader() {
    const monthNames = [
        'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
        'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
    ];

    const currentMonthYear = document.getElementById('currentMonthYear');
    if (currentMonthYear) {
        const month = monthNames[currentCalendarDate.getMonth()];
        const year = currentCalendarDate.getFullYear();
        currentMonthYear.textContent = `${month} ${year}`;
    }
}

function generateCalendarGrid() {
    const calendarGrid = document.getElementById('calendarGrid');
    if (!calendarGrid) return;

    calendarGrid.innerHTML = '';

    // Add day headers
    const dayHeaders = ['রবি', 'সোম', 'মঙ্গল', 'বুধ', 'বৃহ', 'শুক্র', 'শনি'];
    dayHeaders.forEach(day => {
        const headerElement = document.createElement('div');
        headerElement.className = 'calendar-header';
        headerElement.textContent = day;
        calendarGrid.appendChild(headerElement);
    });

    // Get first day of month and number of days
    const firstDay = new Date(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth(), 1);
    const lastDay = new Date(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth() + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day other-month';
        calendarGrid.appendChild(emptyDay);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = createCalendarDay(day);
        calendarGrid.appendChild(dayElement);
    }
}

function createCalendarDay(day) {
    const dayElement = document.createElement('div');
    dayElement.className = 'calendar-day';

    const currentDate = new Date(currentCalendarDate.getFullYear(), currentCalendarDate.getMonth(), day);
    const today = new Date();

    // Check if it's today
    if (currentDate.toDateString() === today.toDateString()) {
        dayElement.classList.add('today');
    }

    // Get notes and reminders for this day
    const dayNotes = getNotesForDate(currentDate);
    const dayReminders = getRemindersForDate(currentDate);

    if (dayNotes.length > 0) {
        dayElement.classList.add('has-notes');
    }

    if (dayReminders.length > 0) {
        dayElement.classList.add('has-reminder');
    }

    dayElement.innerHTML = `
        <div class="calendar-day-number">${day}</div>
        <div class="calendar-day-notes">
            ${dayNotes.length > 0 ? `${dayNotes.length} নোট` : ''}
            ${dayReminders.length > 0 ? `${dayReminders.length} রিমাইন্ডার` : ''}
        </div>
        ${dayNotes.length > 0 ? '<div class="calendar-day-indicator notes"></div>' : ''}
        ${dayReminders.length > 0 ? '<div class="calendar-day-indicator reminder"></div>' : ''}
    `;

    dayElement.onclick = () => showDayDetails(currentDate, dayNotes, dayReminders);

    return dayElement;
}

function getNotesForDate(date) {
    const dateString = date.toDateString();
    return notes.filter(note => {
        const noteDate = new Date(note.createdAt);
        return noteDate.toDateString() === dateString;
    });
}

function getRemindersForDate(date) {
    const dateString = date.toDateString();
    return notes.filter(note => {
        if (!note.reminder) return false;
        const reminderDate = new Date(note.reminder);
        return reminderDate.toDateString() === dateString;
    });
}

function showDayDetails(date, dayNotes, dayReminders) {
    const bengaliDate = formatBengaliDate(date);
    let content = `<h3>${bengaliDate}</h3>`;

    if (dayNotes.length > 0) {
        content += '<h4>নোটসমূহ:</h4><ul>';
        dayNotes.forEach(note => {
            content += `<li onclick="editNote('${note.id}')" style="cursor: pointer; color: var(--primary-color);">${note.title}</li>`;
        });
        content += '</ul>';
    }

    if (dayReminders.length > 0) {
        content += '<h4>রিমাইন্ডারসমূহ:</h4><ul>';
        dayReminders.forEach(note => {
            const reminderTime = new Date(note.reminder).toLocaleTimeString('bn-BD');
            content += `<li onclick="editNote('${note.id}')" style="cursor: pointer; color: var(--danger-color);">${note.title} - ${reminderTime}</li>`;
        });
        content += '</ul>';
    }

    if (dayNotes.length === 0 && dayReminders.length === 0) {
        content += '<p>এই দিনে কোন নোট বা রিমাইন্ডার নেই।</p>';
    }

    showNotification('ক্যালেন্ডার তথ্য', content, 'info');
}

// ===== HELPER FUNCTIONS FOR TAGS AND REMINDERS =====
function formatReminderText(reminderTime) {
    const reminderDate = new Date(reminderTime);
    const now = new Date();
    const timeDiff = reminderDate.getTime() - now.getTime();

    if (timeDiff < 0) {
        return 'মেয়াদ উত্তীর্ণ';
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
        return `${days} দিন বাকি`;
    } else if (hours > 0) {
        return `${hours} ঘন্টা বাকি`;
    } else if (minutes > 0) {
        return `${minutes} মিনিট বাকি`;
    } else {
        return 'শীঘ্রই';
    }
}

// ===== THEME FUNCTIONALITY =====
function initializeThemes() {
    // Load saved theme settings
    const savedTheme = localStorage.getItem('noteAppTheme') || 'dark';
    const savedColorTheme = localStorage.getItem('noteAppColorTheme') || 'blue';

    currentTheme = savedTheme;
    currentColorTheme = savedColorTheme;

    applyTheme(currentTheme);
    applyColorTheme(currentColorTheme);

    // Auto theme detection
    if (currentTheme === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        applyTheme(prefersDark ? 'dark' : 'light');

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (currentTheme === 'auto') {
                applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
}

function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);

    // Update theme buttons in settings
    const themeButtons = document.querySelectorAll('.theme-btn');
    themeButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.theme === currentTheme) {
            btn.classList.add('active');
        }
    });

    // Apply theme to Jodit editor if it exists
    if (joditEditor && joditEditor.container) {
        updateJoditEditorTheme(theme);
    }
}

function updateJoditEditorTheme(theme) {
    if (!joditEditor || !joditEditor.container) return;

    const container = joditEditor.container;
    const toolbar = container.querySelector('.jodit-toolbar');
    const workplace = container.querySelector('.jodit-workplace');
    const wysiwyg = container.querySelector('.jodit-wysiwyg');
    const statusBar = container.querySelector('.jodit-status-bar');

    if (theme === 'dark') {
        // Apply dark theme styles to Jodit editor
        if (toolbar) {
            toolbar.style.backgroundColor = '#1a202c';
            toolbar.style.borderBottom = '1px solid #4a5568';
            toolbar.style.color = '#e2e8f0';
        }

        if (workplace) {
            workplace.style.backgroundColor = '#2d3748';
        }

        if (wysiwyg) {
            wysiwyg.style.backgroundColor = '#2d3748';
            wysiwyg.style.color = '#e2e8f0';

            // Also update iframe content if exists
            const iframe = wysiwyg.querySelector('iframe');
            if (iframe && iframe.contentDocument) {
                const body = iframe.contentDocument.body;
                const html = iframe.contentDocument.documentElement;
                if (body) {
                    body.style.backgroundColor = '#2d3748';
                    body.style.color = '#e2e8f0';
                    body.style.fontFamily = 'Kalpurush, sans-serif';
                }
                if (html) {
                    html.style.backgroundColor = '#2d3748';
                    html.style.color = '#e2e8f0';
                }

                // Apply styles to all text elements in iframe
                const allElements = iframe.contentDocument.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.style) {
                        el.style.color = '#e2e8f0';
                    }
                });
            }
        }

        if (statusBar) {
            statusBar.style.backgroundColor = '#1a202c';
            statusBar.style.borderTop = '1px solid #4a5568';
            statusBar.style.color = '#a0aec0';
        }

        // Update toolbar buttons
        const buttons = container.querySelectorAll('.jodit-toolbar button');
        buttons.forEach(btn => {
            btn.style.color = '#e2e8f0';
            btn.style.backgroundColor = 'transparent';
        });

    } else {
        // Apply light theme styles (reset to default)
        if (toolbar) {
            toolbar.style.backgroundColor = '';
            toolbar.style.borderBottom = '';
            toolbar.style.color = '';
        }

        if (workplace) {
            workplace.style.backgroundColor = '';
        }

        if (wysiwyg) {
            wysiwyg.style.backgroundColor = '';
            wysiwyg.style.color = '';

            // Also update iframe content if exists
            const iframe = wysiwyg.querySelector('iframe');
            if (iframe && iframe.contentDocument) {
                const body = iframe.contentDocument.body;
                const html = iframe.contentDocument.documentElement;
                if (body) {
                    body.style.backgroundColor = '';
                    body.style.color = '';
                    body.style.fontFamily = '';
                }
                if (html) {
                    html.style.backgroundColor = '';
                    html.style.color = '';
                }

                // Reset styles for all text elements in iframe
                const allElements = iframe.contentDocument.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.style) {
                        el.style.color = '';
                    }
                });
            }
        }

        if (statusBar) {
            statusBar.style.backgroundColor = '';
            statusBar.style.borderTop = '';
            statusBar.style.color = '';
        }

        // Reset toolbar buttons
        const buttons = container.querySelectorAll('.jodit-toolbar button');
        buttons.forEach(btn => {
            btn.style.color = '';
            btn.style.backgroundColor = '';
        });
    }
}

function applyColorTheme(colorTheme) {
    document.documentElement.setAttribute('data-color', colorTheme);

    // Update color theme buttons in settings
    const colorButtons = document.querySelectorAll('.color-theme');
    colorButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.color === colorTheme) {
            btn.classList.add('active');
        }
    });
}

function loadSettings() {
    // Load font settings
    currentFontSize = parseInt(localStorage.getItem('noteAppFontSize')) || 18;
    currentFontFamily = localStorage.getItem('noteAppFontFamily') || 'Courier New';
    currentFontWeight = parseInt(localStorage.getItem('noteAppFontWeight')) || 400;

    // Load notification volume setting
    notificationVolume = parseFloat(localStorage.getItem('noteAppNotificationVolume')) || 1.0;

    // Apply font settings
    applyFontSettings();

    // Load other settings
    const autoSaveEnabled = localStorage.getItem('noteAppAutoSave') !== 'false';
    const autoSaveIntervalValue = parseInt(localStorage.getItem('noteAppAutoSaveInterval')) || 0;

    // Update UI elements
    updateSettingsUI();
}

function applyFontSettings() {
    document.documentElement.style.setProperty('--font-size-base', currentFontSize + 'px');
    document.documentElement.style.setProperty('--font-family-bengali', currentFontFamily + ', sans-serif');
    document.documentElement.style.setProperty('--font-weight-base', currentFontWeight);
}

function updateSettingsUI() {
    // Update font size slider
    const fontSizeSlider = document.getElementById('fontSizeSlider');
    const fontSizeValue = document.getElementById('fontSizeValue');
    if (fontSizeSlider && fontSizeValue) {
        fontSizeSlider.value = currentFontSize;
        fontSizeValue.textContent = currentFontSize + 'px';
    }

    // Update font family select
    const fontFamilySelect = document.getElementById('fontFamilySelect');
    if (fontFamilySelect) {
        fontFamilySelect.value = currentFontFamily;
    }

    // Update font weight select
    const fontWeightSelect = document.getElementById('fontWeightSelect');
    if (fontWeightSelect) {
        fontWeightSelect.value = currentFontWeight;
    }

    // Update notification volume slider
    const notificationVolumeSlider = document.getElementById('notificationVolumeSlider');
    const notificationVolumeValue = document.getElementById('notificationVolumeValue');
    if (notificationVolumeSlider && notificationVolumeValue) {
        notificationVolumeSlider.value = Math.round(notificationVolume * 100);
        notificationVolumeValue.textContent = Math.round(notificationVolume * 100) + '%';
    }

    // Update auto save settings
    const autoSaveToggle = document.getElementById('autoSaveToggle');
    const autoSaveIntervalInput = document.getElementById('autoSaveInterval');
    if (autoSaveToggle) {
        autoSaveToggle.checked = autoSaveEnabled;
    }
    if (autoSaveIntervalInput) {
        autoSaveIntervalInput.value = autoSaveInterval / 1000;
    }
}

function openSettingsModal() {
    const modal = document.getElementById('settingsModal');
    modal.style.display = 'flex';
    updateSettingsUI();
    setupSettingsEventListeners();
}

function closeSettingsModal() {
    const modal = document.getElementById('settingsModal');
    modal.style.display = 'none';
}

function setupSettingsEventListeners() {
    // Theme buttons
    const themeButtons = document.querySelectorAll('.theme-btn');
    themeButtons.forEach(btn => {
        btn.onclick = () => {
            currentTheme = btn.dataset.theme;
            applyTheme(currentTheme === 'auto' ?
                (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') :
                currentTheme);
            localStorage.setItem('noteAppTheme', currentTheme);

            themeButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        };
    });

    // Color theme buttons
    const colorButtons = document.querySelectorAll('.color-theme');
    colorButtons.forEach(btn => {
        btn.onclick = () => {
            currentColorTheme = btn.dataset.color;
            applyColorTheme(currentColorTheme);
            localStorage.setItem('noteAppColorTheme', currentColorTheme);

            colorButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        };
    });

    // Font size slider
    const fontSizeSlider = document.getElementById('fontSizeSlider');
    const fontSizeValue = document.getElementById('fontSizeValue');
    if (fontSizeSlider && fontSizeValue) {
        fontSizeSlider.oninput = () => {
            currentFontSize = parseInt(fontSizeSlider.value);
            fontSizeValue.textContent = currentFontSize + 'px';
            applyFontSettings();
            localStorage.setItem('noteAppFontSize', currentFontSize);
        };
    }

    // Font family select
    const fontFamilySelect = document.getElementById('fontFamilySelect');
    if (fontFamilySelect) {
        fontFamilySelect.onchange = () => {
            currentFontFamily = fontFamilySelect.value;
            applyFontSettings();
            localStorage.setItem('noteAppFontFamily', currentFontFamily);
        };
    }

    // Font weight select
    const fontWeightSelect = document.getElementById('fontWeightSelect');
    if (fontWeightSelect) {
        fontWeightSelect.onchange = () => {
            currentFontWeight = parseInt(fontWeightSelect.value);
            applyFontSettings();
            localStorage.setItem('noteAppFontWeight', currentFontWeight);
        };
    }

    // Auto save settings
    const autoSaveToggle = document.getElementById('autoSaveToggle');
    const autoSaveIntervalInput = document.getElementById('autoSaveInterval');

    if (autoSaveToggle) {
        autoSaveToggle.onchange = () => {
            autoSaveEnabled = autoSaveToggle.checked;
            localStorage.setItem('noteAppAutoSave', autoSaveEnabled);
            if (autoSaveEnabled) {
                setupAutoSave();
            } else {
                if (autoSaveTimer) {
                    clearInterval(autoSaveTimer);
                    autoSaveTimer = null;
                }
            }
        };
    }

    if (autoSaveIntervalInput) {
        autoSaveIntervalInput.onchange = () => {
            const intervalValue = parseInt(autoSaveIntervalInput.value);
            autoSaveInterval = intervalValue === 0 ? 0 : intervalValue * 1000;
            localStorage.setItem('noteAppAutoSaveInterval', autoSaveInterval);

            // Show notification about real-time save
            if (intervalValue === 0) {
                showNotification('তথ্য', 'রিয়েল টাইম সেভ চালু করা হয়েছে - পরিবর্তন তাৎক্ষণিক সেভ হবে', 'info');
            }

            if (autoSaveEnabled) {
                setupAutoSave();
            }
        };
    }

    // Notification volume slider
    const notificationVolumeSlider = document.getElementById('notificationVolumeSlider');
    const notificationVolumeValue = document.getElementById('notificationVolumeValue');
    if (notificationVolumeSlider && notificationVolumeValue) {
        notificationVolumeSlider.oninput = () => {
            notificationVolume = parseInt(notificationVolumeSlider.value) / 100;
            notificationVolumeValue.textContent = notificationVolumeSlider.value + '%';
            localStorage.setItem('noteAppNotificationVolume', notificationVolume);
        };
    }

    // Test notification sound button
    const testNotificationSoundBtn = document.getElementById('testNotificationSoundBtn');
    if (testNotificationSoundBtn) {
        testNotificationSoundBtn.onclick = () => {
            if (notificationSound) {
                notificationSound.play();
            }
            showNotification('টেস্ট', 'নোটিফিকেশন সাউন্ড টেস্ট', 'info');
        };
    }



    // Reset settings button
    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    if (resetSettingsBtn) {
        resetSettingsBtn.onclick = () => {
            if (confirm('আপনি কি সত্যিই সব সেটিংস রিসেট করতে চান?')) {
                resetSettings();
            }
        };
    }
}

function resetSettings() {
    // Clear localStorage
    localStorage.removeItem('noteAppTheme');
    localStorage.removeItem('noteAppColorTheme');
    localStorage.removeItem('noteAppFontSize');
    localStorage.removeItem('noteAppFontFamily');
    localStorage.removeItem('noteAppFontWeight');
    localStorage.removeItem('noteAppAutoSave');
    localStorage.removeItem('noteAppAutoSaveInterval');
    localStorage.removeItem('noteAppNotificationVolume');

    // Reset to defaults
    currentTheme = 'dark';
    currentColorTheme = 'blue';
    currentFontSize = 18;
    currentFontFamily = 'Courier New';
    currentFontWeight = 400;
    autoSaveEnabled = true;
    autoSaveInterval = 0;
    notificationVolume = 1.0;

    // Apply defaults
    applyTheme(currentTheme);
    applyColorTheme(currentColorTheme);
    applyFontSettings();
    updateSettingsUI();
    setupAutoSave();

    showNotification('সফল', 'সেটিংস সফলভাবে রিসেট করা হয়েছে', 'success');
}



function isValidColor(color) {
    // Check if color is valid hex color
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
}

// ===== GLOBAL FUNCTION EXPOSURE =====
// Make functions available globally for onclick handlers













// ===== VERTICAL TABS FUNCTIONALITY =====
function initializeVerticalTabs() {
    // Create default tab
    createNewTab('নতুন ট্যাব', '', true);

    // Add event listeners
    const addTabBtn = document.getElementById('addTabBtn');
    const deleteTabBtn = document.getElementById('deleteTabBtn');
    const activeTabTitle = document.getElementById('activeTabTitle');

    if (addTabBtn) {
        addTabBtn.addEventListener('click', () => {
            createNewTab('নতুন ট্যাব', '');
        });
    }

    if (deleteTabBtn) {
        deleteTabBtn.addEventListener('click', deleteCurrentTab);
    }

    if (activeTabTitle) {
        activeTabTitle.addEventListener('input', updateActiveTabTitle);
        activeTabTitle.addEventListener('blur', saveCurrentTabContent);
    }

    // Initialize tab scrolling
    initializeTabScrolling();
}

// ===== TAB SCROLLING FUNCTIONALITY =====
function initializeTabScrolling() {
    const tabScrollLeft = document.getElementById('tabScrollLeft');
    const tabScrollRight = document.getElementById('tabScrollRight');
    const tabsNavContainer = document.querySelector('.tabs-nav-container');
    const tabsNav = document.getElementById('tabsNavCompact');

    if (!tabScrollLeft || !tabScrollRight || !tabsNavContainer || !tabsNav) {
        return;
    }

    // Scroll amount per click
    const scrollAmount = 150;

    // Left scroll button
    tabScrollLeft.addEventListener('click', () => {
        tabsNav.scrollBy({
            left: -scrollAmount,
            behavior: 'smooth'
        });
        setTimeout(updateScrollButtons, 100);
    });

    // Right scroll button
    tabScrollRight.addEventListener('click', () => {
        tabsNav.scrollBy({
            left: scrollAmount,
            behavior: 'smooth'
        });
        setTimeout(updateScrollButtons, 100);
    });

    // Update scroll buttons on scroll
    tabsNav.addEventListener('scroll', updateScrollButtons);

    // Update scroll buttons on resize
    window.addEventListener('resize', updateScrollButtons);

    // Initial update
    setTimeout(updateScrollButtons, 200);
}

function updateScrollButtons() {
    const tabScrollLeft = document.getElementById('tabScrollLeft');
    const tabScrollRight = document.getElementById('tabScrollRight');
    const tabsNav = document.getElementById('tabsNavCompact');

    if (!tabScrollLeft || !tabScrollRight || !tabsNav) {
        return;
    }

    const scrollLeft = tabsNav.scrollLeft;
    const scrollWidth = tabsNav.scrollWidth;
    const clientWidth = tabsNav.clientWidth;

    // Show/hide left button
    if (scrollLeft <= 0) {
        tabScrollLeft.disabled = true;
        tabScrollLeft.style.opacity = '0.3';
    } else {
        tabScrollLeft.disabled = false;
        tabScrollLeft.style.opacity = '0.7';
    }

    // Show/hide right button
    if (scrollLeft + clientWidth >= scrollWidth - 1) {
        tabScrollRight.disabled = true;
        tabScrollRight.style.opacity = '0.3';
    } else {
        tabScrollRight.disabled = false;
        tabScrollRight.style.opacity = '0.7';
    }

    // Hide scroll buttons if not needed
    const needsScrolling = scrollWidth > clientWidth;
    tabScrollLeft.style.display = needsScrolling ? 'flex' : 'none';
    tabScrollRight.style.display = needsScrolling ? 'flex' : 'none';
}

function scrollToActiveTab() {
    const activeTab = document.querySelector('.tab-compact.active');
    const tabsNav = document.getElementById('tabsNavCompact');

    if (!activeTab || !tabsNav) {
        return;
    }

    const tabRect = activeTab.getBoundingClientRect();
    const navRect = tabsNav.getBoundingClientRect();

    // Check if tab is visible
    if (tabRect.left < navRect.left || tabRect.right > navRect.right) {
        // Scroll to make the active tab visible
        const scrollLeft = activeTab.offsetLeft - (tabsNav.clientWidth / 2) + (activeTab.clientWidth / 2);
        tabsNav.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
        });
    }

    setTimeout(updateScrollButtons, 100);
}

function createNewTab(title, content = '', setActive = false) {
    tabCounter++;
    const tabId = `tab_${tabCounter}`;

    // Add tab number to title if not already present
    const tabNumber = editorTabs.length + 1;
    let finalTitle = title;

    // Check if title already has a number pattern
    if (!title.match(/^\d+\.\s/) && !title.match(/^ট্যাব\s\d+/)) {
        finalTitle = `${tabNumber}. ${title}`;
    }

    const newTab = {
        id: tabId,
        title: finalTitle,
        content: content,
        number: tabNumber
    };

    editorTabs.push(newTab);
    updateTabNumbers(); // Update all tab numbers
    renderVerticalTabs();

    if (setActive || editorTabs.length === 1) {
        switchToTab(tabId);
    }

    return tabId;
}

// ===== UPDATE TAB NUMBERS =====
function updateTabNumbers() {
    editorTabs.forEach((tab, index) => {
        const newNumber = index + 1;
        tab.number = newNumber;

        // Update title with new number if it has a number pattern
        if (tab.title.match(/^\d+\.\s/)) {
            // Replace existing number
            tab.title = tab.title.replace(/^\d+\.\s/, `${newNumber}. `);
        } else if (!tab.title.match(/^\d+\.\s/) && !tab.title.match(/^ট্যাব\s\d+/)) {
            // Add number if it doesn't have one
            tab.title = `${newNumber}. ${tab.title}`;
        }
    });
}

function renderVerticalTabs() {
    // Check if we're using compact layout
    const compactTabsContainer = document.getElementById('tabsNavCompact');
    const verticalTabsContainer = document.getElementById('verticalTabs');

    if (compactTabsContainer) {
        renderCompactTabs();
        return;
    }

    if (!verticalTabsContainer) return;

    verticalTabsContainer.innerHTML = '';

    editorTabs.forEach(tab => {
        const tabElement = document.createElement('div');
        tabElement.className = `vertical-tab ${tab.id === currentActiveTabId ? 'active' : ''}`;
        tabElement.dataset.tabId = tab.id;

        // Extract number and title
        const numberMatch = tab.title.match(/^(\d+)\.\s(.+)$/);
        const tabNumber = numberMatch ? numberMatch[1] : (editorTabs.indexOf(tab) + 1);
        const tabTitle = numberMatch ? numberMatch[2] : tab.title;

        tabElement.innerHTML = `
            <span class="tab-number">${tabNumber}</span>
            <span class="tab-title">${tabTitle}</span>
            <button class="tab-close-btn" onclick="deleteTab('${tab.id}')" title="ট্যাব বন্ধ করুন">
                <i class="fas fa-times"></i>
            </button>
        `;

        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close-btn')) {
                switchToTab(tab.id);
            }
        });

        verticalTabsContainer.appendChild(tabElement);
    });
}

function renderCompactTabs() {
    const compactTabsContainer = document.getElementById('tabsNavCompact');
    if (!compactTabsContainer) return;

    compactTabsContainer.innerHTML = '';

    editorTabs.forEach(tab => {
        const tabElement = document.createElement('div');
        tabElement.className = `tab-compact ${tab.id === currentActiveTabId ? 'active' : ''}`;
        tabElement.dataset.tabId = tab.id;

        // Extract number and title
        const numberMatch = tab.title.match(/^(\d+)\.\s(.+)$/);
        const tabNumber = numberMatch ? numberMatch[1] : (editorTabs.indexOf(tab) + 1);
        const tabTitle = numberMatch ? numberMatch[2] : tab.title;

        tabElement.innerHTML = `
            <span class="tab-number">${tabNumber}</span>
            <span class="tab-title">${tabTitle}</span>
            <span class="tab-close" onclick="deleteTab('${tab.id}')" title="ট্যাব বন্ধ করুন">
                <i class="fas fa-times"></i>
            </span>
        `;

        tabElement.addEventListener('click', (e) => {
            if (!e.target.closest('.tab-close')) {
                switchToTab(tab.id);
            }
        });

        compactTabsContainer.appendChild(tabElement);
    });

    // Update scroll buttons after rendering
    setTimeout(updateScrollButtons, 50);
}

function switchToTab(tabId) {
    // Save current tab content before switching
    if (currentActiveTabId && joditEditor) {
        saveCurrentTabContent();
    }

    const tab = editorTabs.find(t => t.id === tabId);
    if (!tab) return;

    currentActiveTabId = tabId;

    // Update UI
    const activeTabTitle = document.getElementById('activeTabTitle');
    if (activeTabTitle) {
        activeTabTitle.value = tab.title;
    }

    // Update editor content
    if (joditEditor) {
        joditEditor.value = tab.content;
    }

    // Update tab visual state
    renderVerticalTabs();

    // Scroll to active tab
    setTimeout(scrollToActiveTab, 100);

    // Update delete button state
    const deleteTabBtn = document.getElementById('deleteTabBtn');
    if (deleteTabBtn) {
        deleteTabBtn.disabled = editorTabs.length <= 1;
    }
}

function saveCurrentTabContent() {
    if (!currentActiveTabId || !joditEditor) {
        return;
    }

    const tab = editorTabs.find(t => t.id === currentActiveTabId);

    if (tab) {
        tab.content = joditEditor.value;
    }
}

function updateActiveTabTitle() {
    const activeTabTitle = document.getElementById('activeTabTitle');
    if (!activeTabTitle || !currentActiveTabId) return;

    const tab = editorTabs.find(t => t.id === currentActiveTabId);
    if (tab) {
        tab.title = activeTabTitle.value || 'নামহীন ট্যাব';
        renderVerticalTabs();
    }
}

function deleteTab(tabId) {
    if (editorTabs.length <= 1) {
        showNotification('ত্রুটি', 'অন্তত একটি ট্যাব থাকতে হবে', 'error');
        return;
    }

    const tabIndex = editorTabs.findIndex(t => t.id === tabId);
    if (tabIndex === -1) return;

    // Remove tab
    editorTabs.splice(tabIndex, 1);

    // Update tab numbers after deletion
    updateTabNumbers();

    // If deleted tab was active, switch to another tab
    if (currentActiveTabId === tabId) {
        const newActiveIndex = Math.min(tabIndex, editorTabs.length - 1);
        switchToTab(editorTabs[newActiveIndex].id);
    }

    renderVerticalTabs();
}

function deleteCurrentTab() {
    if (currentActiveTabId) {
        deleteTab(currentActiveTabId);
    }
}

function getAllTabsContent() {
    // Save current tab content first
    saveCurrentTabContent();

    const tabsData = editorTabs.map(tab => ({
        title: tab.title,
        content: tab.content
    }));

    return tabsData;
}

function loadTabsFromNote(note) {
    // Clear existing tabs
    editorTabs = [];
    currentActiveTabId = null;

    // Handle encrypted notes
    if (note.encrypted && note.tempDecryptedTabs) {
        // Load decrypted tabs
        note.tempDecryptedTabs.forEach((tabData, index) => {
            // Remove existing number from title if present
            let cleanTitle = tabData.title.replace(/^\d+\.\s/, '');
            createNewTab(cleanTitle, tabData.content, index === 0);
        });
    } else if (note.tabs && note.tabs.length > 0) {
        // Load regular tabs from note
        note.tabs.forEach((tabData, index) => {
            // Remove existing number from title if present
            let cleanTitle = tabData.title.replace(/^\d+\.\s/, '');
            createNewTab(cleanTitle, tabData.content, index === 0);
        });
    } else {
        // Create default tab with note content
        const title = note.title || 'মূল নোট';
        const content = note.encrypted && note.tempDecryptedContent ? note.tempDecryptedContent : (note.content || '');
        createNewTab(title, content, true);
    }
}



// ===== PASSWORD PROTECTION FUNCTIONALITY =====
function initializePasswordProtection() {
    console.log('Initializing password protection...');

    // Password toggle functionality
    if (enablePasswordCheckbox) {
        enablePasswordCheckbox.addEventListener('change', togglePasswordSection);
        console.log('✓ Password checkbox event listener added');
    } else {
        console.error('✗ enablePasswordCheckbox not found');
    }

    // Password visibility toggles
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', () => togglePasswordVisibility('notePassword', 'togglePasswordBtn'));
        console.log('✓ togglePasswordBtn event listener added');
    } else {
        console.error('✗ togglePasswordBtn not found');
    }

    if (toggleConfirmPasswordBtn) {
        toggleConfirmPasswordBtn.addEventListener('click', () => togglePasswordVisibility('confirmPassword', 'toggleConfirmPasswordBtn'));
        console.log('✓ toggleConfirmPasswordBtn event listener added');
    } else {
        console.error('✗ toggleConfirmPasswordBtn not found');
    }

    if (toggleUnlockPasswordBtn) {
        toggleUnlockPasswordBtn.addEventListener('click', () => togglePasswordVisibility('unlockPassword', 'toggleUnlockPasswordBtn'));
        console.log('✓ toggleUnlockPasswordBtn event listener added');
    } else {
        console.error('✗ toggleUnlockPasswordBtn not found');
    }

    // Password strength checking
    if (notePasswordInput) {
        notePasswordInput.addEventListener('input', checkPasswordStrength);
    }

    // Password confirmation
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordConfirmation);
    }

    // Unlock functionality
    if (unlockNoteBtn) {
        unlockNoteBtn.addEventListener('click', attemptUnlockNote);
        console.log('✓ unlockNoteBtn event listener added');
    } else {
        console.error('✗ unlockNoteBtn not found');
    }

    // Enter key for unlock
    if (unlockPasswordInput) {
        unlockPasswordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                attemptUnlockNote();
            }
        });
        console.log('✓ unlockPasswordInput keypress event listener added');
    } else {
        console.error('✗ unlockPasswordInput not found');
    }
}

function togglePasswordSection() {
    console.log('togglePasswordSection called');
    console.log('enablePasswordCheckbox.checked:', enablePasswordCheckbox.checked);
    console.log('passwordInputGroup:', passwordInputGroup);

    if (enablePasswordCheckbox.checked) {
        console.log('Showing password input group');
        passwordInputGroup.style.display = 'flex';
        console.log('Password input group display after setting:', passwordInputGroup.style.display);
        if (notePasswordInput) {
            notePasswordInput.focus();
        }
    } else {
        console.log('Hiding password input group');
        passwordInputGroup.style.display = 'none';
        if (notePasswordInput) {
            notePasswordInput.value = '';
        }
        if (confirmPasswordInput) {
            confirmPasswordInput.value = '';
        }
        resetPasswordStrength();
    }
}

function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);

    console.log('togglePasswordVisibility called:', inputId, buttonId);
    console.log('Input element:', input);
    console.log('Button element:', button);

    if (!input || !button) {
        console.error('Missing input or button element');
        return;
    }

    const icon = button.querySelector('i');
    console.log('Icon element:', icon);

    if (!icon) {
        console.error('Icon element not found in button');
        return;
    }

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
        console.log('Password shown');
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
        console.log('Password hidden');
    }
}

function checkPasswordStrength() {
    const password = notePasswordInput.value;
    const strength = calculatePasswordStrength(password);

    updatePasswordStrengthUI(strength);
}

function calculatePasswordStrength(password) {
    if (!password) return { level: 'none', score: 0 };

    let score = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        bengali: /[\u0980-\u09FF]/.test(password)
    };

    // Calculate score
    if (checks.length) score += 2;
    if (checks.lowercase) score += 1;
    if (checks.uppercase) score += 1;
    if (checks.numbers) score += 1;
    if (checks.symbols) score += 2;
    if (checks.bengali) score += 1;
    if (password.length >= 12) score += 1;

    // Determine level
    let level = 'weak';
    if (score >= 7) level = 'strong';
    else if (score >= 5) level = 'good';
    else if (score >= 3) level = 'fair';

    return { level, score, checks };
}

function updatePasswordStrengthUI(strength) {
    if (!passwordStrengthFill || !passwordStrengthText) return;

    // Remove existing classes
    passwordStrengthFill.className = 'strength-fill';

    if (strength.level !== 'none') {
        passwordStrengthFill.classList.add(strength.level);
    }

    // Update text
    const strengthTexts = {
        none: 'পাসওয়ার্ড শক্তি',
        weak: 'দুর্বল পাসওয়ার্ড',
        fair: 'মোটামুটি পাসওয়ার্ড',
        good: 'ভালো পাসওয়ার্ড',
        strong: 'শক্তিশালী পাসওয়ার্ড'
    };

    passwordStrengthText.textContent = strengthTexts[strength.level] || strengthTexts.none;
}

function resetPasswordStrength() {
    if (passwordStrengthFill) {
        passwordStrengthFill.className = 'strength-fill';
    }
    if (passwordStrengthText) {
        passwordStrengthText.textContent = 'পাসওয়ার্ড শক্তি';
    }
}

function validatePasswordConfirmation() {
    const password = notePasswordInput.value;
    const confirmPassword = confirmPasswordInput.value;

    if (confirmPassword && password !== confirmPassword) {
        confirmPasswordInput.style.borderColor = '#dc3545';
        confirmPasswordInput.title = 'পাসওয়ার্ড মিলছে না';
    } else {
        confirmPasswordInput.style.borderColor = '';
        confirmPasswordInput.title = '';
    }
}

// Unicode-safe base64 encoding/decoding functions
function unicodeToBase64(str) {
    try {
        return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
            return String.fromCharCode('0x' + p1);
        }));
    } catch (error) {
        console.error('Unicode to Base64 error:', error);
        return str;
    }
}

function base64ToUnicode(str) {
    try {
        return decodeURIComponent(atob(str).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
    } catch (error) {
        console.error('Base64 to Unicode error:', error);
        return null;
    }
}

// Simple encryption/decryption functions
function encryptText(text, password) {
    try {
        if (!text || !password) {
            console.error('Missing text or password for encryption');
            return text;
        }

        // Simple XOR encryption with password hash
        const passwordHash = hashPassword(password);
        if (!passwordHash) {
            console.error('Failed to hash password for encryption');
            return text;
        }

        let encrypted = '';

        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            const keyChar = passwordHash.charCodeAt(i % passwordHash.length);
            encrypted += String.fromCharCode(charCode ^ keyChar);
        }

        // Unicode-safe Base64 encode the result
        const result = unicodeToBase64(encrypted);
        if (!result || result === encrypted) {
            console.error('Failed to encode to base64');
            return text;
        }

        return result;
    } catch (error) {
        console.error('Encryption error:', error);
        return text;
    }
}

function decryptText(encryptedText, password) {
    try {
        if (!encryptedText || !password) {
            console.error('Missing encrypted text or password');
            return null;
        }

        // Unicode-safe Base64 decode first
        const encrypted = base64ToUnicode(encryptedText);
        if (encrypted === null || encrypted === undefined) {
            console.error('Failed to decode base64');
            return null;
        }

        const passwordHash = hashPassword(password);
        if (!passwordHash) {
            console.error('Failed to hash password');
            return null;
        }

        let decrypted = '';

        for (let i = 0; i < encrypted.length; i++) {
            const charCode = encrypted.charCodeAt(i);
            const keyChar = passwordHash.charCodeAt(i % passwordHash.length);
            decrypted += String.fromCharCode(charCode ^ keyChar);
        }

        return decrypted;
    } catch (error) {
        console.error('Decryption error:', error);
        return null;
    }
}

function hashPassword(password) {
    // Simple hash function for password
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
        const char = password.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
}

function verifyPassword(encryptedData, password) {
    try {
        if (!encryptedData || !password) {
            return false;
        }
        const decrypted = decryptText(encryptedData, password);
        return decrypted !== null && decrypted !== undefined && decrypted.length > 0;
    } catch (error) {
        console.error('Password verification error:', error);
        return false;
    }
}

// Password modal functions
function openPasswordModal(noteId) {
    const note = notes.find(n => n.id === noteId);
    if (!note || !note.encrypted) return;

    lockedNoteId = noteId;
    currentPasswordAttempts = 0;

    if (lockedNoteTitle) {
        lockedNoteTitle.textContent = note.title || 'এনক্রিপ্টেড নোট';
    }

    if (unlockPasswordInput) {
        unlockPasswordInput.value = '';
    }

    hidePasswordError();
    updateAttemptsDisplay();

    if (passwordModal) {
        passwordModal.style.display = 'flex';
        passwordModal.classList.add('show');
        setTimeout(() => {
            if (unlockPasswordInput) {
                unlockPasswordInput.focus();
            }
        }, 100);
    }
}

function closePasswordModal() {
    if (passwordModal) {
        passwordModal.classList.remove('show');
        setTimeout(() => {
            passwordModal.style.display = 'none';
        }, 300);
    }

    lockedNoteId = null;
    currentPasswordAttempts = 0;
    hidePasswordError();
}

function attemptUnlockNote() {
    if (!lockedNoteId || !unlockPasswordInput) {
        console.error('Missing lockedNoteId or unlockPasswordInput');
        return;
    }

    const password = unlockPasswordInput.value.trim();
    if (!password) {
        showPasswordError('পাসওয়ার্ড লিখুন');
        return;
    }

    const note = notes.find(n => n.id === lockedNoteId);
    if (!note || !note.encrypted) {
        console.error('Note not found or not encrypted:', note);
        closePasswordModal();
        return;
    }

    console.log('Attempting to unlock note:', note.title);
    console.log('Encrypted content exists:', !!note.encryptedContent);

    // Verify password by trying to decrypt
    const isValidPassword = verifyPassword(note.encryptedContent, password);
    console.log('Password validation result:', isValidPassword);

    if (isValidPassword) {
        // Password correct - decrypt and open note
        console.log('Password correct, decrypting note...');
        decryptAndOpenNote(note, password);
        closePasswordModal();
        showNotification('সফল', 'নোট আনলক হয়েছে', 'success');
    } else {
        // Password incorrect
        currentPasswordAttempts++;
        console.log('Password incorrect, attempts:', currentPasswordAttempts);

        if (currentPasswordAttempts >= maxPasswordAttempts) {
            showPasswordError('সর্বোচ্চ চেষ্টার সীমা অতিক্রম হয়েছে');
            setTimeout(() => {
                closePasswordModal();
            }, 2000);
        } else {
            showPasswordError('ভুল পাসওয়ার্ড! আবার চেষ্টা করুন।');
            updateAttemptsDisplay();
            unlockPasswordInput.value = '';
            unlockPasswordInput.focus();
        }
    }
}

function showPasswordError(message) {
    if (passwordError) {
        passwordError.querySelector('span').textContent = message;
        passwordError.style.display = 'flex';
    }
}

function hidePasswordError() {
    if (passwordError) {
        passwordError.style.display = 'none';
    }
}

function updateAttemptsDisplay() {
    const remaining = maxPasswordAttempts - currentPasswordAttempts;
    if (attemptsLeft) {
        attemptsLeft.textContent = remaining;
    }

    if (passwordAttempts) {
        if (currentPasswordAttempts > 0 && remaining > 0) {
            passwordAttempts.style.display = 'block';
        } else {
            passwordAttempts.style.display = 'none';
        }
    }
}

function decryptAndOpenNote(note, password) {
    try {
        console.log('Decrypting note:', note.title);
        console.log('Encrypted content length:', note.encryptedContent?.length);

        // Decrypt the content
        const decryptedContent = decryptText(note.encryptedContent, password);
        console.log('Decrypted content:', decryptedContent ? 'Success' : 'Failed');

        if (decryptedContent) {
            // Temporarily store decrypted content
            note.tempDecryptedContent = decryptedContent;

            // Decrypt tabs if they exist
            if (note.encryptedTabs) {
                console.log('Decrypting tabs...');
                const decryptedTabsJson = decryptText(note.encryptedTabs, password);
                if (decryptedTabsJson) {
                    try {
                        note.tempDecryptedTabs = JSON.parse(decryptedTabsJson);
                        console.log('Tabs decrypted successfully:', note.tempDecryptedTabs.length);
                    } catch (e) {
                        console.error('Failed to parse decrypted tabs JSON:', e);
                        note.tempDecryptedTabs = [{
                            title: 'মূল নোট',
                            content: decryptedContent
                        }];
                    }
                }
            } else {
                console.log('No encrypted tabs, using default tab');
                note.tempDecryptedTabs = [{
                    title: 'মূল নোট',
                    content: decryptedContent
                }];
            }

            // Open the note for editing
            console.log('Opening decrypted note for editing...');
            editNote(note.id);
        } else {
            console.error('Failed to decrypt content');
            showNotification('ত্রুটি', 'নোট ডিক্রিপ্ট করতে সমস্যা হয়েছে', 'error');
        }
    } catch (error) {
        console.error('Error decrypting note:', error);
        showNotification('ত্রুটি', 'নোট ডিক্রিপ্ট করতে সমস্যা হয়েছে', 'error');
    }
}

function encryptNoteData(noteData, password) {
    if (!password) {
        console.error('No password provided for encryption');
        return noteData;
    }

    console.log('Encrypting note data:', noteData.title);
    const encryptedNote = { ...noteData };

    // Encrypt main content
    if (noteData.content) {
        console.log('Encrypting main content, length:', noteData.content.length);
        encryptedNote.encryptedContent = encryptText(noteData.content, password);
        if (encryptedNote.encryptedContent) {
            delete encryptedNote.content;
            console.log('Main content encrypted successfully');
        } else {
            console.error('Failed to encrypt main content');
        }
    }

    // Encrypt tabs
    if (noteData.tabs && noteData.tabs.length > 0) {
        console.log('Encrypting tabs, count:', noteData.tabs.length);
        const tabsJson = JSON.stringify(noteData.tabs);
        encryptedNote.encryptedTabs = encryptText(tabsJson, password);
        if (encryptedNote.encryptedTabs) {
            delete encryptedNote.tabs;
            console.log('Tabs encrypted successfully');
        } else {
            console.error('Failed to encrypt tabs');
        }
    }

    // Mark as encrypted
    encryptedNote.encrypted = true;
    encryptedNote.passwordHash = hashPassword(password);

    console.log('Note encryption completed');
    return encryptedNote;
}

// ===== SOCIAL ICONS FALLBACK =====
// Check if Font Awesome icons are loaded and show fallback if needed
function checkSocialIcons() {
    const socialLinks = document.querySelectorAll('.social-link');
    const iconMappings = [
        { icon: 'fab fa-github', fallback: '🐙', title: 'GitHub' },
        { icon: 'fab fa-linkedin-in', fallback: '💼', title: 'LinkedIn' },
        { icon: 'fab fa-twitter', fallback: '🐦', title: 'Twitter' },
        { icon: 'fas fa-envelope', fallback: '✉️', title: 'Email' },
        { icon: 'fab fa-facebook-f', fallback: '📘', title: 'Facebook' },
        { icon: 'fab fa-instagram', fallback: '📷', title: 'Instagram' }
    ];

    socialLinks.forEach((link, index) => {
        if (iconMappings[index]) {
            // Always show emoji fallback for better visibility
            link.innerHTML = iconMappings[index].fallback;
            link.style.fontSize = '1.8rem';
            link.style.lineHeight = '1';
            link.style.display = 'flex';
            link.style.alignItems = 'center';
            link.style.justifyContent = 'center';
            link.title = iconMappings[index].title;

            // Add click functionality
            link.style.cursor = 'pointer';
            link.addEventListener('click', function() {
                // You can add actual social media links here
                console.log(`Clicked on ${iconMappings[index].title}`);
            });
        }
    });
}

function isPasswordValid() {
    console.log('isPasswordValid called');
    console.log('enablePasswordCheckbox:', enablePasswordCheckbox);
    console.log('enablePasswordCheckbox.checked:', enablePasswordCheckbox?.checked);

    if (!enablePasswordCheckbox || !enablePasswordCheckbox.checked) {
        console.log('Password protection not enabled');
        return { valid: true };
    }

    const password = notePasswordInput ? notePasswordInput.value.trim() : '';
    const confirmPassword = confirmPasswordInput ? confirmPasswordInput.value.trim() : '';

    console.log('Password:', password ? 'Provided' : 'Empty');
    console.log('Confirm Password:', confirmPassword ? 'Provided' : 'Empty');

    if (!password) {
        console.log('Password validation failed: No password');
        showNotification('ত্রুটি', 'পাসওয়ার্ড লিখুন', 'error');
        return { valid: false, message: 'পাসওয়ার্ড লিখুন' };
    }

    if (password.length < 4) {
        console.log('Password validation failed: Too short');
        showNotification('ত্রুটি', 'পাসওয়ার্ড কমপক্ষে ৪ অক্ষরের হতে হবে', 'error');
        return { valid: false, message: 'পাসওয়ার্ড কমপক্ষে ৪ অক্ষরের হতে হবে' };
    }

    if (password !== confirmPassword) {
        console.log('Password validation failed: Passwords do not match');
        showNotification('ত্রুটি', 'পাসওয়ার্ড নিশ্চিতকরণ মিলছে না', 'error');
        return { valid: false, message: 'পাসওয়ার্ড নিশ্চিতকরণ মিলছে না' };
    }

    console.log('Password validation successful');
    return { valid: true, password };
}

// Make functions and variables globally accessible for debugging
window.isPasswordValid = isPasswordValid;
window.encryptText = encryptText;
window.decryptText = decryptText;
window.verifyPassword = verifyPassword;
window.hashPassword = hashPassword;
window.toggleAdvancedOptions = toggleAdvancedOptions;
window.togglePasswordSection = togglePasswordSection;
window.notes = notes;

// Debug helper function
window.debugPasswordProtection = function() {
    console.log('=== Password Protection Debug ===');

    // Force show advanced options
    const advancedOptions = document.getElementById('advancedOptions');
    if (advancedOptions) {
        advancedOptions.style.display = 'block !important';
        advancedOptions.style.visibility = 'visible';
        advancedOptions.style.opacity = '1';
        console.log('✓ Advanced options forced to show');
        console.log('Advanced options display:', advancedOptions.style.display);
    } else {
        console.error('✗ Advanced options element not found');
    }

    // Enable password checkbox
    const enablePasswordCheckbox = document.getElementById('enablePassword');
    if (enablePasswordCheckbox) {
        enablePasswordCheckbox.checked = true;
        enablePasswordCheckbox.dispatchEvent(new Event('change'));
        console.log('✓ Password checkbox enabled');
    } else {
        console.error('✗ Password checkbox not found');
    }

    // Force show password input group
    const passwordInputGroup = document.getElementById('passwordInputGroup');
    if (passwordInputGroup) {
        passwordInputGroup.style.display = 'flex !important';
        passwordInputGroup.style.visibility = 'visible';
        passwordInputGroup.style.opacity = '1';
        console.log('✓ Password input group forced to show');
        console.log('Password input group display:', passwordInputGroup.style.display);
    } else {
        console.error('✗ Password input group not found');
    }

    // Set test passwords
    const notePasswordInput = document.getElementById('notePassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (notePasswordInput && confirmPasswordInput) {
        notePasswordInput.value = 'test123';
        confirmPasswordInput.value = 'test123';
        console.log('✓ Test passwords set');
        console.log('Password input value:', notePasswordInput.value);
        console.log('Confirm password value:', confirmPasswordInput.value);
    } else {
        console.error('✗ Password input fields not found');
        console.log('notePasswordInput:', notePasswordInput);
        console.log('confirmPasswordInput:', confirmPasswordInput);
    }

    // Test password validation
    if (typeof window.isPasswordValid === 'function') {
        const validation = window.isPasswordValid();
        console.log('Password validation result:', validation);
    }

    console.log('Now try to save a note with password protection');
    console.log('If still not working, check browser console for errors');
};

// Test eye buttons
window.testEyeButtons = function() {
    console.log('=== Testing Eye Buttons ===');

    const buttons = [
        { id: 'togglePasswordBtn', inputId: 'notePassword' },
        { id: 'toggleConfirmPasswordBtn', inputId: 'confirmPassword' },
        { id: 'toggleUnlockPasswordBtn', inputId: 'unlockPassword' }
    ];

    buttons.forEach(({ id, inputId }) => {
        const button = document.getElementById(id);
        const input = document.getElementById(inputId);

        console.log(`${id}:`, button ? '✓ Found' : '✗ Not found');
        console.log(`${inputId}:`, input ? '✓ Found' : '✗ Not found');

        if (button && input) {
            console.log(`Testing ${id}...`);
            button.click();
            console.log(`Input type after click: ${input.type}`);
        }
    });
};

// Test unlock functionality
window.testUnlockFunction = function() {
    console.log('=== Testing Unlock Function ===');

    const unlockBtn = document.getElementById('unlockNoteBtn');
    const unlockInput = document.getElementById('unlockPassword');

    console.log('unlockNoteBtn:', unlockBtn ? '✓ Found' : '✗ Not found');
    console.log('unlockPasswordInput:', unlockInput ? '✓ Found' : '✗ Not found');

    if (unlockBtn && unlockInput) {
        unlockInput.value = 'test123';
        console.log('Set unlock password to: test123');
        console.log('Clicking unlock button...');
        unlockBtn.click();
    }
};

// Force reinitialize password protection
window.forceReinitPasswordProtection = function() {
    console.log('=== Force Reinitializing Password Protection ===');

    // Remove existing event listeners and add new ones
    const togglePasswordBtn = document.getElementById('togglePasswordBtn');
    const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPasswordBtn');
    const toggleUnlockPasswordBtn = document.getElementById('toggleUnlockPasswordBtn');
    const unlockNoteBtn = document.getElementById('unlockNoteBtn');

    // Add event listeners directly
    if (togglePasswordBtn) {
        togglePasswordBtn.onclick = () => togglePasswordVisibility('notePassword', 'togglePasswordBtn');
        console.log('✓ togglePasswordBtn onclick set');
    }

    if (toggleConfirmPasswordBtn) {
        toggleConfirmPasswordBtn.onclick = () => togglePasswordVisibility('confirmPassword', 'toggleConfirmPasswordBtn');
        console.log('✓ toggleConfirmPasswordBtn onclick set');
    }

    if (toggleUnlockPasswordBtn) {
        toggleUnlockPasswordBtn.onclick = () => togglePasswordVisibility('unlockPassword', 'toggleUnlockPasswordBtn');
        console.log('✓ toggleUnlockPasswordBtn onclick set');
    }

    if (unlockNoteBtn) {
        unlockNoteBtn.onclick = attemptUnlockNote;
        console.log('✓ unlockNoteBtn onclick set');
    }

    console.log('Password protection reinitialized with onclick handlers');
};

// Quick test function
window.quickPasswordTest = function() {
    console.log('=== Quick Password Test ===');

    // Create a test encrypted note
    const testNote = {
        id: 'test-encrypted-note',
        title: 'টেস্ট এনক্রিপ্টেড নোট',
        encrypted: true,
        encryptedContent: encryptText('এটি একটি গোপনীয় নোট', 'test123'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // Add to notes array
    notes.unshift(testNote);

    // Update display
    updateNotesDisplay();

    console.log('✓ Test encrypted note created');
    console.log('✓ Click on the note to test unlock');
    console.log('✓ Password is: test123');
};

function resetPasswordFields() {
    if (enablePasswordCheckbox) {
        enablePasswordCheckbox.checked = false;
    }
    if (passwordInputGroup) {
        passwordInputGroup.style.display = 'none';
    }
    if (notePasswordInput) {
        notePasswordInput.value = '';
    }
    if (confirmPasswordInput) {
        confirmPasswordInput.value = '';
    }
    resetPasswordStrength();
}

function clearAllTemporaryDecryptedData() {
    // Clear temporary decrypted data from all encrypted notes for security
    notes.forEach(note => {
        if (note.encrypted) {
            delete note.tempDecryptedContent;
            delete note.tempDecryptedTabs;
        }
    });
}

// ===== CONTEXT MENU FUNCTIONALITY =====
function createContextMenu() {
    if (contextMenu) {
        contextMenu.remove();
    }

    contextMenu = document.createElement('div');
    contextMenu.className = 'context-menu';
    contextMenu.innerHTML = `
        <div class="context-menu-item" data-action="copy">
            <i class="fas fa-copy"></i>
            <span>কপি করুন</span>
            <span class="shortcut">Ctrl+C</span>
        </div>
        <div class="context-menu-item" data-action="cut">
            <i class="fas fa-cut"></i>
            <span>কাট করুন</span>
            <span class="shortcut">Ctrl+X</span>
        </div>
        <div class="context-menu-item" data-action="paste">
            <i class="fas fa-paste"></i>
            <span>পেস্ট করুন</span>
            <span class="shortcut">Ctrl+V</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" data-action="selectAll">
            <i class="fas fa-check-square"></i>
            <span>সব সিলেক্ট করুন</span>
            <span class="shortcut">Ctrl+A</span>
        </div>
        <div class="context-menu-item" data-action="delete">
            <i class="fas fa-trash"></i>
            <span>ডিলিট করুন</span>
            <span class="shortcut">Del</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" data-action="undo">
            <i class="fas fa-undo"></i>
            <span>আন্ডু করুন</span>
            <span class="shortcut">Ctrl+Z</span>
        </div>
        <div class="context-menu-item" data-action="redo">
            <i class="fas fa-redo"></i>
            <span>রিডু করুন</span>
            <span class="shortcut">Ctrl+Y</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" data-action="bold">
            <i class="fas fa-bold"></i>
            <span>বোল্ড করুন</span>
            <span class="shortcut">Ctrl+B</span>
        </div>
        <div class="context-menu-item" data-action="italic">
            <i class="fas fa-italic"></i>
            <span>ইটালিক করুন</span>
            <span class="shortcut">Ctrl+I</span>
        </div>
        <div class="context-menu-item" data-action="underline">
            <i class="fas fa-underline"></i>
            <span>আন্ডারলাইন করুন</span>
            <span class="shortcut">Ctrl+U</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" data-action="find">
            <i class="fas fa-search"></i>
            <span>খুঁজুন</span>
            <span class="shortcut">Ctrl+F</span>
        </div>
        <div class="context-menu-item" data-action="replace">
            <i class="fas fa-exchange-alt"></i>
            <span>প্রতিস্থাপন করুন</span>
            <span class="shortcut">Ctrl+H</span>
        </div>
    `;

    document.body.appendChild(contextMenu);

    // Add event listeners to context menu items
    contextMenu.addEventListener('click', handleContextMenuAction);

    return contextMenu;
}

function showContextMenu(event) {
    if (event && event.preventDefault) {
        event.preventDefault();
    }

    console.log('Context menu triggered'); // Debug log

    // Get selected text
    const selection = window.getSelection();
    selectedText = selection.toString().trim();

    console.log('Selected text:', selectedText); // Debug log

    // Don't show menu if no text is selected
    if (!selectedText || selectedText.length === 0) {
        hideContextMenu();
        return;
    }

    // Store selection range for later use
    if (selection.rangeCount > 0) {
        selectionRange = selection.getRangeAt(0).cloneRange();
    }

    // Create context menu if it doesn't exist
    if (!contextMenu) {
        createContextMenu();
    }

    // Position the context menu
    const x = event.clientX || 0;
    const y = event.clientY || 0;

    contextMenu.style.left = x + 'px';
    contextMenu.style.top = y + 'px';
    contextMenu.style.display = 'block';
    contextMenu.style.opacity = '1';
    isContextMenuVisible = true;

    // Adjust position if menu goes outside viewport
    const rect = contextMenu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (rect.right > viewportWidth) {
        contextMenu.style.left = Math.max(0, x - rect.width) + 'px';
    }

    if (rect.bottom > viewportHeight) {
        contextMenu.style.top = Math.max(0, y - rect.height) + 'px';
    }

    // Update menu items based on context
    updateContextMenuItems();
}

function hideContextMenu() {
    // Clear any pending timeout
    if (contextMenuTimeout) {
        clearTimeout(contextMenuTimeout);
        contextMenuTimeout = null;
    }

    if (contextMenu && contextMenu.style.display !== 'none') {
        isContextMenuVisible = false;
        lastSelectionText = '';

        // Fade out effect
        contextMenu.style.opacity = '0';
        setTimeout(() => {
            if (contextMenu && !isContextMenuVisible) {
                contextMenu.style.display = 'none';
                contextMenu.style.opacity = '1'; // Reset for next show
            }
        }, 150);
    }
}

function updateContextMenuItems() {
    if (!contextMenu) return;

    const hasSelection = selectedText.length > 0;
    const copyItem = contextMenu.querySelector('[data-action="copy"]');
    const cutItem = contextMenu.querySelector('[data-action="cut"]');
    const deleteItem = contextMenu.querySelector('[data-action="delete"]');
    const boldItem = contextMenu.querySelector('[data-action="bold"]');
    const italicItem = contextMenu.querySelector('[data-action="italic"]');
    const underlineItem = contextMenu.querySelector('[data-action="underline"]');

    // Enable/disable items based on selection
    if (copyItem) copyItem.classList.toggle('disabled', !hasSelection);
    if (cutItem) cutItem.classList.toggle('disabled', !hasSelection);
    if (deleteItem) deleteItem.classList.toggle('disabled', !hasSelection);
    if (boldItem) boldItem.classList.toggle('disabled', !hasSelection);
    if (italicItem) italicItem.classList.toggle('disabled', !hasSelection);
    if (underlineItem) underlineItem.classList.toggle('disabled', !hasSelection);
}

function handleContextMenuAction(event) {
    event.preventDefault();
    event.stopPropagation();

    const target = event.target.closest('.context-menu-item');
    if (!target || target.classList.contains('disabled')) {
        return;
    }

    const action = target.getAttribute('data-action');

    // Hide context menu immediately
    isContextMenuVisible = false;
    hideContextMenu();

    // Execute action
    switch (action) {
        case 'copy':
            executeContextAction('copy');
            break;
        case 'cut':
            executeContextAction('cut');
            break;
        case 'paste':
            executeContextAction('paste');
            break;
        case 'selectAll':
            executeContextAction('selectAll');
            break;
        case 'delete':
            executeContextAction('delete');
            break;
        case 'undo':
            executeContextAction('undo');
            break;
        case 'redo':
            executeContextAction('redo');
            break;
        case 'bold':
            executeContextAction('bold');
            break;
        case 'italic':
            executeContextAction('italic');
            break;
        case 'underline':
            executeContextAction('underline');
            break;
        case 'find':
            executeContextAction('find');
            break;
        case 'replace':
            executeContextAction('replace');
            break;
    }
}

function executeContextAction(action) {
    if (!joditEditor) return;

    try {
        switch (action) {
            case 'copy':
                if (selectedText) {
                    navigator.clipboard.writeText(selectedText).then(() => {
                        showNotification('টেক্সট কপি করা হয়েছে', 'success');
                    }).catch(() => {
                        // Fallback for older browsers
                        document.execCommand('copy');
                        showNotification('টেক্সট কপি করা হয়েছে', 'success');
                    });
                }
                break;

            case 'cut':
                if (selectedText) {
                    navigator.clipboard.writeText(selectedText).then(() => {
                        joditEditor.execCommand('delete');
                        showNotification('টেক্সট কাট করা হয়েছে', 'success');
                    }).catch(() => {
                        // Fallback for older browsers
                        document.execCommand('cut');
                        showNotification('টেক্সট কাট করা হয়েছে', 'success');
                    });
                }
                break;

            case 'paste':
                navigator.clipboard.readText().then(text => {
                    if (text) {
                        if (joditEditor && joditEditor.selection) {
                            joditEditor.selection.insertHTML(text);
                        } else {
                            joditEditor.execCommand('insertHTML', false, text);
                        }
                        showNotification('টেক্সট পেস্ট করা হয়েছে', 'success');
                    }
                }).catch(() => {
                    // Fallback for older browsers
                    try {
                        joditEditor.execCommand('paste');
                        showNotification('টেক্সট পেস্ট করা হয়েছে', 'success');
                    } catch (e) {
                        showNotification('পেস্ট করতে সমস্যা হয়েছে', 'error');
                    }
                });
                break;

            case 'selectAll':
                joditEditor.execCommand('selectAll');
                showNotification('সব টেক্সট সিলেক্ট করা হয়েছে', 'info');
                break;

            case 'delete':
                if (selectedText) {
                    joditEditor.execCommand('delete');
                    showNotification('টেক্সট ডিলিট করা হয়েছে', 'success');
                }
                break;

            case 'undo':
                joditEditor.execCommand('undo');
                showNotification('আন্ডু করা হয়েছে', 'info');
                break;

            case 'redo':
                joditEditor.execCommand('redo');
                showNotification('রিডু করা হয়েছে', 'info');
                break;

            case 'bold':
                if (selectedText) {
                    joditEditor.execCommand('bold');
                    showNotification('টেক্সট বোল্ড করা হয়েছে', 'success');
                }
                break;

            case 'italic':
                if (selectedText) {
                    joditEditor.execCommand('italic');
                    showNotification('টেক্সট ইটালিক করা হয়েছে', 'success');
                }
                break;

            case 'underline':
                if (selectedText) {
                    joditEditor.execCommand('underline');
                    showNotification('টেক্সট আন্ডারলাইন করা হয়েছে', 'success');
                }
                break;

            case 'find':
                try {
                    // Try to open Jodit's search dialog
                    if (joditEditor.commands && joditEditor.commands.search) {
                        joditEditor.execCommand('search');
                    } else {
                        // Fallback: use browser's find
                        document.execCommand('find');
                    }
                    showNotification('সার্চ বক্স খোলা হয়েছে', 'info');
                } catch (e) {
                    // Ultimate fallback: show Ctrl+F message
                    showNotification('Ctrl+F চেপে সার্চ করুন', 'info');
                }
                break;

            case 'replace':
                try {
                    // Try to open Jodit's replace dialog
                    if (joditEditor.commands && joditEditor.commands.replace) {
                        joditEditor.execCommand('replace');
                    } else {
                        showNotification('Ctrl+H চেপে রিপ্লেস করুন', 'info');
                    }
                } catch (e) {
                    showNotification('Ctrl+H চেপে রিপ্লেস করুন', 'info');
                }
                break;
        }
    } catch (error) {
        console.error('Context action error:', error);
        showNotification('অ্যাকশন সম্পাদনে সমস্যা হয়েছে', 'error');
    }
}

function setupContextMenu() {
    // Hide context menu on click outside (but not on the menu itself)
    document.addEventListener('click', function(event) {
        if (contextMenu && !contextMenu.contains(event.target)) {
            // Check if click is in editor area
            const editorElement = document.querySelector('.jodit-wysiwyg');
            if (!editorElement || !editorElement.contains(event.target)) {
                hideContextMenu();
            }
        }
    });

    // Hide context menu on scroll
    document.addEventListener('scroll', hideContextMenu);

    // Hide context menu on window resize
    window.addEventListener('resize', hideContextMenu);

    // Hide context menu on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            hideContextMenu();
        }
    });

    // Disable global selection change listener to prevent conflicts
    // We'll rely on editor-specific events instead

    // Setup context menu when Jodit editor is ready
    setTimeout(() => {
        if (joditEditor && joditEditor.editor) {
            // Add selection listener to Jodit editor specifically
            joditEditor.editor.addEventListener('mouseup', handleEditorSelection);
            joditEditor.editor.addEventListener('keyup', handleEditorSelection);

            // Also prevent default context menu in editor
            joditEditor.editor.addEventListener('contextmenu', function(event) {
                event.preventDefault();
            });
        }
    }, 1000);
}

// Removed handleSelectionChange function as we're using editor-specific events instead

function handleEditorSelection(event) {
    // Clear any existing timeout
    if (contextMenuTimeout) {
        clearTimeout(contextMenuTimeout);
    }

    // Small delay to ensure selection is complete and avoid too frequent updates
    contextMenuTimeout = setTimeout(() => {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
            const selectedText = selection.toString().trim();
            console.log('Editor selection changed:', selectedText);

            if (selectedText.length > 0) {
                lastSelectionText = selectedText;
                showContextMenuAtSelection(selection);
            } else {
                // Only hide if no text is selected and menu is not being used
                if (!isContextMenuVisible || !contextMenu || !contextMenu.matches(':hover')) {
                    hideContextMenu();
                }
            }
        }
    }, 100); // Reduced delay for better responsiveness
}

function showContextMenuAtSelection(selection) {
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // Create a fake event object with position
    const fakeEvent = {
        clientX: rect.right + 10, // Position slightly to the right of selection
        clientY: rect.top - 10,   // Position slightly above selection
        preventDefault: () => {}
    };

    showContextMenu(fakeEvent);
}

// ===== ENHANCED REMINDER SYSTEM =====

// Initialize reminder system
function initializeReminderSystem() {
    // Start daily reminder check (every 10 seconds for testing, change to daily in production)
    dailyReminderCheckInterval = setInterval(checkDailyReminders, 10000); // Check every 10 seconds for testing

    // Check reminders immediately on load
    setTimeout(checkDailyReminders, 3000);

    console.log('Reminder system initialized');
}

// Check for due reminders daily
function checkDailyReminders() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    notes.forEach(note => {
        if (note.reminder) {
            const reminderDate = new Date(note.reminder);
            const reminderDay = new Date(reminderDate.getFullYear(), reminderDate.getMonth(), reminderDate.getDate());

            // Check if reminder is due today
            if (reminderDay.getTime() === today.getTime()) {
                // Check if we haven't already shown this reminder today
                const reminderKey = `${note.id}_${today.getTime()}`;
                const lastShown = localStorage.getItem(`reminder_shown_${reminderKey}`);

                if (!lastShown) {
                    showReminderNotification(note);
                    // Mark as shown for today
                    localStorage.setItem(`reminder_shown_${reminderKey}`, now.getTime().toString());
                }
            }
        }
    });
}

// Legacy function - now handled by the main showReminderNotification function

// Show custom reminder modal
function showCustomReminderModal(note) {
    const modal = document.createElement('div');
    modal.className = 'reminder-modal-overlay';
    modal.innerHTML = `
        <div class="reminder-modal">
            <div class="reminder-modal-header">
                <h3>🔔 রিমাইন্ডার</h3>
                <button class="reminder-modal-close" onclick="closeReminderModal(this)">&times;</button>
            </div>
            <div class="reminder-modal-body">
                <h4>${note.title}</h4>
                <p>আজকের জন্য এই নোটের রিমাইন্ডার সেট করা আছে।</p>
                <div class="reminder-modal-actions">
                    <button class="btn btn-primary" onclick="openNoteFromReminder('${note.id}')">নোট খুলুন</button>
                    <button class="btn btn-secondary" onclick="closeReminderModal(this)">পরে দেখব</button>
                    <button class="btn btn-warning" onclick="snoozeReminder('${note.id}')">১ ঘন্টা পরে মনে করিয়ে দিন</button>
                </div>
            </div>
        </div>
    `;

    // Add to reminder modal container
    const container = document.getElementById('reminderModalContainer');
    if (container) {
        container.appendChild(modal);
    } else {
        document.body.appendChild(modal);
    }

    // Auto close after 30 seconds if no interaction
    setTimeout(() => {
        if (document.body.contains(modal)) {
            modal.remove();
        }
    }, 30000);
}

// Show in-app notification panel
function showInAppReminderNotification(note) {
    const notificationPanel = document.getElementById('reminderNotificationPanel');

    if (!notificationPanel) {
        console.error('Reminder notification panel not found');
        return;
    }

    // Get the notification body
    const notificationBody = notificationPanel.querySelector('.reminder-notification-body') ||
                            notificationPanel.querySelector('#reminderNotificationBody');

    const notificationItem = document.createElement('div');
    notificationItem.className = 'reminder-notification-item';
    notificationItem.innerHTML = `
        <div class="reminder-notification-icon">🔔</div>
        <div class="reminder-notification-content">
            <div class="reminder-notification-title">রিমাইন্ডার: ${note.title}</div>
            <div class="reminder-notification-message">আজকের জন্য রিমাইন্ডার সেট করা আছে</div>
            <div class="reminder-notification-time">${new Date().toLocaleTimeString('bn-BD')}</div>
        </div>
        <div class="reminder-notification-actions">
            <button class="btn btn-sm btn-primary" onclick="openNoteFromReminder('${note.id}')">খুলুন</button>
            <button class="btn btn-sm btn-secondary" onclick="removeReminderNotification(this)">বন্ধ করুন</button>
        </div>
    `;

    // Hide empty state if visible
    const emptyState = notificationBody ? notificationBody.querySelector('.no-reminder-notifications') : null;
    if (emptyState) {
        emptyState.style.display = 'none';
    }

    // Add to notification body
    if (notificationBody) {
        notificationBody.appendChild(notificationItem);
    } else {
        notificationPanel.appendChild(notificationItem);
    }

    // Show notification panel with animation
    notificationPanel.classList.add('show');

    // Auto remove after 2 minutes
    setTimeout(() => {
        if (notificationItem.parentNode) {
            notificationItem.style.transition = 'all 0.3s ease-out';
            notificationItem.style.opacity = '0';
            notificationItem.style.transform = 'translateX(100%)';

            setTimeout(() => {
                if (notificationItem.parentNode) {
                    notificationItem.remove();

                    // Check if notification body is empty
                    const remainingItems = notificationBody ?
                        notificationBody.querySelectorAll('.reminder-notification-item') :
                        notificationPanel.querySelectorAll('.reminder-notification-item');

                    if (remainingItems.length === 0) {
                        // Show empty state
                        const emptyState = notificationBody ? notificationBody.querySelector('.no-reminder-notifications') : null;
                        if (emptyState) {
                            emptyState.style.display = 'block';
                        }

                        // Hide panel after showing empty state
                        setTimeout(() => {
                            notificationPanel.classList.remove('show');
                        }, 2000);
                    }
                }
            }, 300);
        }
    }, 120000);
}

// Create reminder notification panel if it doesn't exist
function createReminderNotificationPanel() {
    const panel = document.createElement('div');
    panel.id = 'reminderNotificationPanel';
    panel.className = 'reminder-notification-panel';
    panel.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        width: 380px;
        max-width: calc(100vw - 40px);
        max-height: 500px;
        overflow-y: auto;
        background: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        z-index: 10001;
        display: none;
        padding: 0;
    `;

    document.body.appendChild(panel);
    return panel;
}

// Play notification sound
function playNotificationSound() {
    try {
        // Create audio element if not exists
        if (!notificationSound) {
            notificationSound = new Audio();
            // Use a simple beep sound or notification sound
            notificationSound.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
        }

        notificationSound.volume = notificationVolume;
        notificationSound.play().catch(e => {
            console.log('Could not play notification sound:', e);
            // Fallback: try to create a beep using Web Audio API
            createBeepSound();
        });
    } catch (error) {
        console.log('Error playing notification sound:', error);
        createBeepSound();
    }
}

// Create beep sound using Web Audio API
function createBeepSound() {
    // Initialize AudioContext if not already done
    if (!audioContextInitialized) {
        initializeAudioContext();
    }

    if (!globalAudioContext) {
        console.log('AudioContext not available for beep sound');
        return;
    }

    try {
        const oscillator = globalAudioContext.createOscillator();
        const gainNode = globalAudioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(globalAudioContext.destination);

        oscillator.frequency.value = 800; // Frequency in Hz
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, globalAudioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3 * notificationVolume, globalAudioContext.currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.01, globalAudioContext.currentTime + 0.5);

        oscillator.start(globalAudioContext.currentTime);
        oscillator.stop(globalAudioContext.currentTime + 0.5);
    } catch (error) {
        console.log('Could not create beep sound:', error);
    }
}

// Helper functions for reminder modal actions
function closeReminderModal(button) {
    const modal = button.closest('.reminder-modal-overlay');
    if (modal) {
        modal.remove();
    }
}

function openNoteFromReminder(noteId) {
    // Close any open reminder modals
    const modals = document.querySelectorAll('.reminder-modal-overlay');
    modals.forEach(modal => modal.remove());

    // Close reminder notification panel
    const notificationPanel = document.getElementById('reminderNotificationPanel');
    if (notificationPanel) {
        notificationPanel.classList.remove('show');
    }

    // Check if it's a test note (but still try to open it if it exists)
    if (noteId.startsWith('test_reminder_')) {
        // First check if the test note exists in notes array
        const testNote = notes.find(n => n.id === noteId);
        if (testNote) {
            // Test note exists, open it normally
            editNote(noteId);
            showNotification('টেস্ট রিমাইন্ডার নোট খোলা হয়েছে!', 'success');
            return;
        } else {
            // Test note doesn't exist, show message
            showNotification('টেস্ট নোটটি খুঁজে পাওয়া যায়নি। নতুন টেস্ট চালান।', 'warning');
            return;
        }
    }

    // Find the note in the notes array
    const note = notes.find(n => n.id === noteId);
    if (note) {
        // Open the note
        editNote(noteId);
        showNotification('নোট খোলা হয়েছে!', 'success');
    } else {
        showNotification('নোটটি খুঁজে পাওয়া যায়নি। এটি হয়তো ডিলেট করা হয়েছে।', 'warning');
    }
}

function snoozeReminder(noteId) {
    // Close modal
    const modals = document.querySelectorAll('.reminder-modal-overlay');
    modals.forEach(modal => modal.remove());

    // Set reminder for 1 hour later
    setTimeout(() => {
        const note = notes.find(n => n.id === noteId);
        if (note) {
            showReminderNotification(note);
        }
    }, 60 * 60 * 1000); // 1 hour

    showNotification('রিমাইন্ডার ১ ঘন্টা পরে আবার দেখানো হবে', 'info');
}

function removeReminderNotification(button) {
    const notificationItem = button.closest('.reminder-notification-item');
    if (notificationItem) {
        notificationItem.style.transition = 'all 0.3s ease-out';
        notificationItem.style.opacity = '0';
        notificationItem.style.transform = 'translateX(100%)';

        setTimeout(() => {
            notificationItem.remove();

            // Hide panel if no notifications left
            const panel = document.getElementById('reminderNotificationPanel');
            const notificationBody = panel ? panel.querySelector('.reminder-notification-body') : null;
            const remainingItems = notificationBody ?
                notificationBody.querySelectorAll('.reminder-notification-item') :
                panel ? panel.querySelectorAll('.reminder-notification-item') : [];

            if (remainingItems.length === 0 && panel) {
                // Show empty state
                const emptyState = notificationBody ? notificationBody.querySelector('.no-reminder-notifications') : null;
                if (emptyState) {
                    emptyState.style.display = 'block';
                }

                // Hide panel after a delay
                setTimeout(() => {
                    panel.classList.remove('show');
                }, 2000);
            }
        }, 300);
    }
}

// Close all reminder notifications
function closeAllReminderNotifications() {
    const panel = document.getElementById('reminderNotificationPanel');
    if (panel) {
        panel.classList.remove('show');

        // Clear notification body content but keep empty state
        const notificationBody = panel.querySelector('.reminder-notification-body') ||
                                panel.querySelector('#reminderNotificationBody');
        if (notificationBody) {
            // Remove all notification items but keep empty state
            const notificationItems = notificationBody.querySelectorAll('.reminder-notification-item');
            notificationItems.forEach(item => item.remove());

            // Show empty state
            const emptyState = notificationBody.querySelector('.no-reminder-notifications');
            if (emptyState) {
                emptyState.style.display = 'block';
            }
        }
    }
}

// Initialize notification manager and setup UI
function initializeNotificationManager() {
    if (window.notificationManager) {
        // Update UI with current status
        window.notificationManager.updateUI();

        // Setup settings UI event listeners
        setupNotificationSettingsUI();

        console.log('Notification manager initialized');
    } else {
        console.warn('Notification manager not available');
    }
}

// Setup notification settings UI event listeners
function setupNotificationSettingsUI() {
    // Get UI elements
    const notificationStatus = document.getElementById('notificationStatus');
    const requestPermissionBtn = document.getElementById('requestPermissionBtn');
    const openBrowserSettingsBtn = document.getElementById('openBrowserSettingsBtn');
    const notificationEnabled = document.getElementById('notificationEnabled');
    const notificationSound = document.getElementById('notificationSound');
    const reminderNotifications = document.getElementById('reminderNotifications');
    const autoSaveNotifications = document.getElementById('autoSaveNotifications');
    const testNotificationBtn = document.getElementById('testNotificationBtn');
    const resetNotificationSettingsBtn = document.getElementById('resetNotificationSettingsBtn');

    if (!window.notificationManager) return;

    // Update status display
    function updateStatusDisplay() {
        const status = window.notificationManager.checkPermissionStatus();
        const statusText = window.notificationManager.getStatusText();

        if (notificationStatus) {
            notificationStatus.textContent = statusText;
            notificationStatus.className = `notification-status status-${status}`;
        }

        // Show/hide buttons based on status
        if (requestPermissionBtn) {
            requestPermissionBtn.style.display = (status === 'default' || status === 'denied') ? 'inline-block' : 'none';
        }

        if (openBrowserSettingsBtn) {
            openBrowserSettingsBtn.style.display = status === 'denied' ? 'inline-block' : 'none';
        }
    }

    // Load current settings
    function loadCurrentSettings() {
        const settings = window.notificationManager.getSettings();

        if (notificationEnabled) notificationEnabled.checked = settings.enabled;
        if (notificationSound) notificationSound.checked = settings.soundEnabled;
        if (reminderNotifications) reminderNotifications.checked = settings.reminderEnabled;
        if (autoSaveNotifications) autoSaveNotifications.checked = settings.autoSaveNotifications || false;
    }

    // Event listeners
    if (requestPermissionBtn) {
        requestPermissionBtn.addEventListener('click', async () => {
            await window.notificationManager.requestPermission();
            updateStatusDisplay();
        });
    }

    if (openBrowserSettingsBtn) {
        openBrowserSettingsBtn.addEventListener('click', () => {
            showNotification('ব্রাউজার সেটিংস', 'ব্রাউজারের সেটিংস > গোপনীয়তা ও নিরাপত্তা > সাইট সেটিংস > নোটিফিকেশন থেকে পরিবর্তন করুন', 'info');
        });
    }

    if (notificationEnabled) {
        notificationEnabled.addEventListener('change', (e) => {
            window.notificationManager.setEnabled(e.target.checked);
        });
    }

    if (notificationSound) {
        notificationSound.addEventListener('change', (e) => {
            window.notificationManager.setSoundEnabled(e.target.checked);
        });
    }

    if (testNotificationBtn) {
        testNotificationBtn.addEventListener('click', async () => {
            const result = await window.notificationManager.showNotification(
                'টেস্ট নোটিফিকেশন',
                'এটি একটি টেস্ট নোটিফিকেশন। যদি আপনি এটি দেখতে পান তাহলে নোটিফিকেশন কাজ করছে!',
                { requireInteraction: true }
            );

            if (!result) {
                showNotification('নোটিফিকেশন টেস্ট', 'নোটিফিকেশন দেখানো যায়নি। অনুমতি চেক করুন।', 'warning');
            }
        });
    }

    if (resetNotificationSettingsBtn) {
        resetNotificationSettingsBtn.addEventListener('click', () => {
            window.notificationManager.resetPermissionDialog();
            window.notificationManager.setEnabled(true);
            window.notificationManager.setSoundEnabled(true);
            loadCurrentSettings();
            showNotification('সেটিংস রিসেট', 'নোটিফিকেশন সেটিংস রিসেট করা হয়েছে', 'success');
        });
    }

    // Initial setup
    updateStatusDisplay();
    loadCurrentSettings();
}

// Legacy function for backward compatibility
function requestNotificationPermission() {
    if (window.notificationManager) {
        return window.notificationManager.requestPermission();
    }
    return Promise.resolve('unsupported');
}

// ===== BULK DOWNLOAD FUNCTIONALITY =====
async function bulkDownloadNotes() {
    try {
        // Check if JSZip is available
        if (typeof JSZip === 'undefined') {
            showNotification('ত্রুটি', 'JSZip library লোড হয়নি। পেজ রিফ্রেশ করে আবার চেষ্টা করুন।', 'error');
            return;
        }

        // Check if there are notes to download
        if (!notes || notes.length === 0) {
            showNotification('কোন নোট নেই', 'ডাউনলোড করার জন্য কোন নোট পাওয়া যায়নি।', 'warning');
            return;
        }

        // Show progress modal
        showDownloadProgressModal();

        // Initialize progress
        updateDownloadProgress(0, notes.length, 0, 'প্রস্তুত করা হচ্ছে...');

        // Add loading state to button
        const downloadBtn = document.getElementById('bulkDownloadBtn');
        if (downloadBtn) {
            downloadBtn.classList.add('loading');
            downloadBtn.disabled = true;
        }

        // Create new JSZip instance
        const zip = new JSZip();
        let totalSize = 0;

        // Process each note with progress updates
        for (let i = 0; i < notes.length; i++) {
            const note = notes[i];

            // Update progress
            const progressText = `প্রক্রিয়াকরণ: ${note.title || `নোট ${i + 1}`}`;
            updateDownloadProgress(i, notes.length, totalSize, progressText);

            // Convert note to text format
            const txtContent = convertNoteToTxt(note);
            totalSize += txtContent.length;

            // Create safe filename
            const safeTitle = sanitizeFilename(note.title || `নোট-${i + 1}`);
            const filename = `${safeTitle}.txt`;

            // Add file to zip
            zip.file(filename, txtContent);

            // Small delay to allow UI updates
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Update progress for zip generation
        updateDownloadProgress(notes.length, notes.length, totalSize, 'ZIP ফাইল তৈরি করা হচ্ছে...');

        const zipBlob = await zip.generateAsync({
            type: 'blob',
            compression: 'DEFLATE',
            compressionOptions: {
                level: 6
            }
        });

        // Update progress for download preparation
        updateDownloadProgress(notes.length, notes.length, zipBlob.size, 'ডাউনলোড প্রস্তুত করা হচ্ছে...');

        // Create download link
        const downloadUrl = URL.createObjectURL(zipBlob);
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;

        // Create filename with current date
        const currentDate = new Date();
        const dateStr = currentDate.toISOString().split('T')[0];
        downloadLink.download = `আমার-নোটসমূহ-${dateStr}.zip`;

        // Trigger download
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Clean up
        URL.revokeObjectURL(downloadUrl);

        // Hide progress modal
        hideDownloadProgressModal();

        // Remove loading state from button
        if (downloadBtn) {
            downloadBtn.classList.remove('loading');
            downloadBtn.disabled = false;
        }

        // Show success message
        showNotification('সফল!', `${notes.length}টি নোট সফলভাবে ডাউনলোড হয়েছে (${formatFileSize(zipBlob.size)})`, 'success');

    } catch (error) {
        console.error('Bulk download error:', error);

        // Hide progress modal
        hideDownloadProgressModal();

        // Remove loading state from button
        const downloadBtn = document.getElementById('bulkDownloadBtn');
        if (downloadBtn) {
            downloadBtn.classList.remove('loading');
            downloadBtn.disabled = false;
        }

        showNotification('ত্রুটি', 'নোট ডাউনলোড করতে সমস্যা হয়েছে। আবার চেষ্টা করুন।', 'error');
    }
}

// Convert note object to formatted text
function convertNoteToTxt(note) {
    let content = '';

    // Add title
    if (note.title) {
        content += `শিরোনাম: ${note.title}\n`;
        content += '='.repeat(note.title.length + 10) + '\n\n';
    }

    // Add metadata
    content += 'তথ্য:\n';
    content += '-----\n';

    if (note.created) {
        const createdDate = new Date(note.created);
        content += `তৈরি: ${createdDate.toLocaleString('bn-BD')}\n`;
    }

    if (note.updated) {
        const updatedDate = new Date(note.updated);
        content += `আপডেট: ${updatedDate.toLocaleString('bn-BD')}\n`;
    }

    if (note.priority) {
        const priorityMap = {
            'low': 'কম',
            'medium': 'মাধ্যম',
            'high': 'উচ্চ',
            'urgent': 'জরুরি'
        };
        content += `অগ্রাধিকার: ${priorityMap[note.priority] || note.priority}\n`;
    }

    if (note.reminder) {
        const reminderDate = new Date(note.reminder);
        content += `রিমাইন্ডার: ${reminderDate.toLocaleString('bn-BD')}\n`;
    }

    content += '\n';

    // Add tabs content if exists
    if (note.tabs && note.tabs.length > 0) {
        content += 'বিষয়বস্তু:\n';
        content += '--------\n\n';

        note.tabs.forEach((tab, index) => {
            if (tab.title) {
                content += `[ট্যাব ${index + 1}: ${tab.title}]\n`;
                content += '-'.repeat(tab.title.length + 15) + '\n';
            } else {
                content += `[ট্যাব ${index + 1}]\n`;
                content += '----------\n';
            }

            if (tab.content) {
                // Remove HTML tags and convert to plain text
                const plainText = stripHtmlTags(tab.content);
                content += plainText + '\n\n';
            }
        });
    } else if (note.content) {
        // Legacy single content
        content += 'বিষয়বস্তু:\n';
        content += '--------\n';
        const plainText = stripHtmlTags(note.content);
        content += plainText + '\n\n';
    }

    // Add attachments info if exists
    if (note.attachments && note.attachments.length > 0) {
        content += 'সংযুক্তি:\n';
        content += '-------\n';
        note.attachments.forEach((attachment, index) => {
            content += `${index + 1}. ${attachment.name} (${attachment.type})\n`;
        });
        content += '\n';
    }

    // Add footer
    content += '\n';
    content += '---\n';
    content += 'আমার স্মার্ট নোট থেকে রপ্তানি করা হয়েছে\n';
    content += `রপ্তানির তারিখ: ${new Date().toLocaleString('bn-BD')}\n`;

    return content;
}

// Remove HTML tags and convert to plain text
function stripHtmlTags(html) {
    if (!html) return '';

    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get text content and clean up
    let text = tempDiv.textContent || tempDiv.innerText || '';

    // Clean up extra whitespace
    text = text.replace(/\s+/g, ' ').trim();

    // Convert common HTML entities
    text = text.replace(/&nbsp;/g, ' ');
    text = text.replace(/&amp;/g, '&');
    text = text.replace(/&lt;/g, '<');
    text = text.replace(/&gt;/g, '>');
    text = text.replace(/&quot;/g, '"');
    text = text.replace(/&#39;/g, "'");

    return text;
}

// Sanitize filename for safe file system usage
function sanitizeFilename(filename) {
    // Remove or replace invalid characters
    let safe = filename.replace(/[<>:"/\\|?*]/g, '-');

    // Remove control characters
    safe = safe.replace(/[\x00-\x1f\x80-\x9f]/g, '');

    // Trim and limit length
    safe = safe.trim().substring(0, 100);

    // Ensure it's not empty
    if (!safe) {
        safe = 'নোট';
    }

    return safe;
}

// Show download progress modal
function showDownloadProgressModal() {
    const modal = document.getElementById('downloadProgressModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

// Hide download progress modal
function hideDownloadProgressModal() {
    const modal = document.getElementById('downloadProgressModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Update download progress
function updateDownloadProgress(processed, total, fileSize, statusText) {
    const progressText = document.getElementById('downloadProgressText');
    const progressBar = document.getElementById('downloadProgressBar');
    const progressPercentage = document.getElementById('downloadProgressPercentage');
    const totalNotesCount = document.getElementById('totalNotesCount');
    const processedNotesCount = document.getElementById('processedNotesCount');
    const fileSizeEstimate = document.getElementById('fileSizeEstimate');

    if (progressText) {
        progressText.textContent = statusText;
    }

    const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${percentage}%`;
    }

    if (totalNotesCount) {
        totalNotesCount.textContent = total.toString();
    }

    if (processedNotesCount) {
        processedNotesCount.textContent = processed.toString();
    }

    if (fileSizeEstimate) {
        if (fileSize > 0) {
            fileSizeEstimate.textContent = formatFileSize(fileSize);
        } else {
            fileSizeEstimate.textContent = 'গণনা করা হচ্ছে...';
        }
    }
}

// Format file size in human readable format
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Create sample notes for testing download functionality
function createSampleNotesForDownload() {
    const sampleNotes = [
        {
            id: generateId(),
            title: 'আমার প্রথম নোট',
            tabs: [{
                title: 'মূল বিষয়বস্তু',
                content: '<p>এটি একটি নমুনা নোট। এখানে আপনি আপনার চিন্তাভাবনা, পরিকল্পনা এবং গুরুত্বপূর্ণ তথ্য লিখতে পারেন।</p><p><strong>বৈশিষ্ট্যসমূহ:</strong></p><ul><li>রিচ টেক্সট এডিটিং</li><li>মাল্টিপল ট্যাব সাপোর্ট</li><li>পাসওয়ার্ড প্রোটেকশন</li><li>রিমাইন্ডার সেট করা</li></ul>'
            }],
            priority: 'medium',
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        },
        {
            id: generateId(),
            title: 'কাজের তালিকা',
            tabs: [{
                title: 'আজকের কাজ',
                content: '<h3>আজকের করণীয়:</h3><ol><li>প্রজেক্ট রিপোর্ট তৈরি করা</li><li>ক্লায়েন্টের সাথে মিটিং</li><li>ইমেইল চেক করা</li><li>নতুন ফিচার ডিজাইন করা</li></ol><p><em>সময়সীমা: আজ সন্ধ্যা ৬টা</em></p>'
            }, {
                title: 'আগামীকালের পরিকল্পনা',
                content: '<h3>আগামীকালের জন্য:</h3><ul><li>কোড রিভিউ</li><li>টেস্টিং</li><li>ডকুমেন্টেশন আপডেট</li></ul>'
            }],
            priority: 'high',
            created: new Date(Date.now() - 86400000).toISOString(), // Yesterday
            updated: new Date().toISOString()
        },
        {
            id: generateId(),
            title: 'গুরুত্বপূর্ণ তথ্য',
            tabs: [{
                title: 'যোগাযোগের তথ্য',
                content: '<h3>জরুরি যোগাযোগ:</h3><p><strong>অফিস:</strong> ০১৭১২৩৪৫৬৭৮</p><p><strong>ইমেইল:</strong> <EMAIL></p><p><strong>ঠিকানা:</strong> ঢাকা, বাংলাদেশ</p>'
            }],
            priority: 'urgent',
            created: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
            updated: new Date().toISOString()
        }
    ];

    // Add sample notes to the notes array
    notes.push(...sampleNotes);
    saveNotes();
    updateNotesDisplay();

    // Show notification about sample data
    setTimeout(() => {
        showNotification('নমুনা ডেটা', 'কয়েকটি নমুনা নোট তৈরি করা হয়েছে। এখন ডাউনলোড বাটন টেস্ট করতে পারেন!', 'success');
    }, 3000);
}



// Enhanced note thumbnail to show reminder indicator
function updateNoteThumbnailWithReminder(noteElement, note) {
    // Remove existing reminder indicator
    const existingIndicator = noteElement.querySelector('.reminder-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Add reminder indicator if note has reminder
    if (note.reminder) {
        const reminderDate = new Date(note.reminder);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const reminderDay = new Date(reminderDate.getFullYear(), reminderDate.getMonth(), reminderDate.getDate());

        const indicator = document.createElement('div');
        indicator.className = 'reminder-indicator';

        // Check if reminder is today, overdue, or future
        if (reminderDay.getTime() === today.getTime()) {
            indicator.innerHTML = '🔔';
            indicator.title = 'আজকের রিমাইন্ডার';
            indicator.classList.add('reminder-today');
        } else if (reminderDay.getTime() < today.getTime()) {
            indicator.innerHTML = '⚠️';
            indicator.title = 'মেয়াদোত্তীর্ণ রিমাইন্ডার';
            indicator.classList.add('reminder-overdue');
        } else {
            indicator.innerHTML = '⏰';
            indicator.title = `রিমাইন্ডার: ${reminderDate.toLocaleDateString('bn-BD')}`;
            indicator.classList.add('reminder-future');
        }

        noteElement.appendChild(indicator);
    }
}

// Update all note thumbnails with reminder indicators
function updateAllNoteThumbnailsWithReminders() {
    const noteElements = document.querySelectorAll('.note-item');
    noteElements.forEach(noteElement => {
        const noteId = noteElement.dataset.noteId;
        const note = notes.find(n => n.id === noteId);
        if (note) {
            updateNoteThumbnailWithReminder(noteElement, note);
        }
    });
}

// Test reminder system function
function testReminderSystem() {
    // Create a real test note and add it to notes array
    const testNote = {
        id: 'test_reminder_' + Date.now(),
        title: '🔔 টেস্ট রিমাইন্ডার নোট',
        content: '<p>এটি একটি টেস্ট রিমাইন্ডার নোট। এই নোটটি রিমাইন্ডার সিস্টেম টেস্ট করার জন্য তৈরি করা হয়েছে।</p><p><strong>বৈশিষ্ট্য:</strong></p><ul><li>রিমাইন্ডার নোটিফিকেশন</li><li>ক্লিক করে নোট খোলা</li><li>স্বয়ংক্রিয় সেভ</li></ul>',
        priority: 'high',
        reminder: new Date().toISOString(), // Set reminder for now
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tabs: [
            {
                id: 'tab_' + Date.now(),
                title: 'মূল বিষয়বস্তু',
                content: '<p>এটি একটি টেস্ট রিমাইন্ডার নোট। এই নোটটি রিমাইন্ডার সিস্টেম টেস্ট করার জন্য তৈরি করা হয়েছে।</p><p><strong>বৈশিষ্ট্য:</strong></p><ul><li>রিমাইন্ডার নোটিফিকেশন</li><li>ক্লিক করে নোট খোলা</li><li>স্বয়ংক্রিয় সেভ</li></ul>',
                isActive: true
            }
        ]
    };

    // Add to notes array so it can be opened
    notes.unshift(testNote);

    // Save to localStorage
    saveNotes();

    // Update display
    updateNotesDisplay();

    // Show test reminder notification immediately
    showReminderNotification(testNote);

    // Show success message
    const currentSound = getCurrentNotificationSound();
    const soundName = getSoundDisplayName(currentSound);
    showNotification(`রিমাইন্ডার সিস্টেম টেস্ট করা হচ্ছে! নোটিফিকেশন প্যানেল দেখুন। "${soundName}" সাউন্ড বাজবে।`, 'info');
}

// ===== BACKGROUND CUSTOMIZATION SYSTEM =====

// Initialize background customization
function initializeBackgroundCustomization() {
    // Background type toggle
    const bgTypeButtons = document.querySelectorAll('.bg-type-btn');
    bgTypeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            bgTypeButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            const type = btn.dataset.type;
            showBackgroundOptions(type);
        });
    });

    // Color picker
    const colorPicker = document.getElementById('backgroundColorPicker');
    if (colorPicker) {
        colorPicker.addEventListener('change', (e) => {
            updateBackgroundPreview('solid', e.target.value);
        });
    }

    // Preset colors
    const presetColors = document.querySelectorAll('.preset-color');
    presetColors.forEach(color => {
        color.addEventListener('click', () => {
            presetColors.forEach(c => c.classList.remove('active'));
            color.classList.add('active');

            const colorValue = color.dataset.color;
            if (colorPicker) colorPicker.value = colorValue;
            updateBackgroundPreview('solid', colorValue);
        });
    });

    // Gradient presets
    const gradientPresets = document.querySelectorAll('.gradient-preset');
    gradientPresets.forEach(gradient => {
        gradient.addEventListener('click', () => {
            gradientPresets.forEach(g => g.classList.remove('active'));
            gradient.classList.add('active');

            const gradientValue = gradient.dataset.gradient;
            updateBackgroundPreview('gradient', gradientValue);
        });
    });

    // Pattern presets
    const patternPresets = document.querySelectorAll('.pattern-preset');
    patternPresets.forEach(pattern => {
        pattern.addEventListener('click', () => {
            patternPresets.forEach(p => p.classList.remove('active'));
            pattern.classList.add('active');

            const patternValue = pattern.dataset.pattern;
            updateBackgroundPreview('pattern', patternValue);
        });
    });

    // Action buttons
    const applyBtn = document.getElementById('applyBackgroundBtn');
    const previewBtn = document.getElementById('previewBackgroundBtn');
    const resetBtn = document.getElementById('resetBackgroundBtn');

    if (applyBtn) {
        applyBtn.addEventListener('click', applyBackgroundChanges);
    }

    if (previewBtn) {
        previewBtn.addEventListener('click', previewBackgroundChanges);
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', resetBackgroundToDefault);
    }

    // Load saved background settings
    loadSavedBackgroundSettings();
}

// Show/hide background options based on type
function showBackgroundOptions(type) {
    const solidOptions = document.getElementById('solidColorOptions');
    const gradientOptions = document.getElementById('gradientOptions');
    const patternOptions = document.getElementById('patternOptions');

    // Hide all options first
    if (solidOptions) solidOptions.style.display = 'none';
    if (gradientOptions) gradientOptions.style.display = 'none';
    if (patternOptions) patternOptions.style.display = 'none';

    // Show selected option
    switch (type) {
        case 'solid':
            if (solidOptions) solidOptions.style.display = 'block';
            break;
        case 'gradient':
            if (gradientOptions) gradientOptions.style.display = 'block';
            break;
        case 'pattern':
            if (patternOptions) patternOptions.style.display = 'block';
            break;
    }
}

// Update background preview
function updateBackgroundPreview(type, value) {
    const body = document.body;

    // Store current background for reset
    if (!body.dataset.originalBackground) {
        body.dataset.originalBackground = getComputedStyle(body).background;
    }

    switch (type) {
        case 'solid':
            body.style.background = value;
            body.style.backgroundImage = 'none';
            break;
        case 'gradient':
            body.style.background = value;
            break;
        case 'pattern':
            applyPatternBackground(value);
            break;
    }

    // Store current settings for apply
    body.dataset.backgroundType = type;
    body.dataset.backgroundValue = value;
}

// Apply pattern background
function applyPatternBackground(pattern) {
    const body = document.body;
    const baseColor = getComputedStyle(body).getPropertyValue('--bg-color') || '#1a1a1a';
    const accentColor = getComputedStyle(body).getPropertyValue('--text-secondary') || '#666';

    switch (pattern) {
        case 'dots':
            body.style.background = baseColor;
            body.style.backgroundImage = `radial-gradient(circle, ${accentColor} 1px, transparent 1px)`;
            body.style.backgroundSize = '20px 20px';
            break;
        case 'grid':
            body.style.background = baseColor;
            body.style.backgroundImage = `
                linear-gradient(${accentColor} 1px, transparent 1px),
                linear-gradient(90deg, ${accentColor} 1px, transparent 1px)
            `;
            body.style.backgroundSize = '20px 20px';
            break;
        case 'diagonal':
            body.style.background = baseColor;
            body.style.backgroundImage = `repeating-linear-gradient(
                45deg,
                ${accentColor},
                ${accentColor} 1px,
                transparent 1px,
                transparent 20px
            )`;
            break;
        case 'waves':
            body.style.background = baseColor;
            body.style.backgroundImage = `
                radial-gradient(ellipse at top, ${accentColor}, transparent),
                radial-gradient(ellipse at bottom, ${accentColor}, transparent)
            `;
            body.style.backgroundSize = '20px 10px';
            body.style.backgroundPosition = '0 0, 10px 5px';
            break;
        case 'hexagon':
            body.style.background = baseColor;
            body.style.backgroundImage = `
                linear-gradient(30deg, transparent 24%, ${accentColor} 25%, ${accentColor} 26%, transparent 27%, transparent 74%, ${accentColor} 75%, ${accentColor} 76%, transparent 77%, transparent),
                linear-gradient(-30deg, transparent 24%, ${accentColor} 25%, ${accentColor} 26%, transparent 27%, transparent 74%, ${accentColor} 75%, ${accentColor} 76%, transparent 77%, transparent)
            `;
            body.style.backgroundSize = '24px 40px';
            break;
        case 'circuit':
            body.style.background = '#0a0a0a';
            body.style.backgroundImage = `
                linear-gradient(90deg, #00ff41 1px, transparent 1px),
                linear-gradient(180deg, #00ff41 1px, transparent 1px),
                radial-gradient(circle at 50% 50%, #00ff41 1px, transparent 1px)
            `;
            body.style.backgroundSize = '40px 40px, 40px 40px, 20px 20px';
            body.style.backgroundPosition = '0 0, 0 0, 10px 10px';
            break;
        case 'matrix':
            body.style.background = '#000000';
            body.style.backgroundImage = `
                linear-gradient(0deg, transparent 24%, #00ff00 25%, #00ff00 26%, transparent 27%, transparent 74%, #00ff00 75%, #00ff00 76%, transparent 77%, transparent),
                linear-gradient(90deg, transparent 24%, #00ff00 25%, #00ff00 26%, transparent 27%, transparent 74%, #00ff00 75%, #00ff00 76%, transparent 77%, transparent)
            `;
            body.style.backgroundSize = '16px 16px';
            break;
        case 'carbon':
            body.style.background = '#1a1a1a';
            body.style.backgroundImage = `
                radial-gradient(circle at 25% 25%, #333 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #333 2px, transparent 2px),
                linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
                linear-gradient(-45deg, #2a2a2a 25%, transparent 25%)
            `;
            body.style.backgroundSize = '16px 16px, 16px 16px, 8px 8px, 8px 8px';
            body.style.backgroundPosition = '0 0, 8px 8px, 0 0, 4px 4px';
            break;
        case 'neural':
            body.style.background = '#0d1117';
            body.style.backgroundImage = `
                radial-gradient(circle at 20% 20%, #4a9eff 1px, transparent 1px),
                radial-gradient(circle at 80% 80%, #4a9eff 1px, transparent 1px),
                linear-gradient(45deg, transparent 40%, rgba(74, 158, 255, 0.1) 50%, transparent 60%)
            `;
            body.style.backgroundSize = '30px 30px, 30px 30px, 60px 60px';
            body.style.backgroundPosition = '0 0, 15px 15px, 0 0';
            break;
        case 'geometric':
            body.style.background = '#1a1a1a';
            body.style.backgroundImage = `
                linear-gradient(45deg, #333 25%, transparent 25%),
                linear-gradient(-45deg, #333 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #333 75%),
                linear-gradient(-45deg, transparent 75%, #333 75%)
            `;
            body.style.backgroundSize = '32px 32px';
            body.style.backgroundPosition = '0 0, 0 16px, 16px -16px, -16px 0px';
            break;
        case 'constellation':
            body.style.background = '#0a0a0a';
            body.style.backgroundImage = `
                radial-gradient(circle at 10% 20%, #ffffff 1px, transparent 1px),
                radial-gradient(circle at 80% 80%, #ffffff 1px, transparent 1px),
                radial-gradient(circle at 40% 40%, #ffffff 0.5px, transparent 0.5px),
                linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.1) 49%, rgba(255, 255, 255, 0.1) 51%, transparent 52%)
            `;
            body.style.backgroundSize = '100px 100px, 60px 60px, 40px 40px, 200px 200px';
            body.style.backgroundPosition = '0 0, 50px 50px, 20px 20px, 0 0';
            break;
        case 'cyberpunk':
            body.style.background = '#000000';
            body.style.backgroundImage = `
                linear-gradient(90deg, #ff0080 1px, transparent 1px),
                linear-gradient(0deg, #00ffff 1px, transparent 1px),
                radial-gradient(circle at 25% 25%, #ff0080 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #00ffff 2px, transparent 2px)
            `;
            body.style.backgroundSize = '50px 50px, 50px 50px, 100px 100px, 100px 100px';
            body.style.backgroundPosition = '0 0, 0 0, 0 0, 50px 50px';
            break;
    }
}

// Apply background changes permanently
function applyBackgroundChanges() {
    const body = document.body;
    const type = body.dataset.backgroundType;
    const value = body.dataset.backgroundValue;

    if (type && value) {
        // Save to localStorage
        const backgroundSettings = {
            type: type,
            value: value,
            timestamp: Date.now()
        };

        localStorage.setItem('backgroundSettings', JSON.stringify(backgroundSettings));

        showNotification('ব্যাকগ্রাউন্ড সফলভাবে প্রয়োগ করা হয়েছে!', 'success');
    } else {
        showNotification('কোন ব্যাকগ্রাউন্ড সিলেক্ট করা হয়নি', 'warning');
    }
}

// Preview background changes
function previewBackgroundChanges() {
    const body = document.body;
    const type = body.dataset.backgroundType;
    const value = body.dataset.backgroundValue;

    if (type && value) {
        showNotification('প্রিভিউ দেখানো হচ্ছে - সেটিংস মোডাল বন্ধ করে দেখুন', 'info');

        // Close settings modal temporarily for preview
        setTimeout(() => {
            const settingsModal = document.getElementById('settingsModal');
            if (settingsModal) {
                settingsModal.style.display = 'none';

                // Show modal again after 3 seconds
                setTimeout(() => {
                    settingsModal.style.display = 'flex';
                }, 3000);
            }
        }, 500);
    } else {
        showNotification('প্রিভিউ করার জন্য একটি ব্যাকগ্রাউন্ড সিলেক্ট করুন', 'warning');
    }
}

// Reset background to default
function resetBackgroundToDefault() {
    const body = document.body;

    // Remove custom background
    body.style.background = '';
    body.style.backgroundImage = '';
    body.style.backgroundSize = '';
    body.style.backgroundPosition = '';

    // Clear stored settings
    delete body.dataset.backgroundType;
    delete body.dataset.backgroundValue;
    delete body.dataset.originalBackground;

    // Remove from localStorage
    localStorage.removeItem('backgroundSettings');

    // Reset UI selections
    document.querySelectorAll('.bg-type-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === 'solid') {
            btn.classList.add('active');
        }
    });

    document.querySelectorAll('.preset-color, .gradient-preset, .pattern-preset').forEach(item => {
        item.classList.remove('active');
    });

    // Show solid color options
    showBackgroundOptions('solid');

    showNotification('ব্যাকগ্রাউন্ড ডিফল্ট অবস্থায় ফিরিয়ে আনা হয়েছে', 'success');
}

// Load saved background settings
function loadSavedBackgroundSettings() {
    try {
        const savedSettings = localStorage.getItem('backgroundSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);

            // Apply saved background
            updateBackgroundPreview(settings.type, settings.value);

            // Update UI to reflect saved settings
            const typeBtn = document.querySelector(`[data-type="${settings.type}"]`);
            if (typeBtn) {
                document.querySelectorAll('.bg-type-btn').forEach(btn => btn.classList.remove('active'));
                typeBtn.classList.add('active');
                showBackgroundOptions(settings.type);
            }

            // Select appropriate preset
            if (settings.type === 'solid') {
                const colorPreset = document.querySelector(`[data-color="${settings.value}"]`);
                if (colorPreset) {
                    document.querySelectorAll('.preset-color').forEach(c => c.classList.remove('active'));
                    colorPreset.classList.add('active');
                }

                const colorPicker = document.getElementById('backgroundColorPicker');
                if (colorPicker) colorPicker.value = settings.value;
            } else if (settings.type === 'gradient') {
                const gradientPreset = document.querySelector(`[data-gradient="${settings.value}"]`);
                if (gradientPreset) {
                    document.querySelectorAll('.gradient-preset').forEach(g => g.classList.remove('active'));
                    gradientPreset.classList.add('active');
                }
            } else if (settings.type === 'pattern') {
                const patternPreset = document.querySelector(`[data-pattern="${settings.value}"]`);
                if (patternPreset) {
                    document.querySelectorAll('.pattern-preset').forEach(p => p.classList.remove('active'));
                    patternPreset.classList.add('active');
                }
            }
        }
    } catch (error) {
        console.error('Error loading background settings:', error);
    }
}



// ===== NOTIFICATION SOUNDS SYSTEM =====

// Sound definitions with Web Audio API frequencies
const notificationSounds = {
    default: { frequency: 800, duration: 200, type: 'sine' },
    bell: { frequency: 523, duration: 300, type: 'sine' }, // C5 note
    chime: { frequency: [523, 659, 784], duration: 400, type: 'sine' }, // C-E-G chord
    ding: { frequency: 1047, duration: 150, type: 'sine' }, // C6 note
    notification: { frequency: [440, 554], duration: 250, type: 'square' }, // A-C# notes
    alert: { frequency: 880, duration: 300, type: 'sawtooth' }, // A5 note
    soft: { frequency: 330, duration: 400, type: 'sine' }, // E4 note
    beep: { frequency: 1000, duration: 100, type: 'square' },
    tone: { frequency: [262, 330, 392], duration: 500, type: 'triangle' } // C-E-G chord
};

// Initialize notification sounds
function initializeNotificationSounds() {
    const soundSelect = document.getElementById('notificationSoundSelect');
    const previewBtn = document.getElementById('previewSoundBtn');

    if (soundSelect) {
        soundSelect.addEventListener('change', (e) => {
            const selectedSound = e.target.value;
            saveNotificationSound(selectedSound);
            showNotification(`নোটিফিকেশন সাউন্ড "${getSoundDisplayName(selectedSound)}" সিলেক্ট করা হয়েছে`, 'success');
        });
    }

    if (previewBtn) {
        previewBtn.addEventListener('click', () => {
            const selectedSound = soundSelect ? soundSelect.value : 'default';
            playNotificationSound(selectedSound);
        });
    }

    // Load saved sound preference
    loadSavedNotificationSound();
}

// Play notification sound using Web Audio API
function playNotificationSound(soundType = 'default') {
    try {
        // Check if Web Audio API is supported
        if (!window.AudioContext && !window.webkitAudioContext) {
            console.warn('Web Audio API not supported');
            return;
        }

        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioContext = new AudioContext();

        const sound = notificationSounds[soundType] || notificationSounds.default;
        const frequencies = Array.isArray(sound.frequency) ? sound.frequency : [sound.frequency];

        // Create and play each frequency
        frequencies.forEach((freq, index) => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
            oscillator.type = sound.type;

            // Set volume envelope
            const startTime = audioContext.currentTime + (index * 0.1);
            const endTime = startTime + (sound.duration / 1000);

            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, endTime);

            oscillator.start(startTime);
            oscillator.stop(endTime);
        });

    } catch (error) {
        console.error('Error playing notification sound:', error);
        // Fallback to system beep if available
        try {
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }
        } catch (e) {
            console.warn('Vibration not supported');
        }
    }
}

// Get display name for sound
function getSoundDisplayName(soundType) {
    const displayNames = {
        default: 'ডিফল্ট সাউন্ড',
        bell: 'বেল সাউন্ড',
        chime: 'চাইম সাউন্ড',
        ding: 'ডিং সাউন্ড',
        notification: 'নোটিফিকেশন সাউন্ড',
        alert: 'অ্যালার্ট সাউন্ড',
        soft: 'সফট সাউন্ড',
        beep: 'বিপ সাউন্ড',
        tone: 'টোন সাউন্ড'
    };
    return displayNames[soundType] || displayNames.default;
}

// Save notification sound preference
function saveNotificationSound(soundType) {
    localStorage.setItem('notificationSound', soundType);
}

// Load saved notification sound
function loadSavedNotificationSound() {
    const savedSound = localStorage.getItem('notificationSound') || 'default';
    const soundSelect = document.getElementById('notificationSoundSelect');

    if (soundSelect) {
        soundSelect.value = savedSound;
    }
}

// Get current notification sound
function getCurrentNotificationSound() {
    return localStorage.getItem('notificationSound') || 'default';
}

// ===== PERFORMANCE OPTIMIZATION =====

// Optimize performance to reduce forced reflows
function optimizePerformance() {
    // Debounce resize events
    let resizeTimeout;
    const originalResize = window.onresize;

    window.onresize = function(e) {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            if (originalResize) {
                originalResize.call(this, e);
            }
        }, 100);
    };

    // Optimize scroll events
    let scrollTimeout;
    const optimizeScrollEvents = () => {
        const scrollElements = document.querySelectorAll('.scrollable, .note-list, .search-results');

        scrollElements.forEach(element => {
            element.addEventListener('scroll', () => {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    // Batch scroll-related operations
                    requestAnimationFrame(() => {
                        // Any scroll-dependent updates here
                    });
                }, 16); // ~60fps
            }, { passive: true });
        });
    };

    // Apply optimizations
    requestAnimationFrame(() => {
        optimizeScrollEvents();

        // Reduce layout thrashing by batching DOM reads/writes
        const observer = new MutationObserver((mutations) => {
            const batchedOperations = [];

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    batchedOperations.push(() => {
                        // Handle DOM changes efficiently
                    });
                }
            });

            if (batchedOperations.length > 0) {
                requestAnimationFrame(() => {
                    batchedOperations.forEach(op => op());
                });
            }
        });

        // Observe main content areas
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            observer.observe(mainContent, {
                childList: true,
                subtree: true,
                attributes: false
            });
        }
    });

    console.log('Performance optimizations applied ⚡');
}



// Set reminder display mode
function setReminderDisplayMode(mode) {
    if (mode === 'modal') {
        localStorage.setItem('reminderDisplayMode', 'modal');
        showNotification('রিমাইন্ডার মোডাল পপআপ মোডে সেট করা হয়েছে', 'success');
    } else {
        localStorage.setItem('reminderDisplayMode', 'panel');
        showNotification('রিমাইন্ডার নোটিফিকেশন প্যানেল মোডে সেট করা হয়েছে', 'success');
    }
}

// ===== STORAGE PATH MANAGEMENT =====
function loadStoragePath() {
    const savedPath = localStorage.getItem('storagePath');
    if (savedPath) {
        currentStoragePath = savedPath;
    }
    updateStoragePathDisplay();
}

function saveStoragePath(path) {
    currentStoragePath = path;
    localStorage.setItem('storagePath', path);
    updateStoragePathDisplay();
    showNotification('সংরক্ষণ পাথ আপডেট করা হয়েছে', 'success');
}

function updateStoragePathDisplay() {
    const currentPathElement = document.getElementById('currentStoragePath');
    const storagePathInput = document.getElementById('storagePathInput');

    if (currentPathElement) {
        currentPathElement.textContent = currentStoragePath;
    }

    if (storagePathInput) {
        storagePathInput.placeholder = `বর্তমান: ${currentStoragePath}`;
    }
}

function copyCurrentStoragePath() {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(currentStoragePath).then(() => {
            showNotification('সংরক্ষণ পাথ কপি করা হয়েছে', 'success');

            // Visual feedback
            const copyBtn = document.getElementById('copyCurrentPathBtn');
            if (copyBtn) {
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.style.background = '#28a745';

                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.style.background = '';
                }, 1500);
            }
        }).catch(err => {
            console.error('Failed to copy storage path:', err);
            showNotification('পাথ কপি করতে সমস্যা হয়েছে', 'error');
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = currentStoragePath;
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            showNotification('সংরক্ষণ পাথ কপি করা হয়েছে', 'success');
        } catch (err) {
            console.error('Failed to copy storage path:', err);
            showNotification('পাথ কপি করতে সমস্যা হয়েছে', 'error');
        }

        document.body.removeChild(textArea);
    }
}

function updateStoragePath() {
    const storagePathInput = document.getElementById('storagePathInput');
    const newPath = storagePathInput.value.trim();

    if (!newPath) {
        showNotification('অনুগ্রহ করে একটি বৈধ পাথ লিখুন', 'warning');
        return;
    }

    // Basic path validation
    if (newPath.length < 3) {
        showNotification('পাথটি খুবই ছোট। অনুগ্রহ করে একটি সম্পূর্ণ পাথ লিখুন', 'warning');
        return;
    }

    saveStoragePath(newPath);
    storagePathInput.value = '';
}

// Load saved reminder display mode
function loadSavedReminderDisplayMode() {
    const savedMode = localStorage.getItem('reminderDisplayMode') || 'panel';

    // Update UI to reflect saved mode
    const modeBtn = document.querySelector(`[data-mode="${savedMode}"]`);
    if (modeBtn) {
        document.querySelectorAll('.reminder-mode-btn').forEach(btn => btn.classList.remove('active'));
        modeBtn.classList.add('active');
    }
}