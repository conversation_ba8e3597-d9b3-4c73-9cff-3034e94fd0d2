/* ===== FONT AWESOME FALLBACK ===== */
/* Fallback for Font Awesome icons if CDN fails */
.fas, .far, .fab, .fal, .fad, .fa {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", sans-serif !important;
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Unicode fallbacks for common icons - only when Font Awesome fails */
.fa-fallback .fa-sticky-note::before { content: "📝"; }
.fa-fallback .fa-plus::before { content: "+"; }
.fa-fallback .fa-search::before { content: "🔍"; }
.fa-fallback .fa-calendar::before { content: "📅"; }
.fa-fallback .fa-cog::before { content: "⚙️"; }
.fa-fallback .fa-question-circle::before { content: "❓"; }
.fa-fallback .fa-bell::before { content: "🔔"; }
.fa-fallback .fa-times::before { content: "✕"; }
.fa-fallback .fa-save::before { content: "💾"; }
.fa-fallback .fa-edit::before { content: "✏️"; }
.fa-fallback .fa-trash::before { content: "🗑️"; }
.fa-fallback .fa-copy::before { content: "📋"; }
.fa-fallback .fa-share-alt::before { content: "📤"; }
.fa-fallback .fa-print::before { content: "🖨️"; }
.fa-fallback .fa-download::before { content: "⬇️"; }
.fa-fallback .fa-upload::before { content: "⬆️"; }
.fa-fallback .fa-file-pdf::before { content: "📄"; }
.fa-fallback .fa-lock::before { content: "🔒"; }
.fa-fallback .fa-unlock::before { content: "🔓"; }
.fa-fallback .fa-eye::before { content: "👁️"; }
.fa-fallback .fa-eye-slash::before { content: "🙈"; }
.fa-fallback .fa-paperclip::before { content: "📎"; }
.fa-fallback .fa-smile::before { content: "😊"; }
.fa-fallback .fa-expand::before { content: "⛶"; }
.fa-fallback .fa-compress::before { content: "⛶"; }
.fa-fallback .fa-compress-arrows-alt::before { content: "⇱"; }
.fa-fallback .fa-plus-circle::before { content: "⊕"; }
.fa-fallback .fa-volume-up::before { content: "🔊"; }
.fa-fallback .fa-folder-open::before { content: "📂"; }

/* Additional fallback icons */
.fa-fallback .fa-home::before { content: "🏠"; }
.fa-fallback .fa-user::before { content: "👤"; }
.fa-fallback .fa-star::before { content: "⭐"; }
.fa-fallback .fa-heart::before { content: "❤️"; }
.fa-fallback .fa-check::before { content: "✓"; }
.fa-fallback .fa-cut::before { content: "✂️"; }
.fa-fallback .fa-paste::before { content: "📋"; }
.fa-fallback .fa-undo::before { content: "↶"; }
.fa-fallback .fa-redo::before { content: "↷"; }
.fa-fallback .fa-bold::before { content: "B"; font-weight: bold; }
.fa-fallback .fa-italic::before { content: "I"; font-style: italic; }
.fa-fallback .fa-underline::before { content: "U"; text-decoration: underline; }
.fa-fallback .fa-check-square::before { content: "☑"; }
.fa-fallback .fa-exchange-alt::before { content: "⇄"; }
.fa-fallback .fa-arrow-left::before { content: "←"; }
.fa-fallback .fa-arrow-right::before { content: "→"; }
.fa-fallback .fa-arrow-up::before { content: "↑"; }
.fa-fallback .fa-arrow-down::before { content: "↓"; }
.fa-fallback .fa-info-circle::before { content: "ℹ️"; }
.fa-fallback .fa-exclamation-triangle::before { content: "⚠️"; }
.fa-fallback .fa-check-circle::before { content: "✅"; }
.fa-fallback .fa-times-circle::before { content: "❌"; }

/* ===== FULLSCREEN LOADER ===== */
.fullscreen-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.fullscreen-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-container {
    text-align: center;
    position: relative;
    z-index: 2;
}

.loader-animation {
    position: relative;
    margin-bottom: 2rem;
}

.loader-circle {
    width: 120px;
    height: 120px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    position: relative;
    margin: 0 auto 2rem;
    animation: loaderRotate 2s linear infinite;
}

.loader-circle::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    width: 120px;
    height: 120px;
    border: 4px solid transparent;
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: loaderSpin 1s linear infinite;
}

.loader-inner-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
    animation: loaderPulse 2s ease-in-out infinite;
}

.loader-inner-circle::before {
    content: '📝';
    animation: loaderBounce 1.5s ease-in-out infinite;
}

.loader-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    background-color: #ffffff;
    border-radius: 50%;
    animation: loaderDots 1.4s ease-in-out infinite both;
}

.dot-1 {
    animation-delay: -0.32s;
}

.dot-2 {
    animation-delay: -0.16s;
}

.dot-3 {
    animation-delay: 0s;
}

.loader-text {
    color: #ffffff;
    font-family: 'Kalpurush', sans-serif;
}

.loader-text h2 {
    font-size: 2rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: loaderTextGlow 2s ease-in-out infinite;
}

.loader-text p {
    font-size: 1.2rem;
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    animation: loaderTextFade 2s ease-in-out infinite;
}

.loader-progress {
    width: 200px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 0 auto;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, #f0f0f0, #ffffff);
    border-radius: 2px;
    width: 0%;
    animation: loaderProgress 3s ease-in-out;
}

.loader-background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-note {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    animation: floatingNotes 8s linear infinite;
}

.floating-note-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-note-2 {
    top: 20%;
    right: 15%;
    animation-delay: -1s;
}

.floating-note-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: -2s;
}

.floating-note-4 {
    top: 60%;
    right: 25%;
    animation-delay: -3s;
}

.floating-note-5 {
    bottom: 20%;
    right: 10%;
    animation-delay: -4s;
}

.floating-note-6 {
    top: 40%;
    left: 5%;
    animation-delay: -5s;
}

/* ===== LOADER ANIMATIONS ===== */
@keyframes loaderRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loaderSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loaderPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.8;
    }
}

@keyframes loaderBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes loaderDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes loaderTextGlow {
    0%, 100% {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    50% {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
    }
}

@keyframes loaderTextFade {
    0%, 100% {
        opacity: 0.9;
    }
    50% {
        opacity: 0.6;
    }
}

@keyframes loaderProgress {
    0% {
        width: 0%;
    }
    50% {
        width: 70%;
    }
    100% {
        width: 100%;
    }
}

@keyframes floatingNotes {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* ===== VARIABLES ===== */
:root {
    --primary-color: #4a6bdf;
    --primary-hover: #3a5bcf;
    --secondary-color: #6c757d;
    --secondary-hover: #5a6268;
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --success-color: #28a745;
    --success-hover: #218838;
    --warning-color: #ffc107;
    --warning-hover: #e0a800;
    --info-color: #17a2b8;
    --info-hover: #138496;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #212529;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --font-family-bengali: 'Kalpurush', sans-serif;
    --font-family-base: 'Courier New', monospace;
    --font-family-english: 'Courier New', monospace;
    --font-size-base: 18px;
    --font-weight-base: 400;
    --background-color: #f5f7fa;
    --card-background: #ffffff;
    --modal-background: rgba(0, 0, 0, 0.5);
}

/* ===== LIGHT THEME ===== */
[data-theme="light"] .sort-select {
    color: #212529 !important;
}

[data-theme="light"] .sort-label {
    color: #212529 !important;
}

[data-theme="light"] .sort-select option {
    background-color: white !important;
    color: #212529 !important;
}

[data-theme="light"] .sort-container {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

[data-theme="light"] .sort-container:hover {
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

[data-theme="light"] .sort-select {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: #2c3e50 !important;
}

[data-theme="light"] .sort-select:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(255, 255, 255, 0.8);
}

[data-theme="light"] .sort-select option {
    background: white;
    color: #2c3e50;
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
    --primary-color: #5a7bef;
    --primary-hover: #4a6bdf;
    --secondary-color: #6c757d;
    --secondary-hover: #5a6268;
    --light-color: #2d3748;
    --dark-color: #1a202c;
    --text-color: #e2e8f0;
    --text-muted: #a0aec0;
    --border-color: #4a5568;
    --background-color: #1a202c;
    --card-background: #2d3748;
    --modal-background: rgba(0, 0, 0, 0.8);
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);

    /* Additional variables for bookmark system */
    --text-primary: #e2e8f0;
    --text-secondary: #a0aec0;
    --card-bg: #2d3748;
    --bg-secondary: #1a202c;
    --input-bg: #2d3748;
    --hover-bg: #4a5568;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --accent-color: #6f42c1;
}

[data-theme="dark"] .sort-select {
    color: white !important;
}

[data-theme="dark"] .sort-label {
    color: white !important;
}

[data-theme="dark"] .sort-select option {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

/* ===== COLOR THEMES ===== */
[data-color="green"] {
    --primary-color: #28a745;
    --primary-hover: #218838;
}

[data-color="purple"] {
    --primary-color: #6f42c1;
    --primary-hover: #5a32a3;
}

[data-color="orange"] {
    --primary-color: #fd7e14;
    --primary-hover: #e8690b;
}

[data-color="red"] {
    --primary-color: #dc3545;
    --primary-hover: #c82333;
}

[data-color="teal"] {
    --primary-color: #1A1A1A;
    --primary-hover: #1A1A1A;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-english);
    font-weight: var(--font-weight-base);
    font-size: var(--font-size-base);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background-color);
    min-height: 100vh;
    transition: var(--transition);
    overflow-x: hidden;
}

/* Default English font for all elements */
input, textarea, select, option {
    font-family: var(--font-family-english);
}

/* Bengali font for specific elements */
.bengali-text,
[lang="bn"],
.note-title,
.note-content,
.notification-title,
.notification-message,
.modal-title,
.empty-state h2,
.empty-state p,
.path-label,
.logo,
h1, h2, h3, h4, h5, h6,
input[placeholder*="নোট"],
input[placeholder*="শিরোনাম"],
input[placeholder*="ট্যাব"],
textarea[placeholder*="নোট"],
select option[class*="bengali"],
.sort-select,
.sort-label,
.priority-select,
.priority-select-compact {
    font-family: var(--font-family-bengali) !important;
}

/* English font for code, paths, and technical elements */
.path-text,
code, pre,
.file-path,
.technical-text,
input[type="url"],
input[type="email"],
.monospace,
.footer-title,
.footer-description,
.footer-copyright,
.developer-name,
.english-text {
    font-family: var(--font-family-english) !important;
}

/* Force Courier New for all English content */
body *:not(.bengali-text):not([class*="bengali"]):not(input[placeholder*="নোট"]):not(input[placeholder*="শিরোনাম"]):not(input[placeholder*="ট্যাব"]):not(textarea[placeholder*="নোট"]) {
    font-family: var(--font-family-english);
}

/* Ensure all form elements use Courier New by default */
input:not(.bengali-text):not([class*="bengali"]),
textarea:not(.bengali-text):not([class*="bengali"]),
select:not(.bengali-text):not([class*="bengali"]) {
    font-family: var(--font-family-english) !important;
}

/* Override for Bengali placeholders */
input[placeholder*="নোট"],
input[placeholder*="শিরোনাম"],
input[placeholder*="ট্যাব"],
input[placeholder*="খুঁজুন"],
textarea[placeholder*="নোট"] {
    font-family: var(--font-family-bengali) !important;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

button {
    cursor: pointer;
    font-family: var(--font-family-english);
    font-weight: 400;
    font-size: 16px;
}

/* Bengali text in buttons */
button[title*="নোট"],
button[title*="সার্চ"],
button[title*="ক্যালেন্ডার"],
button[title*="সেটিংস"],
button[title*="সাহায্য"],
button[title*="ড্র্যাগ"],
button[title*="রিসেট"],
button[title*="পাথ"],
button[title*="ব্যাকআপ"],
button[title*="রিস্টোর"],
button[title*="স্যাম্পল"],
button[title*="ড্যাশবোর্ড"],
button:contains("বাতিল"),
button:contains("সংরক্ষণ"),
button:contains("ডিলেট"),
button:contains("প্রিন্ট"),
button:contains("শেয়ার"),
button:contains("এক্সপোর্ট") {
    font-family: var(--font-family-bengali);
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== LAYOUT ===== */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--box-shadow);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
}

.logo {
    font-family: var(--font-family-bengali);
    font-size: 1.5rem;
    font-weight: 500;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.logo i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

/* ===== PATH DISPLAY SECTION ===== */
.path-display-section {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: none; /* Initially hidden */
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.path-display-section.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.path-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    background: var(--card-background);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.path-container:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(74, 107, 223, 0.1);
}

.path-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
}

.path-info i {
    color: var(--primary-color);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.path-label {
    font-family: var(--font-family-bengali);
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
    flex-shrink: 0;
}

.path-text {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--text-muted);
    background: var(--light-color);
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    word-break: break-all;
    flex: 1;
    min-width: 0;
    transition: var(--transition);
}

.copy-path-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
}

.copy-path-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.copy-path-btn:active {
    transform: translateY(0);
}

.copy-path-btn i {
    font-size: 0.9rem;
}

/* Dark mode styles for path display */
[data-theme="dark"] .path-display-section {
    background: var(--background-color) !important;
    border-bottom: 1px solid var(--border-color) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .path-container {
    background: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .path-container:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 2px 8px rgba(90, 123, 239, 0.3) !important;
}

[data-theme="dark"] .path-info i {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .path-label {
    color: var(--text-color) !important;
    font-weight: 500 !important;
}

[data-theme="dark"] .path-text {
    background: var(--light-color) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-muted) !important;
}

[data-theme="dark"] .copy-path-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
}

[data-theme="dark"] .copy-path-btn:hover {
    background: var(--primary-hover) !important;
    color: white !important;
}

[data-theme="dark"] .copy-path-btn i {
    color: white !important;
}

/* ===== BACKUP CONFIRMATION MODAL ===== */
.backup-confirm-modal .modal-content {
    max-width: 500px;
    width: 90%;
}

.backup-confirm-body {
    padding: 1.5rem;
    text-align: center;
}

.backup-confirm-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.backup-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.backup-icon i {
    font-size: 2rem;
    color: white;
}

.backup-confirm-section h4 {
    font-family: var(--font-family-bengali);
    color: var(--text-color);
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
}

.backup-confirm-section p {
    font-family: var(--font-family-bengali);
    color: var(--text-muted);
    margin: 0.5rem 0;
    line-height: 1.5;
}

.backup-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
    width: 100%;
}

.backup-detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.backup-detail-item i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.backup-detail-item span {
    font-family: var(--font-family-bengali);
    font-size: 0.9rem;
    color: var(--text-color);
}

/* Dark mode styles for backup confirmation */
[data-theme="dark"] .backup-detail-item {
    background: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .backup-detail-item span {
    color: var(--text-color);
}

/* Mobile responsive for backup confirmation */
@media (max-width: 480px) {
    .backup-details {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .backup-detail-item {
        padding: 0.5rem;
    }

    .backup-detail-item span {
        font-size: 0.8rem;
    }
}

.header-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
    padding: 0.25rem 0;
    min-height: 40px;
}

/* ===== ENCRYPTION STYLES ===== */
.encryption-icon {
    color: var(--warning-color);
    margin-right: 0.25rem;
    font-size: 0.875rem;
}

.note-card.encrypted {
    border-left: 4px solid var(--warning-color);
}

.note-card.encrypted .note-content {
    opacity: 0.8;
}

.note-card.encrypted .note-title {
    color: var(--warning-color);
    font-weight: 600;
}

/* ===== COMPACT BUTTON STYLES ===== */
.btn-compact {
    padding: 0.4rem;
    min-width: auto;
    width: 2.2rem;
    height: 2.2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-compact:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.btn-compact:active {
    transform: scale(0.95);
}

/* ===== SORT CONTAINER - NEW DESIGN ===== */
.sort-container {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    height: 38px;
    min-height: 38px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sort-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.sort-container:hover::before {
    left: 100%;
}

.sort-container:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}



.sort-select {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 0.4rem 0.6rem;
    font-family: var(--font-family-bengali);
    font-size: 0.85rem;
    color: white !important;
    outline: none;
    transition: all 0.3s ease;
    min-width: 130px;
    cursor: pointer;
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 500;
    backdrop-filter: blur(5px);
    position: relative;
}

.sort-select:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.02);
}

.sort-select:focus {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.7);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.sort-select option {
    background: #2c3e50;
    color: white;
    font-family: var(--font-family-bengali);
    font-size: 0.85rem;
    padding: 0.6rem;
    border: none;
}

.sort-select option {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
    padding: 0.5rem;
}

.sort-label {
    color: white !important;
    font-family: var(--font-family-bengali);
    font-size: 0.85rem;
    margin-right: 0.6rem;
    white-space: nowrap;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.sort-label::before {
    content: '📊';
    font-size: 0.9rem;
}

.sort-select:focus {
    background-color: rgba(255, 255, 255, 0.1);
}

.sort-select option {
    font-family: var(--font-family-bengali);
    padding: 0.5rem;
}

/* ===== NOTIFICATION BELL ===== */
.notification-bell-container {
    position: relative;
}

.notification-bell {
    position: relative;
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 1.1rem;
    transition: var(--transition);
}

.notification-bell:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.notification-bell.has-notifications {
    background-color: var(--primary-color);
    color: white;
    animation: bellShake 0.5s ease-in-out;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0);
    transition: var(--transition);
}

.notification-badge.show {
    opacity: 1;
    transform: scale(1);
    animation: badgePop 0.3s ease;
}

@keyframes bellShake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

@keyframes badgePop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* ===== NOTIFICATION PANEL ===== */
.notification-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: flex-start;
}

.notification-panel.active {
    display: flex;
    animation: modalFadeIn 0.3s ease;
}

.notification-panel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
}

.notification-panel-content {
    background-color: var(--card-background);
    width: 400px;
    max-width: 90vw;
    height: 100vh;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 2001;
    animation: slideInFromLeft 0.3s ease;
    display: flex;
    flex-direction: column;
    color: var(--text-color);
}

.notification-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-color);
}

.notification-panel-header h3 {
    font-family: var(--font-family-bengali);
    font-size: 1.3rem;
    font-weight: 400;
    color: var(--text-color);
    margin: 0;
}

.notification-panel-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-panel-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.no-notifications {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.no-notifications i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-notifications p {
    font-family: var(--font-family-bengali);
    font-size: 1.1rem;
    margin: 0;
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* ===== NOTIFICATION ITEMS ===== */
.notification-item {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    border-left: 4px solid var(--primary-color);
    color: var(--text-color);
}

.notification-item:hover {
    background-color: var(--light-color);
    transform: translateX(5px);
}

.notification-item.unread {
    background-color: rgba(74, 107, 223, 0.05);
    border-left-color: var(--primary-color);
}

.notification-item.read {
    opacity: 0.8;
    border-left-color: var(--text-muted);
}

.notification-item.success {
    border-left-color: var(--success-color);
}

.notification-item.error {
    border-left-color: var(--danger-color);
}

.notification-item.warning {
    border-left-color: var(--warning-color);
}

.notification-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.notification-item-title {
    font-family: var(--font-family-bengali);
    font-weight: 400;
    font-size: 1rem;
    color: var(--text-color);
    margin: 0;
}

.notification-item-time {
    font-family: var(--font-family-bengali);
    font-size: 0.7rem;
    color: var(--text-muted);
    white-space: nowrap;
    background-color: var(--light-color);
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-weight: 400;
    border: 1px solid var(--border-color);
}

.notification-item-message {
    font-family: var(--font-family-bengali);
    font-size: 1rem;
    color: var(--text-color);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    opacity: 0.9;
    font-weight: 400;
}

.notification-item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
}

.notification-item-type {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    background-color: var(--light-color);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.notification-item-delete {
    background-color: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
    font-size: 0.8rem;
}

.notification-item-delete:hover {
    background-color: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

.notification-item-full-time {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.notification-item-full-time small {
    font-family: var(--font-family-bengali);
    font-size: 0.75rem;
    color: var(--text-muted);
    font-style: italic;
}

.main {
    min-height: calc(100vh - 70px);
    margin-top: -60px;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-family: var(--font-family-bengali);
    font-weight: 400;
    transition: var(--transition);
    border: none;
    outline: none;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

/* Add Note Button specific styling */
#addNoteBtn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#addNoteBtn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

#addNoteBtn:active {
    transform: scale(0.95);
}

#addNoteBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

#addNoteBtn:hover::before {
    left: 100%;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
}

/* Download TXT button specific styles */
#downloadTxtBtn {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#downloadTxtBtn:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

#downloadTxtBtn:active {
    transform: translateY(0);
}

#downloadTxtBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#downloadTxtBtn:hover::before {
    left: 100%;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: var(--danger-hover);
}

.btn-search {
    background-color: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-search:hover {
    background-color: #e9ecef;
}

.btn-icon {
    padding: 0.3rem;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: var(--text-color);
    border: none;
    transition: var(--transition);
}

.btn-icon:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* ===== NOTES GRID ===== */
.notes-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-top: 3rem; /* Extra space for external titles */
    padding-top: 1rem;
    width: 100%;
    box-sizing: border-box;
}

/* ===== NOTE CARD ===== */
.note-card {
    background-color: var(--card-background);
    box-shadow: var(--box-shadow);
    padding: 0;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: visible;
    border: 1px solid var(--border-color);
    margin-top: 1.2rem; /* Space for external title */
    min-width: 0; /* Ensure equal column widths */
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 220px;
}

/* ===== EXTERNAL TITLE ===== */
.note-external-title {
    position: absolute;
    top: 5px;
    left: 0;
    right: 0;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px 8px 0 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    border-bottom: none;
    position: relative;
    overflow: hidden;
}

.note-external-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.note-card:hover .note-external-title::before {
    left: 100%;
}

.external-title-text {
    color: white;
    font-family: var(--font-family-bengali);
    font-size: 1.2rem;
    font-weight: 400;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    z-index: 2;
}

.note-card:hover .external-title-text {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4), 0 0 10px rgba(255, 255, 255, 0.3);
}

.external-title-decoration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.priority-badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.priority-badge.priority-urgent {
    background-color: rgba(220, 53, 69, 0.8);
    border-color: rgba(220, 53, 69, 0.5);
}

.priority-badge.priority-high {
    background-color: rgba(253, 126, 20, 0.8);
    border-color: rgba(253, 126, 20, 0.5);
}

.priority-badge.priority-medium {
    background-color: rgba(255, 193, 7, 0.8);
    border-color: rgba(255, 193, 7, 0.5);
}

.priority-badge.priority-low {
    background-color: rgba(40, 167, 69, 0.8);
    border-color: rgba(40, 167, 69, 0.5);
}

.external-lock-icon {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: lockPulse 2s infinite;
}

/* Animations for external title */
@keyframes lockPulse {
    0%, 100% {
        opacity: 0.9;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.note-external-title {
    animation: slideDownFade 0.5s ease-out;
}

@keyframes slideDownFade {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.note-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* External title hover effects */
.note-card:hover .note-external-title {
    background: linear-gradient(135deg, var(--primary-hover), var(--secondary-color));
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.note-card:hover .external-title-text {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.note-card:hover .priority-badge {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.note-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    gap: 1rem;
    padding: 1rem 1rem 0 1rem;
}

.note-date-section {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    flex: 1;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.08) 0%, rgba(74, 144, 226, 0.03) 100%);
    border: 1px solid rgba(74, 144, 226, 0.15);
    border-radius: 8px;
    padding: 0.6rem;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.note-date-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--warning-color));
    opacity: 0.7;
}

.note-date {
    font-family: var(--font-family-english);
    font-size: 0.7rem;
    color: var(--text-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: help;
    transition: all 0.3s ease;
    padding: 0.4rem 1.6rem;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.note-date:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
}

.note-date i {
    font-size: 0.8rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.note-date:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.note-date .date-label {
    font-family: var(--font-family-bengali);
    font-weight: 600;
    min-width: 32px;
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.note-date .date-value {
    font-family: var(--font-family-english);
    font-size: 0.8rem;
    opacity: 0.9;
    font-weight: 700;
    flex: 1;
}

.created-date {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-left: 3px solid var(--success-color);
    position: relative;
}

.created-date::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, var(--success-color), rgba(40, 167, 69, 0.6));
    border-radius: 0 2px 2px 0;
}

.created-date i {
    color: var(--success-color);
}

.updated-date {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-left: 3px solid var(--warning-color);
    position: relative;
}

.updated-date::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, var(--warning-color), rgba(255, 193, 7, 0.6));
    border-radius: 0 2px 2px 0;
}

.updated-date i {
    color: var(--warning-color);
}

/* Dark theme adjustments for note-date-section */
[data-theme="dark"] .note-date-section {
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.12) 0%, rgba(74, 144, 226, 0.06) 100%);
    border-color: rgba(74, 144, 226, 0.25);
}

[data-theme="dark"] .note-date {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

[data-theme="dark"] .note-date:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .created-date {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(40, 167, 69, 0.08) 100%);
}

[data-theme="dark"] .updated-date {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(255, 193, 7, 0.08) 100%);
}

/* Note priority section removed from note cards - priority is shown in external title */
/* .note-priority-section {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-top: 0.25rem;
} */

/* Responsive design for note dates */
@media (max-width: 768px) {
    .note-date-section {
        gap: 0.25rem;
        padding: 0.5rem;
    }

    .note-date {
        font-size: 0.65rem;
        gap: 0.4rem;
        padding: 0.35rem 0.5rem;
    }

    .note-date .date-value {
        font-size: 0.6rem;
    }

    .note-date .date-label {
        min-width: 28px;
        font-size: 0.6rem;
    }

    .note-date i {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .note-date-section {
        padding: 0.4rem;
        gap: 0.2rem;
    }

    .note-date {
        padding: 0.3rem 0.4rem;
        gap: 0.3rem;
    }

    .note-date .date-label {
        min-width: 25px;
        font-size: 0.55rem;
    }

    .note-date .date-value {
        font-size: 0.55rem;
    }
}

/* ===== INSERTED DATE/TIME STYLES ===== */
.date, .time, .datetime {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    font-family: var(--font-family-bengali);
    display: inline-block;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.date {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.time {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.datetime {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.date:hover, .time:hover, .datetime:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== REMINDER INPUT STYLES ===== */
.reminder-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-background);
    color: var(--text-color);
    font-family: var(--font-family-bengali);
    font-size: 0.9rem;
}

.reminder-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* ===== SHARE EXPIRY INPUT STYLES ===== */
.expiry-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-background);
    color: var(--text-color);
    font-family: var(--font-family-bengali);
    font-size: 0.9rem;
}

.expiry-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}



.note-content {
    font-family: var(--font-family-bengali);
    font-weight: 400;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-color);
    max-height: 120px;
    overflow: hidden;
    position: relative;
    padding: 0 1rem 1rem 1rem;
    flex: 1;
}

.note-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-family: var(--font-family-bengali);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.note-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, white);
}

.note-preview {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-last-saved {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    font-family: var(--font-family-bengali);
    font-size: 0.8rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.note-last-saved i {
    color: var(--primary-color);
    font-size: 0.75rem;
}

/* ===== AUTO SAVE STYLES ===== */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--success-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-family: var(--font-family-bengali);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    animation: autoSaveSlideDown 0.4s ease-out;
}

.auto-save-indicator i {
    margin-right: 0.5rem;
}

@keyframes autoSaveSlideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes autoSaveFadeOut {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
}

.auto-save-indicator.fade-out {
    animation: autoSaveFadeOut 0.3s ease-in forwards;
}

/* Responsive auto-save indicator */
@media (max-width: 768px) {
    .auto-save-indicator {
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;
        font-size: 0.8rem;
        padding: 0.6rem 1rem;
    }

    @keyframes autoSaveSlideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes autoSaveFadeOut {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(-10px);
        }
    }
}

#autoSaveBtn.active {
    background-color: var(--success-color);
    color: white;
}

#autoSaveBtn.active:hover {
    background-color: #218838;
}

/* ===== BACKUP & RESTORE BUTTONS ===== */
.btn-info {
    background-color: var(--info-color);
    color: white;
    border: none;
}

.btn-info:hover {
    background-color: var(--info-hover);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border: none;
}

.btn-warning:hover {
    background-color: var(--warning-hover);
}

/* ===== NOTE ACTIONS FOOTER ===== */
.note-actions-footer {
    margin-top: auto;
    padding: 0.5rem;
    border-top: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(74, 144, 226, 0.05) 100%);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.3s ease;
}

.note-card:hover .note-actions-footer {
    opacity: 1;
    transform: translateY(0);
}

.note-actions-footer .note-actions {
    display: flex;
    gap: 0.25rem;
    justify-content: space-between;
    flex-wrap: wrap;
    opacity: 1;
    transform: none;
}

.btn-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.15rem;
    padding: 0.4rem 0.2rem;
    border: none;
    background: transparent;
    color: var(--text-muted);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.65rem;
    min-width: 40px;
    text-align: center;
    flex: 1;
}

.btn-action:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3);
}

.btn-action.btn-danger:hover {
    background: var(--danger-color);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-action i {
    font-size: 0.9rem;
    margin-bottom: 0.05rem;
}

.btn-action span {
    font-size: 0.6rem;
    font-weight: 500;
    white-space: nowrap;
    line-height: 1.1;
}

/* Responsive design for note actions footer */
@media (max-width: 768px) {
    .note-actions-footer {
        padding: 0.4rem;
    }

    .btn-action {
        min-width: 35px;
        padding: 0.35rem 0.15rem;
        gap: 0.1rem;
    }

    .btn-action span {
        font-size: 0.55rem;
    }

    .btn-action i {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .note-actions {
        gap: 0.2rem;
    }

    .btn-action {
        min-width: 32px;
        padding: 0.3rem 0.1rem;
    }

    .btn-action span {
        font-size: 0.5rem;
    }

    .btn-action i {
        font-size: 0.8rem;
    }
}

/* ===== NOTE ACTIONS ENHANCEMENT (Legacy) ===== */
.note-card-header .note-actions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    opacity: 0;
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

.note-card:hover .note-card-header .note-actions {
    opacity: 1;
}

.note-actions .btn-icon {
    padding: 0.375rem;
    font-size: 0.875rem;
    border-radius: 4px;
    transition: var(--transition);
    position: relative;
}

.note-actions .btn-icon:hover {
    transform: scale(1.1);
}

/* Help button styling */
.btn-help {
    background-color: var(--info-color);
    color: white;
}

.btn-help:hover {
    background-color: var(--info-hover);
    transform: scale(1.05);
}

/* Drag button styling */
.btn-drag {
    background-color: var(--warning-color);
    color: white;
    transition: all 0.3s ease;
}

.btn-drag:hover {
    background-color: var(--warning-hover);
    transform: scale(1.05);
}

.btn-drag.active {
    background-color: var(--success-color);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    animation: dragPulse 2s infinite;
}

@keyframes dragPulse {
    0%, 100% {
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.8);
    }
}

/* Reset button styling */
.btn-reset {
    background-color: var(--danger-color);
    color: white;
    transition: all 0.3s ease;
}

.btn-reset:hover {
    background-color: var(--danger-hover);
    transform: scale(1.05) rotate(-15deg);
}

.btn-reset:active {
    transform: scale(0.95) rotate(-15deg);
}

/* Drag mode styles */
.notes-grid.drag-mode {
    user-select: none;
}

.notes-grid.drag-mode .note-card {
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;
}

.notes-grid.drag-mode .note-card::before {
    content: '⋮⋮';
    position: absolute;
    top: 10px;
    right: 10px;
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0.7;
    z-index: 10;
    pointer-events: none;
}

.notes-grid.drag-mode .note-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.notes-grid.drag-mode .note-card.sortable-ghost {
    opacity: 0.5;
    transform: scale(0.95);
}

.notes-grid.drag-mode .note-card.sortable-drag {
    cursor: grabbing;
    transform: rotate(5deg) scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

.notes-grid.drag-mode .note-card.sortable-chosen {
    background-color: var(--primary-light);
    border: 2px dashed var(--primary-color);
}

/* Drag indicator */
.drag-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--success-color);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-family: var(--font-family-bengali);
    font-weight: 600;
    z-index: 9999;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Duplicate button styling */
.note-actions .btn-icon:nth-child(1) {
    color: var(--success-color);
    position: relative;
}

/* Duplicate button special effect */
.note-actions .btn-icon:nth-child(1):active {
    transform: scale(0.95);
}

/* Simple hover effect for duplicate button */
.note-actions .btn-icon:nth-child(1):hover {
    background-color: var(--success-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* Tooltip fade in animation */
@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Pulse animation for duplicated notes */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Print button styling */
.note-actions .btn-icon:nth-child(2) {
    color: var(--info-color);
}

.note-actions .btn-icon:nth-child(2):hover {
    background-color: var(--info-color);
    color: white;
}

/* PDF button styling */
.note-actions .btn-icon:nth-child(3) {
    color: var(--danger-color);
}

.note-actions .btn-icon:nth-child(3):hover {
    background-color: var(--danger-color);
    color: white;
}

/* Edit button styling */
.note-actions .btn-icon:nth-child(4) {
    color: var(--primary-color);
}

.note-actions .btn-icon:nth-child(4):hover {
    background-color: var(--primary-color);
    color: white;
}

/* Delete button styling */
.note-actions .btn-icon:nth-child(5) {
    color: var(--danger-color);
}

.note-actions .btn-icon:nth-child(5):hover {
    background-color: var(--danger-color);
    color: white;
}

/* ===== HELP MODAL STYLES ===== */
.help-modal .modal-content {
    max-width: 800px;
    max-height: 90vh;
}

.help-body {
    max-height: 70vh;
    overflow-y: auto;
}

.help-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.help-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.shortcut-item kbd {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0.25rem;
}

.actions-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.action-item i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.action-details strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.action-details p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--bg-secondary);
    border-radius: 6px;
    border-left: 4px solid var(--success-color);
    position: relative;
}

.tips-list li::before {
    content: '💡';
    position: absolute;
    left: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--success-color);
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

/* ===== SYMBOLS TOOLBAR ===== */
.symbols-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 100;
}

.symbols-title {
    font-family: var(--font-family-bengali);
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.symbols-close-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
    flex-shrink: 0;
}

.symbols-close-btn:hover {
    background: var(--danger-hover);
    transform: scale(1.1);
}

.symbols-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    flex: 1;
    max-height: 120px;
    overflow-y: auto;
    padding: 0.25rem 0;
}

.symbol-btn {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.375rem 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    transition: var(--transition);
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.symbol-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.symbol-btn:active {
    transform: scale(0.95);
}

/* Scrollbar styling for symbols container */
.symbols-container::-webkit-scrollbar {
    width: 4px;
}

.symbols-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.symbols-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

.symbols-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Responsive design for symbols toolbar */
@media (max-width: 768px) {
    .symbols-toolbar {
        padding: 0.5rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .symbols-title {
        font-size: 0.75rem;
        order: 1;
    }

    .symbols-close-btn {
        order: 3;
        margin-left: auto;
    }

    .symbols-container {
        width: 100%;
        max-height: 80px;
        order: 2;
    }

    .symbol-btn {
        min-width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }
}

/* ===== EMPTY STATE ===== */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h2 {
    font-family: var(--font-family-bengali);
    font-size: 1.8rem;
    font-weight: 400;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.empty-state p {
    font-family: var(--font-family-bengali);
    font-weight: 400;
    margin-bottom: 2rem;
    font-size: 1.2rem;
}

/* ===== NOTE TITLE SECTION ===== */
.note-title-section {
    margin-bottom: 1rem;
}

.note-title-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
    font-family: var(--font-family-bengali);
}

.note-title-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family-bengali);
    transition: var(--transition);
    background-color: white;
}

.note-title-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.note-title-input::placeholder {
    color: #999;
    font-family: var(--font-family-bengali);
}

/* ===== MODALS ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    overflow: hidden;
}

.modal.active {
    display: flex;
    animation: modalFadeIn 0.3s ease;
}

/* Prevent body scroll when modal is open */
body.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-background);
    backdrop-filter: blur(2px);
    z-index: 999;
}

.modal-content {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    z-index: 1001;
    animation: modalSlideIn 0.3s ease;
    min-width: 400px;
    min-height: 300px;
}













/* Tooltip for modal title double-click */
.modal-header h3[title] {
    position: relative;
}

.modal-header h3[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1004;
    margin-bottom: 5px;
}

.modal-header h3[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--dark-color);
    z-index: 1004;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-family: var(--font-family-bengali);
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--text-color);
}

.modal-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.modal-body {
    padding: 1.25rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.25rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--light-color);
}

/* ===== FULLSCREEN MODE ===== */
.modal.fullscreen {
    padding: 0;
}

.modal.fullscreen .modal-content {
    max-width: 100vw;
    max-height: 100vh;
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    animation: none;
}

.modal.fullscreen .modal-body {
    max-height: calc(100vh - 200px);
    padding: 1.5rem;
}

.modal.fullscreen .note-editor-container {
    min-height: calc(100vh - 250px);
}

.modal.fullscreen #noteEditor {
    min-height: calc(100vh - 250px);
}

.modal.fullscreen .jodit-workplace {
    min-height: calc(100vh - 250px);
    height: calc(100vh - 250px) !important;
}

.modal.fullscreen .jodit-container {
    height: calc(100vh - 200px) !important;
}

.modal.fullscreen .jodit-wysiwyg {
    min-height: calc(100vh - 300px) !important;
    height: calc(100vh - 300px) !important;
    max-height: calc(100vh - 300px) !important;
}

.modal.fullscreen .jodit-wysiwyg[contenteditable="true"] {
    min-height: calc(100vh - 300px) !important;
    height: calc(100vh - 300px) !important;
    max-height: calc(100vh - 300px) !important;
    overflow-y: auto !important;
}

/* Force the iframe content to resize */
.modal.fullscreen .jodit-wysiwyg iframe {
    min-height: calc(100vh - 300px) !important;
    height: calc(100vh - 300px) !important;
}

/* Ensure the editor content area expands */
.modal.fullscreen .jodit-wysiwyg body {
    min-height: calc(100vh - 300px) !important;
}

/* Additional selectors for better targeting */
.modal.fullscreen .jodit-container .jodit-workplace .jodit-wysiwyg {
    min-height: calc(100vh - 300px) !important;
    height: calc(100vh - 300px) !important;
    max-height: calc(100vh - 300px) !important;
}

.modal.fullscreen .jodit-container .jodit-workplace {
    min-height: calc(100vh - 250px) !important;
    height: calc(100vh - 250px) !important;
}

/* ===== SEARCH MODAL FULLSCREEN ===== */
#searchModal.fullscreen .modal-body {
    max-height: calc(100vh - 120px);
    padding: 1.5rem;
}

#searchModal.fullscreen .search-results {
    max-height: calc(100vh - 220px);
    overflow-y: auto;
}

#fullscreenBtn.active i {
    transform: rotate(45deg);
}

#searchFullscreenBtn.active i {
    transform: rotate(45deg);
}

/* ===== COMPACT NOTE EDITOR STYLES ===== */

/* Note Meta Section */
.note-meta-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.note-title-wrapper {
    margin-bottom: 0.75rem;
}

.note-title-input-compact {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family-bengali);
    font-size: 1.1rem;
    font-weight: 500;
    background: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.note-title-input-compact:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
    background: var(--card-background);
}

.note-controls-row {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.priority-select-compact {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-family: var(--font-family-bengali);
    background: var(--card-background);
    color: var(--text-color);
    font-size: 0.9rem;
    min-width: 100px;
    transition: all 0.2s ease;
}

.priority-select-compact:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1);
}

.reminder-container-compact {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.reminder-input-compact {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.85rem;
    background: var(--card-background);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.reminder-input-compact:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1);
}

.btn-icon-sm {
    padding: 0.4rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--card-background);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon-sm:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.toggle-advanced {
    background: var(--light-color);
}

.toggle-advanced.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Advanced Options Section */
.advanced-options {
    margin-bottom: 1rem;
    animation: slideDown 0.3s ease;
}

.advanced-section {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
}

/* Compact Password Section */
.note-password-section-compact {
    margin-bottom: 1rem;
}

.password-container-compact {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.password-toggle-compact {
    display: flex;
    align-items: center;
}

.checkbox-label-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
}

.checkmark-compact {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label-compact input[type="checkbox"]:checked + .checkmark-compact {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label-compact input[type="checkbox"]:checked + .checkmark-compact::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.password-input-group-compact {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.password-field-compact {
    display: flex;
    gap: 0.25rem;
}

.password-input-compact {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    background: var(--card-background);
    color: var(--text-color);
}

.password-strength-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.strength-bar-compact {
    flex: 1;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.strength-text-compact {
    color: var(--text-muted);
    font-size: 0.75rem;
}

/* Compact File Attachments */
.note-attachments-section-compact {
    margin-top: 1rem;
}

.file-upload-area-compact {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--card-background);
}

.file-upload-area-compact:hover {
    border-color: var(--primary-color);
    background: rgba(74, 107, 223, 0.05);
}

.file-upload-content-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.file-upload-content-compact i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.attached-files-compact {
    margin-top: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Compact Editor Section */
.editor-section-compact {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tabs-header-compact {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    gap: 0.5rem;
    position: relative;
}

.tabs-nav-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.tab-scroll-btn {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    opacity: 0.7;
}

.tab-scroll-btn:hover {
    background: var(--primary-color);
    color: white;
    opacity: 1;
    transform: scale(1.05);
}

.tab-scroll-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
}

.tab-scroll-btn:disabled:hover {
    background: var(--card-background);
    color: var(--text-muted);
}

.tabs-nav-compact {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
    padding: 0 4px;
    transition: transform 0.3s ease;
}

.tabs-nav-compact::-webkit-scrollbar {
    display: none;
}

.tab-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.85rem;
    color: var(--text-color);
    position: relative;
}

.tab-compact .tab-number {
    background: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.15rem 0.4rem;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
    flex-shrink: 0;
    line-height: 1;
}

.tab-compact.active .tab-number {
    background: var(--success-color);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.tab-compact:hover .tab-number {
    background: white;
    color: var(--primary-color);
}

.tab-compact:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tab-compact.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tab-compact .tab-close {
    font-size: 0.7rem;
    opacity: 0.7;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 3px;
}

.tab-compact .tab-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
}

.tabs-actions-compact {
    display: flex;
    gap: 0.5rem;
}

.add-tab-btn-compact {
    background: var(--primary-color);
    color: white;
    border: none;
}

.tab-content-compact {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.active-tab-header-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.tab-title-input-compact {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    background: var(--card-background);
    color: var(--text-color);
}

.tab-title-input-compact:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1);
}

.delete-tab-btn-compact {
    background: var(--danger-color);
    color: white;
    border: none;
}

.note-editor-container-compact {
    flex: 1;
    min-height: 400px;
    position: relative;
}

.note-editor-container-compact #noteEditor {
    width: 100%;
    height: 100%;
    min-height: 400px;
    border: none;
    padding: 1rem;
    font-family: var(--font-family-bengali);
    font-size: 1rem;
    line-height: 1.6;
    background: var(--card-background);
    color: var(--text-color);
    resize: none;
    outline: none;
}

/* Animation for slide down */
@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 500px;
        transform: translateY(0);
    }
}

/* ===== DARK MODE COMPACT STYLES ===== */
[data-theme="dark"] .note-meta-section {
    background: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .note-title-input-compact {
    background: var(--light-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .note-title-input-compact:focus {
    background: var(--card-background);
    border-color: var(--primary-color);
}

[data-theme="dark"] .priority-select-compact,
[data-theme="dark"] .reminder-input-compact {
    background: var(--light-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-icon-sm {
    background: var(--light-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .btn-icon-sm:hover {
    background: var(--primary-color);
    color: white;
}

[data-theme="dark"] .advanced-section {
    background: var(--light-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .password-input-compact {
    background: var(--card-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .file-upload-area-compact {
    background: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .file-upload-area-compact:hover {
    border-color: var(--primary-color);
    background: rgba(90, 123, 239, 0.1);
}

[data-theme="dark"] .editor-section-compact {
    background: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .tabs-header-compact {
    background: var(--light-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .tab-scroll-btn {
    background: var(--dark-color);
    border-color: var(--border-color);
    color: var(--text-muted);
}

[data-theme="dark"] .tab-scroll-btn:hover {
    background: var(--primary-color);
    color: white;
}

[data-theme="dark"] .tab-scroll-btn:disabled:hover {
    background: var(--dark-color);
    color: var(--text-muted);
}

/* Responsive tab scrolling */
@media (max-width: 768px) {
    .tab-scroll-btn {
        width: 28px;
        height: 28px;
    }

    .tab-scroll-btn i {
        font-size: 0.8rem;
    }

    .tabs-header-compact {
        padding: 0.5rem 0.75rem;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .tab-scroll-btn {
        width: 24px;
        height: 24px;
    }

    .tab-scroll-btn i {
        font-size: 0.7rem;
    }

    .tabs-header-compact {
        padding: 0.4rem 0.5rem;
        gap: 0.2rem;
    }
}

[data-theme="dark"] .tab-compact {
    background: var(--card-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .active-tab-header-compact {
    background: var(--light-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .tab-title-input-compact {
    background: var(--card-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .note-editor-container-compact #noteEditor {
    background: var(--card-background);
    color: var(--text-color);
}

/* ===== SCROLL BUTTONS ===== */
.scroll-buttons {
    position: fixed;
    right: 2rem;
    bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 1000;
}

.scroll-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.scroll-btn:hover {
    background: var(--primary-hover);
    transform: translateY(0) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.scroll-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.scroll-to-bottom {
    background: linear-gradient(135deg, var(--info-color), var(--info-hover));
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.scroll-to-bottom:hover {
    background: linear-gradient(135deg, var(--info-hover), var(--info-color));
}

/* Scroll button animations */
@keyframes scrollBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

.scroll-btn:active {
    animation: scrollBounce 0.6s ease;
}

/* Dark mode scroll buttons */
[data-theme="dark"] .scroll-btn {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .scroll-btn:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .scroll-buttons {
        right: 1rem;
        bottom: 1rem;
    }

    .scroll-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .scroll-buttons {
        right: 0.75rem;
        bottom: 0.75rem;
    }

    .scroll-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }
}

/* ===== SCROLL PROGRESS INDICATOR ===== */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    z-index: 1001;
    transition: width 0.1s ease;
}

[data-theme="dark"] .scroll-progress {
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
}

/* Scroll button tooltips */
.scroll-btn::before {
    content: attr(title);
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dark-color);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1002;
}

.scroll-btn:hover::before {
    opacity: 1;
    visibility: visible;
    right: 65px;
}

[data-theme="dark"] .scroll-btn::before {
    background: var(--card-background);
    border: 1px solid var(--border-color);
}

/* Scroll button pulse animation when page loads */
@keyframes scrollPulse {
    0% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 4px 12px rgba(74, 107, 223, 0.4), 0 0 0 10px rgba(74, 107, 223, 0.1);
    }
    100% {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

.scroll-btn.pulse {
    animation: scrollPulse 2s ease-in-out;
}

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--dark-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}



/* ===== ANIMATED FOOTER ===== */
.animated-footer {
    position: relative;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    min-height: 300px;
    overflow: hidden;
    margin-top: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.footer-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: wave 6s ease-in-out infinite;
}

.wave-1 {
    animation-delay: 0s;
    opacity: 0.7;
}

.wave-2 {
    animation-delay: -2s;
    opacity: 0.5;
    height: 120px;
}

.wave-3 {
    animation-delay: -4s;
    opacity: 0.3;
    height: 80px;
}

@keyframes wave {
    0%, 100% {
        transform: translateX(-50%) translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateX(-50%) translateY(-20px) rotate(180deg);
    }
}

.footer-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.footer-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.6;
    animation: float 8s ease-in-out infinite;
}

.particle-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle-2 {
    top: 60%;
    left: 20%;
    animation-delay: -1s;
}

.particle-3 {
    top: 30%;
    right: 15%;
    animation-delay: -2s;
}

.particle-4 {
    top: 70%;
    right: 25%;
    animation-delay: -3s;
}

.particle-5 {
    top: 40%;
    left: 80%;
    animation-delay: -4s;
}

.particle-6 {
    top: 80%;
    left: 70%;
    animation-delay: -5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.footer-main {
    position: relative;
    z-index: 3;
}

.footer-logo {
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.footer-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
    animation: pulse 2s ease-in-out infinite;
}

.footer-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes glow {
    from {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 10px rgba(255, 255, 255, 0.2);
    }
    to {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.4);
    }
}

.footer-text {
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.4s both;
}

.footer-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.developer-name {
    font-weight: 700;
    font-size: 1.3rem;
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    display: inline-block;
    text-shadow: none;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.footer-social {
    display: flex !important;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.6s both;
    visibility: visible !important;
    opacity: 1 !important;
}

.social-link {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    visibility: visible !important;
    opacity: 1 !important;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.social-link i {
    font-size: 1.5rem;
    color: white;
    transition: all 0.3s ease;
}

/* Fallback for when Font Awesome doesn't load */
.social-link:nth-child(1)::before {
    content: "🐙";
    font-size: 1.5rem;
    display: none;
}

.social-link:nth-child(2)::before {
    content: "💼";
    font-size: 1.5rem;
    display: none;
}

.social-link:nth-child(3)::before {
    content: "🐦";
    font-size: 1.5rem;
    display: none;
}

.social-link:nth-child(4)::before {
    content: "✉️";
    font-size: 1.5rem;
    display: none;
}

.social-link:nth-child(5)::before {
    content: "📘";
    font-size: 1.5rem;
    display: none;
}

.social-link:nth-child(6)::before {
    content: "📷";
    font-size: 1.5rem;
    display: none;
}

/* Show fallback when Font Awesome icons are not loaded */
.social-link i:not([class*="fa-"]) + ::before,
.social-link:empty::before {
    display: block;
}

.social-link:hover i {
    transform: rotate(360deg);
}

.footer-copyright {
    animation: fadeInUp 1s ease 0.8s both;
}

.footer-copyright p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.footer-glow {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 100px;
    background: radial-gradient(ellipse, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: translateX(-50%) scale(1);
    }
    50% {
        opacity: 0.6;
        transform: translateX(-50%) scale(1.2);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== DARK MODE FOOTER STYLES ===== */
[data-theme="dark"] .animated-footer {
    background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
}

[data-theme="dark"] .footer-wave {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .social-link {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .social-link:hover {
    background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .footer-glow {
    background: radial-gradient(ellipse, rgba(90, 123, 239, 0.3) 0%, transparent 70%);
}

/* ===== RESPONSIVE FOOTER ===== */
@media (max-width: 768px) {
    .animated-footer {
        min-height: 250px;
        margin-top: 2rem;
    }

    .footer-content {
        padding: 1.5rem;
    }

    .footer-title {
        font-size: 1.5rem;
    }

    .footer-description {
        font-size: 1rem;
    }

    .developer-name {
        font-size: 1.1rem;
    }

    .footer-social {
        gap: 1rem;
    }

    .social-link {
        width: 40px;
        height: 40px;
    }

    .social-link i {
        font-size: 1.2rem;
    }

    .particle {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .animated-footer {
        min-height: 200px;
    }

    .footer-content {
        padding: 1rem;
    }

    .footer-title {
        font-size: 1.3rem;
    }

    .footer-description {
        font-size: 0.9rem;
    }

    .developer-name {
        font-size: 1rem;
    }

    .footer-social {
        gap: 0.75rem;
    }

    .social-link {
        width: 35px;
        height: 35px;
    }

    .social-link i {
        font-size: 1rem;
    }
}

/* ===== NOTE EDITOR ===== */
.note-priority-section {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.note-priority-section label {
    font-weight: 500;
    color: var(--text-color);
}

.priority-select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family-bengali);
    background-color: white;
    outline: none;
    transition: var(--transition);
}

.priority-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

/* Priority colors for note cards */
.note-card.priority-low {
    border-left: 4px solid #28a745;
}

.note-card.priority-medium {
    border-left: 4px solid #ffc107;
}

.note-card.priority-high {
    border-left: 4px solid #fd7e14;
}

.note-card.priority-urgent {
    border-left: 4px solid #dc3545;
}

.note-priority-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.note-priority-indicator.low {
    background-color: #28a745;
}

.note-priority-indicator.medium {
    background-color: #ffc107;
}

.note-priority-indicator.high {
    background-color: #fd7e14;
}

.note-priority-indicator.urgent {
    background-color: #dc3545;
}

.note-editor-container {
    min-height: 400px;
}

#noteEditor {
    width: 100%;
    min-height: 400px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    font-family: var(--font-family-bengali);
    font-weight: 400;
    font-size: 1.2rem;
    line-height: 1.6;
    resize: vertical;
    outline: none;
    transition: var(--transition);
}

#noteEditor:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

/* ===== VERTICAL TABS STYLES ===== */
.editor-tabs-container {
    display: flex;
    gap: 1rem;
    min-height: 400px;
}

.vertical-tabs-sidebar {
    width: 250px;
    min-width: 200px;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.tabs-header h4 {
    margin: 0;
    font-family: var(--font-family-bengali);
    color: var(--text-color);
    font-size: 1rem;
}

.add-tab-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-tab-btn:hover {
    background-color: var(--primary-hover);
    transform: scale(1.1);
}

.vertical-tabs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    overflow-y: auto;
    max-height: 800px;
}

.vertical-tab {
    padding: 0.75rem;
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
}

.vertical-tab .tab-number {
    background: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    min-width: 24px;
    text-align: center;
    flex-shrink: 0;
    line-height: 1;
}

.vertical-tab.active .tab-number {
    background: var(--success-color);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.vertical-tab:hover .tab-number {
    background: var(--primary-color);
    transform: scale(1.05);
}

.vertical-tab:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

.vertical-tab.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.vertical-tab.active:hover {
    background-color: var(--primary-hover);
}

.tab-title {
    font-family: var(--font-family-bengali);
    font-size: 0.9rem;
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-close-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
}

.tab-close-btn:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
}

.vertical-tab.active .tab-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.tab-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.active-tab-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.tab-title-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-background);
    color: var(--text-color);
    font-family: var(--font-family-bengali);
    font-size: 1rem;
}

.tab-title-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.delete-tab-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.delete-tab-btn:hover {
    background-color: var(--danger-hover);
}

/* ===== SEARCH MODAL ===== */
.search-container {
    margin-bottom: 1.5rem;
}

.search-input-group {
    position: relative;
    margin-bottom: 1rem;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.875rem;
}

#searchInput {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family-bengali);
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

#searchInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.clear-search {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: var(--text-muted);
    padding: 0.25rem;
}

.search-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background-color: white;
    color: var(--text-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background-color: var(--light-color);
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
}

/* Search Sections */
.search-section {
    margin-bottom: 1.5rem;
}

.search-section-title {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 0.8rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-section-title i {
    color: var(--primary-color);
    font-size: 12px;
}

/* Bookmark Search Results */
.search-result-item.bookmark-result {
    border-left: 3px solid var(--warning-color);
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.05) 0%, transparent 100%);
}

.search-result-item.bookmark-result:hover {
    border-left-color: var(--primary-color);
    background: linear-gradient(90deg, rgba(74, 107, 223, 0.05) 0%, transparent 100%);
}

.search-result-icon {
    margin-right: 8px;
    color: var(--primary-color);
    font-size: 12px;
}

.search-result-url {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 2px;
    word-break: break-all;
}

.search-result-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    font-size: 10px;
}

.search-result-category {
    background: var(--primary-color);
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
}

.search-result-favorite {
    color: var(--warning-color);
    display: flex;
    align-items: center;
    gap: 2px;
}

.search-result-visits {
    color: var(--text-muted);
}

.search-result-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background-color: var(--light-color);
}

.search-result-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.search-result-content {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.5;
}

.search-result-date {
    font-family: var(--font-family-bengali);
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
    font-weight: 400;
    background-color: var(--light-color);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
}

/* ===== EMOJI PICKER ===== */
.emoji-modal .modal-content {
    max-width: 400px;
}

.emoji-picker {
    max-height: 500px;
}

.emoji-categories {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.emoji-category {
    padding: 0.5rem;
    border: none;
    background-color: transparent;
    font-size: 1.25rem;
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.emoji-category:hover {
    background-color: var(--light-color);
}

.emoji-category.active {
    background-color: var(--primary-color);
    color: white;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.25rem;
    max-height: 300px;
    overflow-y: auto;
}

.emoji-item {
    padding: 0.5rem;
    border: none;
    background-color: transparent;
    font-size: 1.25rem;
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.emoji-item:hover {
    background-color: var(--light-color);
    transform: scale(1.2);
}

/* ===== NOTIFICATIONS ===== */
.notification-container {
    position: fixed;
    top: 0.75rem;
    left: 0.75rem;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    max-width: 320px;
}

.notification {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.75rem 2rem 0.75rem 0.75rem;
    min-width: 250px;
    max-width: 320px;
    border-left: 3px solid var(--primary-color);
    animation: notificationSlideInLeft 0.3s ease;
    position: relative;
    word-wrap: break-word;
    font-size: 0.9rem;
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.notification-icon {
    font-size: 1rem;
    margin-top: 0.1rem;
    margin-right: 0.2rem;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-family: var(--font-family-bengali);
    font-weight: 400;
    font-size: 0.9rem;
    margin-bottom: 0.15rem;
    color: var(--text-color);
}

.notification-message {
    font-family: var(--font-family-bengali);
    font-weight: 400;
    font-size: 0.85rem;
    color: var(--text-color);
    line-height: 1.3;
    opacity: 0.9;
}

.notification-close {
    position: absolute;
    top: 0.4rem;
    right: 0.4rem;
    background-color: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.15rem;
    border-radius: 50%;
    transition: var(--transition);
    width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--danger-color);
    transform: scale(1.1);
}

.fade-out {
    animation: fadeOut 0.3s ease forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-100%);
    }
}

/* ===== ANIMATIONS ===== */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes notificationSlideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-up {
    animation: slideUp 0.3s ease;
}

/* ===== JODIT EDITOR CUSTOMIZATION ===== */
.jodit-container {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.jodit-toolbar {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.jodit-workplace {
    min-height: 400px;
}

.jodit-wysiwyg {
    font-family: var(--font-family-bengali), var(--font-family-english);
    font-weight: 500;
    font-size: 16px !important;
    line-height: 1.6 !important;
    padding: 1rem;
}

/* Unified font styling for all text in Jodit editor */
.jodit-wysiwyg *,
.jodit-wysiwyg p,
.jodit-wysiwyg div,
.jodit-wysiwyg span,
.jodit-wysiwyg h1,
.jodit-wysiwyg h2,
.jodit-wysiwyg h3,
.jodit-wysiwyg h4,
.jodit-wysiwyg h5,
.jodit-wysiwyg h6,
.jodit-wysiwyg li,
.jodit-wysiwyg td,
.jodit-wysiwyg th,
.jodit-wysiwyg blockquote,
.jodit-wysiwyg pre,
.jodit-wysiwyg code {
    font-family: var(--font-family-bengali), var(--font-family-english) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

/* Specific heading sizes */
.jodit-wysiwyg h1 {
    font-size: 24px !important;
    line-height: 1.4 !important;
}

.jodit-wysiwyg h2 {
    font-size: 22px !important;
    line-height: 1.4 !important;
}

.jodit-wysiwyg h3 {
    font-size: 20px !important;
    line-height: 1.4 !important;
}

.jodit-wysiwyg h4 {
    font-size: 18px !important;
    line-height: 1.5 !important;
}

.jodit-wysiwyg h5 {
    font-size: 16px !important;
    line-height: 1.5 !important;
}

.jodit-wysiwyg h6 {
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* Bengali text specific styling (legacy support) */
.jodit-wysiwyg .bengali-text,
.jodit-wysiwyg [lang="bn"] {
    font-family: var(--font-family-bengali) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

/* Horizontal line styling in Jodit editor */
.jodit-wysiwyg hr {
    border: none !important;
    border-top: 2px solid var(--border-color) !important;
    margin: 15px 0 !important;
    width: 100% !important;
    display: block !important;
    height: 2px !important;
    background: none !important;
    clear: both !important;
    box-sizing: border-box !important;
}

/* Dark theme horizontal line */
[data-theme="dark"] .jodit-wysiwyg hr {
    border-top-color: var(--text-muted) !important;
}

/* Light theme horizontal line */
[data-theme="light"] .jodit-wysiwyg hr {
    border-top-color: #ccc !important;
}

/* ===== JODIT EDITOR DARK MODE STYLES ===== */
[data-theme="dark"] .jodit-container {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-toolbar {
    background-color: var(--dark-color) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-toolbar button {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .jodit-toolbar button:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

[data-theme="dark"] .jodit-toolbar .jodit-toolbar__box {
    background-color: var(--dark-color) !important;
}

[data-theme="dark"] .jodit-wysiwyg {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-wysiwyg[contenteditable="true"] {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-workplace {
    background-color: var(--card-background) !important;
}

[data-theme="dark"] .jodit-status-bar {
    background-color: var(--dark-color) !important;
    border-top: 1px solid var(--border-color) !important;
    color: var(--text-muted) !important;
}

/* Jodit popup menus in dark mode */
[data-theme="dark"] .jodit-popup {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-popup .jodit-popup__content {
    background-color: green !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-popup button {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .jodit-popup button:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Jodit dropdown menus */
[data-theme="dark"] .jodit-dropdown {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-dropdown .jodit-dropdown__content {
    background-color: var(--card-background) !important;
}

[data-theme="dark"] .jodit-dropdown .jodit-dropdown__content a {
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-dropdown .jodit-dropdown__content a:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Jodit color picker */
[data-theme="dark"] .jodit-color-picker {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
}

/* Jodit table editor */
[data-theme="dark"] .jodit-table-editor {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
}

/* Jodit image editor */
[data-theme="dark"] .jodit-image-editor {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

/* Jodit link editor */
[data-theme="dark"] .jodit-link-editor {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-link-editor input {
    background-color: var(--light-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

/* Jodit source editor */
[data-theme="dark"] .jodit-source {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-source textarea {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

/* Jodit iframe content (for better text visibility) */
[data-theme="dark"] .jodit-wysiwyg iframe {
    background-color: var(--card-background) !important;
}

[data-theme="dark"] .jodit-wysiwyg iframe body {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

/* Additional Jodit elements */
[data-theme="dark"] .jodit-toolbar__box .jodit-toolbar__group {
    border-right: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-toolbar .jodit-toolbar__button {
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-toolbar .jodit-toolbar__button:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

[data-theme="dark"] .jodit-toolbar .jodit-toolbar__button.jodit-toolbar__button_active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Jodit placeholder text */
[data-theme="dark"] .jodit-wysiwyg::before {
    color: var(--text-muted) !important;
}

/* Jodit selection and focus styles */
[data-theme="dark"] .jodit-wysiwyg:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: -2px !important;
}

/* Additional Jodit dark mode fixes */
[data-theme="dark"] .jodit-container .jodit-toolbar__box {
    /* background-color: var(--dark-color) !important; */
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .jodit-container .jodit-toolbar__button {
    color: var(--text-color) !important;
    background-color: transparent !important;
    border: none !important;
}

[data-theme="dark"] .jodit-container .jodit-toolbar__button:hover,
[data-theme="dark"] .jodit-container .jodit-toolbar__button.jodit-toolbar__button_active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

[data-theme="dark"] .jodit-container .jodit-toolbar__button svg {
    fill: var(--text-color) !important;
}

[data-theme="dark"] .jodit-container .jodit-toolbar__button:hover svg,
[data-theme="dark"] .jodit-container .jodit-toolbar__button.jodit-toolbar__button_active svg {
    fill: white !important;
}

/* Jodit input fields in dark mode */
[data-theme="dark"] .jodit-ui-input {
    background-color: var(--light-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-ui-input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(90, 123, 239, 0.2) !important;
}

/* Jodit dialog boxes */
[data-theme="dark"] .jodit-dialog {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-dialog .jodit-dialog__header {
    background-color: var(--dark-color) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-dialog .jodit-dialog__content {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-dialog .jodit-dialog__footer {
    background-color: var(--dark-color) !important;
    border-top: 1px solid var(--border-color) !important;
}

/* Jodit tabs */
[data-theme="dark"] .jodit-tabs {
    background-color: var(--card-background) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-tabs .jodit-tabs__tab {
    color: var(--text-color) !important;
    background-color: transparent !important;
}

[data-theme="dark"] .jodit-tabs .jodit-tabs__tab.jodit-tabs__tab_active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Jodit file browser */
[data-theme="dark"] .jodit-filebrowser {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-filebrowser .jodit-filebrowser__tree {
    background-color: var(--light-color) !important;
    border-right: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .jodit-filebrowser .jodit-filebrowser__files {
    background-color: var(--card-background) !important;
}

/* Jodit context menu */
[data-theme="dark"] .jodit-context-menu {
    background-color: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .jodit-context-menu .jodit-context-menu__item {
    color: var(--text-color) !important;
}

[data-theme="dark"] .jodit-context-menu .jodit-context-menu__item:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Jodit tooltip */
[data-theme="dark"] .jodit-tooltip {
    background-color: var(--dark-color) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

/* Force text color and unified styling in dark mode editor content */
[data-theme="dark"] .jodit-wysiwyg *,
[data-theme="dark"] .jodit-wysiwyg p,
[data-theme="dark"] .jodit-wysiwyg div,
[data-theme="dark"] .jodit-wysiwyg span,
[data-theme="dark"] .jodit-wysiwyg h1,
[data-theme="dark"] .jodit-wysiwyg h2,
[data-theme="dark"] .jodit-wysiwyg h3,
[data-theme="dark"] .jodit-wysiwyg h4,
[data-theme="dark"] .jodit-wysiwyg h5,
[data-theme="dark"] .jodit-wysiwyg h6,
[data-theme="dark"] .jodit-wysiwyg li,
[data-theme="dark"] .jodit-wysiwyg td,
[data-theme="dark"] .jodit-wysiwyg th,
[data-theme="dark"] .jodit-wysiwyg blockquote,
[data-theme="dark"] .jodit-wysiwyg pre,
[data-theme="dark"] .jodit-wysiwyg code {
    color: var(--text-color) !important;
    font-family: var(--font-family-bengali), var(--font-family-english) !important;
    font-size: 20px !important;
    line-height: 26px !important;
}

/* Dark mode specific heading sizes */
[data-theme="dark"] .jodit-wysiwyg h1 {
    font-size: 24px !important;
    line-height: 1.4 !important;
}

[data-theme="dark"] .jodit-wysiwyg h2 {
    font-size: 22px !important;
    line-height: 1.4 !important;
}

[data-theme="dark"] .jodit-wysiwyg h3 {
    font-size: 20px !important;
    line-height: 1.4 !important;
}

[data-theme="dark"] .jodit-wysiwyg h4 {
    font-size: 18px !important;
    line-height: 1.5 !important;
}

[data-theme="dark"] .jodit-wysiwyg h5 {
    font-size: 16px !important;
    line-height: 1.5 !important;
}

[data-theme="dark"] .jodit-wysiwyg h6 {
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* Special handling for iframe content in Jodit editor */
[data-theme="dark"] .jodit-wysiwyg iframe html,
[data-theme="dark"] .jodit-wysiwyg iframe body {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
    font-family: var(--font-family-bengali), var(--font-family-english) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

/* Iframe content styling for all elements */
[data-theme="dark"] .jodit-wysiwyg iframe body *,
.jodit-wysiwyg iframe body * {
    font-family: var(--font-family-bengali), var(--font-family-english) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

/* Light theme iframe content */
.jodit-wysiwyg iframe html,
.jodit-wysiwyg iframe body {
    font-family: var(--font-family-bengali), var(--font-family-english) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

/* Ensure placeholder text is visible in dark mode */
[data-theme="dark"] .jodit-wysiwyg[data-placeholder]:empty:before {
    color: var(--text-muted) !important;
}

/* Fix for any remaining white backgrounds */


[data-theme="dark"] .jodit-workplace,
[data-theme="dark"] .jodit-wysiwyg {
    background-color: var(--card-background) !important;
    color: var(--text-color) !important;
}

/* ===== VERTICAL TABS DARK MODE STYLES ===== */
[data-theme="dark"] .vertical-tabs-sidebar {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .tabs-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .tabs-header h4 {
    color: var(--text-color);
}

[data-theme="dark"] .vertical-tab {
    background-color: var(--input-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .vertical-tab:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .vertical-tab.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

[data-theme="dark"] .tab-title {
    color: inherit;
}

[data-theme="dark"] .tab-close-btn {
    color: inherit;
}

[data-theme="dark"] .active-tab-header {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark"] .tab-title-input {
    background-color: var(--input-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .tab-title-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* ===== DARK MODE DATE/TIME STYLES ===== */
[data-theme="dark"] .date {
    background-color: #1e3a8a;
    color: #93c5fd;
    border-color: #3b82f6;
}

[data-theme="dark"] .time {
    background-color: #581c87;
    color: #d8b4fe;
    border-color: #a855f7;
}

[data-theme="dark"] .datetime {
    background-color: #14532d;
    color: #86efac;
    border-color: #22c55e;
}

[data-theme="dark"] .date:hover,
[data-theme="dark"] .time:hover,
[data-theme="dark"] .datetime:hover {
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

/* ===== DARK MODE INPUT STYLES ===== */
[data-theme="dark"] .reminder-input,
[data-theme="dark"] .expiry-input {
    background-color: var(--input-background);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .reminder-input:focus,
[data-theme="dark"] .expiry-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* ===== DARK MODE NOTE DATE STYLES ===== */
[data-theme="dark"] .note-date {
    color: var(--text-muted);
}

[data-theme="dark"] .note-date:hover {
    color: var(--text-color);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large screens - ensure 4 columns */
@media (min-width: 1200px) {
    .notes-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 1.5rem;
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
    }
}

/* Medium-large screens - 4 columns */
@media (min-width: 992px) and (max-width: 1199px) {
    .notes-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 1.2rem;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    /* Tablet styles */
    .modal-content {
        max-width: 750px;
    }

    .note-editor-container {
        min-height: 350px;
    }

    #noteEditor {
        min-height: 350px;
    }

    .notes-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .header .container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    /* External title responsive */
    .external-title-text {
        font-size: 0.95rem;
    }

    .note-external-title {
        padding: 0.5rem 0.75rem;
        top: -2rem;
    }

    .note-card {
        margin-top: 2.5rem;
    }

    .priority-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    /* Add Note Button responsive */
    #addNoteBtn {
        width: 32px;
        height: 32px;
        font-size: 0.85rem;
    }

    /* Compact buttons responsive */
    .btn-compact {
        width: 1.8rem;
        height: 1.8rem;
        font-size: 0.7rem;
        padding: 0.3rem;
    }

    /* Path display responsive styles */
    .path-container {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .path-info {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .path-label {
        font-size: 0.9rem;
    }

    .path-text {
        font-size: 0.8rem;
        padding: 0.5rem;
        word-break: break-all;
        overflow-wrap: break-word;
    }

    .copy-path-btn {
        align-self: center;
        padding: 0.75rem 1.5rem;
    }

    .copy-path-btn i {
        margin-right: 0.5rem;
    }

    .copy-path-btn::after {
        content: ' কপি করুন';
        font-family: var(--font-family-bengali);
    }

    /* Dark mode mobile styles for path display */
    [data-theme="dark"] .path-display-section {
        background: var(--background-color) !important;
        border-bottom: 1px solid var(--border-color) !important;
    }

    [data-theme="dark"] .path-container {
        background: var(--card-background) !important;
        border: 1px solid var(--border-color) !important;
    }

    [data-theme="dark"] .path-info i {
        color: var(--primary-color) !important;
    }

    [data-theme="dark"] .path-label {
        color: var(--text-color) !important;
        font-weight: 500 !important;
    }

    [data-theme="dark"] .path-text {
        background: var(--light-color) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-muted) !important;
    }

    [data-theme="dark"] .copy-path-btn {
        background: var(--primary-color) !important;
        color: white !important;
        border: none !important;
    }

    [data-theme="dark"] .copy-path-btn:hover {
        background: var(--primary-hover) !important;
    }

    [data-theme="dark"] .copy-path-btn i {
        color: white !important;
    }

    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .sort-container {
        order: -1;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .sort-select {
        min-width: 200px;
    }

    .notification-panel-content {
        width: 100vw;
        max-width: 100vw;
    }

    .notes-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .modal-content {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    /* Vertical tabs responsive design */
    .editor-tabs-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .vertical-tabs-sidebar {
        width: 100%;
        min-width: auto;
        max-height: 200px;
        order: 2;
    }

    .tab-content-area {
        order: 1;
    }

    .vertical-tabs {
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        max-height: none;
        gap: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .vertical-tab {
        min-width: 120px;
        flex-shrink: 0;
    }

    .tabs-header h4 {
        font-size: 0.9rem;
    }

    .add-tab-btn {
        width: 25px;
        height: 25px;
    }

    /* Keep editor smaller on mobile */
    .note-editor-container {
        min-height: 250px;
    }

    #noteEditor {
        min-height: 250px;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-footer .btn {
        width: 100%;
    }

    .search-filters {
        justify-content: center;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .notification-container {
        left: 0.4rem;
        right: 0.4rem;
        top: 0.4rem;
        max-width: calc(100% - 0.8rem);
    }

    .notification {
        min-width: auto;
        width: 100%;
        max-width: 100%;
    }

    /* File upload area mobile styles */
    .file-upload-area {
        padding: 0.5rem;
        min-height: 50px;
    }

    .file-upload-content {
        flex-direction: column;
        gap: 0.25rem;
    }

    .file-upload-content p {
        font-size: 0.8rem;
    }

    .file-upload-hint {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 1.25rem;
    }

    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .notes-grid {
        gap: 0.75rem;
    }

    .note-card {
        padding: 1rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-icon {
        font-size: 3rem;
    }

    .emoji-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-center {
    justify-content: center;
}

.align-center {
    align-items: center;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-3 {
    padding: 0.75rem;
}

.p-4 {
    padding: 1rem;
}

/* ===== JODIT POPUP STYLES ===== */
.jodit-popup {
    background: white !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--box-shadow) !important;
    z-index: 10000 !important;
}

.jodit-popup button {
    transition: var(--transition) !important;
}

.jodit-popup button:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    transform: scale(1.1) !important;
}

/* ===== ENHANCED ANIMATIONS ===== */
.note-card {
    animation: slideUpFade 0.4s ease-out;
}

@keyframes slideUpFade {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.note-card:hover .note-priority-indicator {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* ===== PRIORITY SPECIFIC STYLES ===== */
.note-card.priority-urgent {
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.note-card.priority-urgent:hover {
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3);
}

.note-card.priority-high {
    box-shadow: 0 2px 8px rgba(253, 126, 20, 0.2);
}

.note-card.priority-high:hover {
    box-shadow: 0 4px 16px rgba(253, 126, 20, 0.3);
}

.note-card.priority-medium {
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.note-card.priority-medium:hover {
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
}

.note-card.priority-low {
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.note-card.priority-low:hover {
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

/* ===== DASHBOARD STYLES ===== */
.btn-dashboard {
    background-color: #6f42c1;
    color: white;
    border: none;
}

.btn-dashboard:hover {
    background-color: #5a2d91;
    transform: translateY(-2px);
}

.dashboard-modal .modal-content {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
}

.dashboard-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dashboard-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: white;
}

.stat-content p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-container h4 {
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.chart-container canvas {
    max-height: 250px;
}

.recent-activity {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.recent-activity h4 {
    margin-bottom: 1rem;
    color: white;
}

.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: var(--transition);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    cursor: pointer;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.activity-content {
    flex: 1;
}

.activity-content h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: white;
}

.activity-content p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.7;
}

.activity-time {
    font-size: 0.75rem;
    opacity: 0.6;
}

/* Dashboard fullscreen mode */
.dashboard-modal.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    background: rgba(0, 0, 0, 0.95);
}

.dashboard-modal.fullscreen .modal-content {
    max-width: 100%;
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
}

.dashboard-modal.fullscreen .dashboard-body {
    max-height: calc(100vh - 120px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header {
        padding: 1rem 0;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .sort-container {
        order: -1;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
        padding: 0.375rem 0.5rem;
    }

    .sort-label {
        font-size: 0.8rem;
    }

    .sort-select {
        min-width: 160px;
        font-size: 0.8rem;
        padding: 0.25rem 0.375rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .notes-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .note-card {
        padding: 1rem;
    }

    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .search-filters {
        flex-wrap: wrap;
    }

    .filter-btn {
        flex: 1;
        min-width: auto;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .notification-panel-content {
        width: 100%;
        right: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-body {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.25rem;
    }

    .sort-container {
        padding: 0.25rem 0.375rem;
    }

    .sort-label {
        font-size: 0.75rem;
    }

    .sort-select {
        min-width: 140px;
        font-size: 0.75rem;
        padding: 0.2rem 0.3rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .note-card h3 {
        font-size: 1rem;
    }

    .modal-content {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
    }

    .emoji-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-content h4 {
        font-size: 1.5rem;
    }

    .calendar-modal .modal-content {
        width: 95%;
    }

    .calendar-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .calendar-grid {
        font-size: 0.875rem;
    }

    .calendar-day {
        min-height: 60px;
        padding: 0.5rem;
    }

    .calendar-legend {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tags-filter-container {
        gap: 1rem;
    }

    .popular-tags-list,
    .all-tags-list {
        max-height: 150px;
    }

    .filter-actions {
        flex-direction: column;
    }
}





/* ===== REMINDER STYLES ===== */
.note-reminder-section {
    margin-bottom: 1rem;
}

.note-reminder-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.reminder-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.reminder-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family-bengali);
    transition: var(--transition);
}

.reminder-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 107, 223, 0.25);
}

.clear-reminder {
    padding: 0.75rem;
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.clear-reminder:hover {
    background-color: var(--danger-hover);
}

/* ===== CALENDAR STYLES ===== */
.calendar-modal .modal-content {
    width: 90%;
    max-width: 900px;
}

.calendar-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
}

.calendar-controls h4 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-color);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1rem;
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.calendar-day {
    background-color: white;
    padding: 0.75rem;
    min-height: 80px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    border: 2px solid transparent;
}

.calendar-day:hover {
    background-color: rgba(74, 107, 223, 0.1);
}

.calendar-day.other-month {
    background-color: var(--light-color);
    color: var(--text-muted);
}

.calendar-day.today {
    border-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.calendar-day.has-notes {
    background-color: rgba(74, 107, 223, 0.1);
}

.calendar-day.has-reminder {
    background-color: rgba(220, 53, 69, 0.1);
}

.calendar-day.has-notes.has-reminder {
    background: linear-gradient(45deg, rgba(74, 107, 223, 0.1) 50%, rgba(220, 53, 69, 0.1) 50%);
}

.calendar-day-number {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.calendar-day-notes {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.calendar-day-indicator {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.calendar-day-indicator.notes {
    background-color: var(--primary-color);
}

.calendar-day-indicator.reminder {
    background-color: var(--danger-color);
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.legend-color.has-notes {
    background-color: rgba(74, 107, 223, 0.3);
}

.legend-color.has-reminder {
    background-color: rgba(220, 53, 69, 0.3);
}

.legend-color.today {
    background-color: rgba(255, 193, 7, 0.3);
}



.note-reminder {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0.5rem 0;
    padding: 0.25rem 0.5rem;
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 500;
}

.note-reminder i {
    font-size: 0.625rem;
}





/* ===== SETTINGS MODAL STYLES ===== */
.settings-modal .modal-content {
    max-width: 800px;
}

.settings-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-size: 1.125rem;
}

/* Important Settings Panel */
.important-settings {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    border: 2px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.important-settings h4 {
    color: #ff6b35 !important;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.important-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.btn-important {
    width: 100%;
    padding: 1rem;
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-important i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.btn-important span {
    font-weight: 600;
    font-size: 0.95rem;
    line-height: 1.2;
}

.btn-important small {
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1.3;
    margin-top: 0.25rem;
}

.btn-important:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Danger Zone Styling */
.danger-zone {
    grid-column: 1 / -1;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px dashed rgba(220, 53, 69, 0.3);
}

.btn-danger-highlight {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: 2px solid #dc3545;
    color: white;
    position: relative;
}

.btn-danger-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-danger-highlight:hover::before {
    left: 100%;
}

.btn-danger-highlight:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.btn-danger-highlight small {
    color: #ffebee;
    font-weight: 500;
}

/* Storage Path Setting */
.storage-path-setting {
    grid-column: 1 / -1;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px dashed rgba(74, 107, 223, 0.3);
}

.storage-path-container {
    background: linear-gradient(135deg, rgba(74, 107, 223, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
    border: 2px solid rgba(74, 107, 223, 0.3);
    border-radius: 10px;
    padding: 1.2rem;
}

.storage-path-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

.storage-path-header i {
    font-size: 1.1rem;
}

.storage-path-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    align-items: stretch;
}

.storage-path-input {
    flex: 1;
    padding: 0.6rem 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.storage-path-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.2);
}

.storage-path-input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.current-path-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(74, 107, 223, 0.05);
    border: 1px solid rgba(74, 107, 223, 0.2);
    border-radius: 6px;
    padding: 0.5rem 0.8rem;
    font-size: 12px;
}

.current-path-display small {
    color: var(--text-muted);
    word-break: break-all;
    flex: 1;
    margin-right: 0.5rem;
}

.current-path-display #currentStoragePath {
    color: var(--primary-color);
    font-weight: 500;
    font-family: 'Courier New', monospace;
}

.copy-current-path-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.copy-current-path-btn:hover {
    background: var(--primary-color-dark);
    transform: scale(1.05);
}

.copy-current-path-btn i {
    font-size: 10px;
}

/* Responsive Design for Important Settings */
@media (max-width: 768px) {
    .important-settings-grid {
        grid-template-columns: 1fr;
    }

    .btn-important {
        padding: 0.8rem;
    }

    .important-settings {
        padding: 1rem;
        margin-top: 1.5rem;
    }

    .important-settings h4 {
        font-size: 1.1rem;
    }
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.setting-help {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
    font-style: italic;
}









.theme-toggle {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.theme-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-background);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    font-family: var(--font-family-bengali);
    font-size: 0.875rem;
}

.theme-btn:hover {
    border-color: var(--primary-color);
    background-color: rgba(74, 107, 223, 0.1);
}

.theme-btn.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.color-themes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.color-theme {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--card-background);
}

.color-theme:hover {
    border-color: var(--primary-color);
    background-color: rgba(74, 107, 223, 0.1);
}

.color-theme.active {
    border-color: var(--primary-color);
    background-color: rgba(74, 107, 223, 0.1);
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-theme span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

.font-select,
.number-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family-bengali);
    background-color: var(--card-background);
    color: var(--text-color);
    transition: var(--transition);
}

.font-select:focus,
.number-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 107, 223, 0.25);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
    display: inline-block;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: "";
    position: absolute;
    top: 3px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    display: block;
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1);
}

.checkbox-label input[type="checkbox"]:focus + .checkmark {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.2);
}

.checkbox-label input[type="checkbox"]:disabled + .checkmark {
    opacity: 0.5;
    cursor: not-allowed;
}

.checkbox-label input[type="checkbox"]:disabled + .checkmark:hover {
    border-color: var(--border-color);
    box-shadow: none;
}





/* ===== ANIMATIONS ===== */


@keyframes networkNodePulse {
    0% {
        r: 8;
    }
    50% {
        r: 12;
    }
    100% {
        r: 8;
    }
}

.network-node.pulse {
    animation: networkNodePulse 1s ease-in-out infinite;
}

/* ===== ADVANCED SEARCH STYLES ===== */
.advanced-search-options {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.search-mode-toggle {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.search-mode-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--bg-color);
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.search-mode-btn:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.search-mode-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}



.search-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--card-bg);
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.search-result-highlight {
    background: #ffeb3b;
    color: #000;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 500;
}

[data-theme="dark"] .search-result-highlight {
    background: #ffc107;
    color: #000;
}

/* ===== SEARCH FILTERS DARK MODE ===== */
[data-theme="dark"] .search-filters select,
[data-theme="dark"] .search-filters .filter-btn,
[data-theme="dark"] .search-filters button {
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .search-filters select:focus,
[data-theme="dark"] .search-filters .filter-btn:focus,
[data-theme="dark"] .search-filters button:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

[data-theme="dark"] .search-filters .filter-btn:hover,
[data-theme="dark"] .search-filters button:hover {
    background-color: var(--hover-color);
}

[data-theme="dark"] .search-filters .filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== DARK MODE NOTIFICATION PANEL ===== */
[data-theme="dark"] .notification-panel-content {
    background-color: var(--dark-color);
    color: var(--text-color);
    border-right: 1px solid var(--border-color);
}

[data-theme="dark"] .notification-panel-header {
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .notification-panel-header h3 {
    color: var(--text-color);
}

[data-theme="dark"] .notification-item {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .notification-item:hover {
    background-color: var(--light-color);
}

[data-theme="dark"] .notification-item-title {
    color: var(--text-color);
    font-weight: 500;
}

[data-theme="dark"] .notification-item-message {
    color: var(--text-color);
    opacity: 0.9;
    font-weight: 400;
}

[data-theme="dark"] .notification-item-time {
    background-color: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    opacity: 0.9;
    font-weight: 500;
}

[data-theme="dark"] .notification-item-type {
    background-color: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    opacity: 0.9;
    font-weight: 500;
}

[data-theme="dark"] .notification-item-delete {
    color: var(--text-muted);
}

[data-theme="dark"] .notification-item-delete:hover {
    background-color: var(--danger-color);
    color: white;
}

[data-theme="dark"] .no-notifications {
    color: var(--text-color);
    opacity: 0.8;
}

[data-theme="dark"] .notification-item-full-time small {
    color: var(--text-color);
    opacity: 0.8;
    font-weight: 400;
}

/* ===== DARK MODE TOAST NOTIFICATIONS ===== */
[data-theme="dark"] .notification {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .notification-title {
    color: var(--text-color);
    font-weight: 500;
}

[data-theme="dark"] .notification-message {
    color: var(--text-color);
    opacity: 0.9;
}

[data-theme="dark"] .notification-icon {
    color: var(--text-color);
}

[data-theme="dark"] .notification-close {
    color: var(--text-muted);
    background-color: transparent;
}

[data-theme="dark"] .notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--danger-color);
}

/* Success notification in dark mode */
[data-theme="dark"] .notification.success .notification-icon {
    color: var(--success-color);
}

[data-theme="dark"] .notification.success .notification-title {
    color: var(--success-color);
}

/* Error notification in dark mode */
[data-theme="dark"] .notification.error .notification-icon {
    color: var(--danger-color);
}

[data-theme="dark"] .notification.error .notification-title {
    color: var(--danger-color);
}

/* Warning notification in dark mode */
[data-theme="dark"] .notification.warning .notification-icon {
    color: var(--warning-color);
}

[data-theme="dark"] .notification.warning .notification-title {
    color: var(--warning-color);
}

/* Info notification in dark mode */
[data-theme="dark"] .notification.info .notification-icon {
    color: var(--info-color);
}

[data-theme="dark"] .notification.info .notification-title {
    color: var(--info-color);
}

.regex-error {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--danger-color);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: var(--shadow);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion:hover {
    background: var(--hover-bg);
}

.search-suggestion-text {
    font-weight: 500;
    color: var(--text-color);
}

.search-suggestion-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header {
        padding: 1rem 0;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .notes-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .modal-content {
        width: 95%;
        max-width: none;
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .modal-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .modal-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .note-title-input {
        font-size: 1rem;
    }

    .jodit-container {
        min-height: 200px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        font-size: 0.875rem;
    }

    .calendar-day {
        min-height: 60px;
    }

    .network-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .notification-panel-content {
        width: 100%;
        right: 0;
        border-radius: 0;
    }

    .search-mode-toggle {
        flex-direction: column;
    }

    .search-options {
        flex-direction: column;
        gap: 0.5rem;
    }

    .search-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* ===== FILE ATTACHMENT STYLES ===== */
.note-attachments-section {
    margin-bottom: 1rem;
}

.note-attachments-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.file-upload-area {
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 0.75rem 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-color);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 60px;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: var(--hover-bg);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(74, 107, 223, 0.1);
    transform: scale(1.01);
}

.file-upload-content {
    pointer-events: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-upload-content i {
    font-size: 1.25rem;
    color: var(--text-muted);
}

.file-upload-content p {
    margin: 0;
    color: var(--text-color);
    font-size: 0.9rem;
}

.file-upload-hint {
    font-size: 0.8rem;
    color: var(--text-muted) !important;
    margin-left: 0.5rem;
}

.attached-files {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.attached-file {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.attached-file:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 6px;
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.file-icon.image {
    background: #28a745;
}

.file-icon.pdf {
    background: #dc3545;
}

.file-icon.document {
    background: #007bff;
}

.file-icon.text {
    background: #6c757d;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.file-actions {
    display: flex;
    gap: 0.25rem;
}

.file-action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.file-action-btn:hover {
    color: var(--primary-color);
    background: var(--hover-bg);
}

.file-action-btn.delete:hover {
    color: var(--danger-color);
}

/* File Preview Modal */
.file-preview-modal .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
}

.file-preview-content {
    display: flex;
    flex-direction: column;
    height: 80vh;
}

.file-preview-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.file-preview-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-color);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.file-preview-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.file-preview-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.file-preview-container .preview-placeholder {
    text-align: center;
    color: var(--text-muted);
    padding: 2rem;
}

.file-preview-container .preview-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.file-info {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.file-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.file-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-info-label {
    font-weight: 500;
    color: var(--text-color);
}

.file-info-value {
    color: var(--text-muted);
    text-align: right;
}

.upload-progress {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.upload-progress-bar {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    width: 0%;
}

.file-upload-error {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--danger-color);
}

/* ===== SECURITY STYLES ===== */
.note-security-section {
    margin-bottom: 1rem;
}

.note-security-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.security-options {
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.security-options .checkbox-label {
    margin-bottom: 1rem;
}

.security-options .checkbox-label i {
    color: var(--warning-color);
}

.password-section {
    margin-left: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.password-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
    font-family: var(--font-family-bengali);
}

.password-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.toggle-password {
    margin-left: 0.5rem;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-muted);
}

.toggle-password:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    width: 0%;
}

.strength-fill.weak {
    background: var(--danger-color);
    width: 25%;
}

.strength-fill.fair {
    background: var(--warning-color);
    width: 50%;
}

.strength-fill.good {
    background: var(--info-color);
    width: 75%;
}

.strength-fill.strong {
    background: var(--success-color);
    width: 100%;
}

.strength-text {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Password Modal */
.password-modal .modal-content {
    max-width: 400px;
    width: 90%;
}

.password-verification {
    text-align: center;
    padding: 1rem 0;
}

.lock-icon {
    font-size: 3rem;
    color: var(--warning-color);
    margin-bottom: 1rem;
}

.password-verification p {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.password-error {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--danger-color);
    text-align: left;
}

.password-error i {
    margin-right: 0.5rem;
}

/* Backup Encryption Modal */
.backup-encryption-modal .modal-content {
    max-width: 500px;
}

.encryption-options {
    padding: 1rem;
}

.encryption-options .checkbox-label {
    margin-bottom: 1rem;
}

.encryption-options .checkbox-label i {
    color: var(--success-color);
}

.backup-password-section {
    margin-left: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.backup-password-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.encryption-note {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 6px;
    border-left: 3px solid var(--warning-color);
    font-size: 0.875rem;
    color: var(--text-color);
}

.encryption-note i {
    color: var(--warning-color);
    margin-right: 0.5rem;
}

/* Encrypted Note Indicator */
.note-card.encrypted {
    border-left: 4px solid var(--warning-color);
    position: relative;
}

.note-card.encrypted::before {
    content: '\f023';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    color: var(--warning-color);
    font-size: 0.875rem;
}

.note-card.encrypted .note-content {
    filter: blur(2px);
    transition: filter 0.3s ease;
}

.note-card.encrypted:hover .note-content {
    filter: blur(1px);
}

.encrypted-placeholder {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.encrypted-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    color: var(--warning-color);
}

.encrypted-placeholder p {
    margin-bottom: 1rem;
}

.unlock-btn {
    background: var(--warning-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.unlock-btn:hover {
    background: #e0a800;
}

/* ===== SHARING & EXPORT STYLES ===== */
.share-modal .modal-content,
.export-modal .modal-content {
    max-width: 600px;
    width: 90%;
}

.share-body,
.export-body {
    max-height: 70vh;
    overflow-y: auto;
}

.share-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.share-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.share-section h4 i {
    color: var(--primary-color);
}

.share-section p {
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.share-link-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.share-link-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
    font-family: monospace;
    font-size: 0.875rem;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

/* Custom Domain Section */
.custom-domain-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 1rem;
}

.custom-domain-section label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.custom-domain-input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
}

.custom-domain-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.2);
}

/* Share options checkboxes use global styling */

.share-expiry-section {
    margin-left: 2rem;
    padding-top: 0.5rem;
}

.share-expiry-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.expiry-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
}

.qr-code-container {
    text-align: center;
}

.qr-code {
    display: inline-block;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
}

.qr-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.social-share-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.social-btn.facebook {
    background: #1877f2;
}

.social-btn.twitter {
    background: #1da1f2;
}

.social-btn.whatsapp {
    background: #25d366;
}

.social-btn.telegram {
    background: #0088cc;
}

.social-btn.email {
    background: #ea4335;
}

/* Export Styles */
.export-formats h4 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.format-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.format-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.format-option:hover {
    border-color: var(--primary-color);
    background: var(--hover-bg);
}

.format-option.selected {
    border-color: var(--primary-color);
    background: rgba(74, 107, 223, 0.1);
}

.format-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 8px;
    font-size: 1.5rem;
}

.format-option.selected .format-icon {
    background: var(--primary-color);
}

.format-details h5 {
    margin: 0 0 0.25rem 0;
    color: var(--text-color);
}

.format-details p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.export-options {
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.export-options h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
}

.export-options .checkbox-label {
    margin-bottom: 0.5rem;
}

/* QR Code Styles */
.qr-code canvas,
.qr-code img {
    max-width: 200px;
    max-height: 200px;
}

/* Share Link Animation */
.share-link-input.copied {
    border-color: var(--success-color);
    background: rgba(40, 167, 69, 0.1);
}

.copy-feedback {
    position: absolute;
    top: -2rem;
    right: 0;
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    opacity: 0;
    animation: copyFeedback 2s ease-in-out;
}

@keyframes copyFeedback {
    0% { opacity: 0; transform: translateY(10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

/* ===== PASSWORD PROTECTION STYLES ===== */
.note-password-section {
    margin-bottom: 1.5rem;
}

.note-password-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.password-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
}

.password-toggle {
    margin-bottom: 1rem;
}

.password-input-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.password-field {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    background: var(--input-bg);
    color: var(--text-color);
    transition: border-color 0.3s ease;
}

.password-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.toggle-password {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-color);
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    background: #dc3545;
    width: 25%;
}

.strength-fill.fair {
    background: #fd7e14;
    width: 50%;
}

.strength-fill.good {
    background: #ffc107;
    width: 75%;
}

.strength-fill.strong {
    background: #28a745;
    width: 100%;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Password Modal Styles */
.password-modal .modal-content {
    max-width: 450px;
    width: 90%;
}

.password-content {
    text-align: center;
}

.password-body {
    padding: 2rem 1.5rem;
}

.password-unlock-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.lock-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
    animation: lockPulse 2s ease-in-out infinite;
}

@keyframes lockPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(74, 107, 223, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(74, 107, 223, 0);
    }
}

.unlock-password-field {
    position: relative;
    width: 100%;
    max-width: 300px;
}

.unlock-password-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--input-bg);
    color: var(--text-color);
    text-align: center;
    transition: border-color 0.3s ease;
}

.unlock-password-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.toggle-unlock-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-unlock-password:hover {
    color: var(--primary-color);
}

.password-error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid rgba(220, 53, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    max-width: 300px;
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.password-attempts {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid rgba(255, 193, 7, 0.3);
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
}

/* Encrypted Note Card Styles */
.note-card.encrypted {
    background: linear-gradient(135deg, rgba(74, 107, 223, 0.1), rgba(118, 75, 162, 0.1));
    border: 2px solid rgba(74, 107, 223, 0.3);
    position: relative;
    overflow: hidden;
}

.note-card.encrypted::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(74, 107, 223, 0.05) 10px,
        rgba(74, 107, 223, 0.05) 20px
    );
    pointer-events: none;
}

.note-card.encrypted .note-preview {
    filter: blur(2px);
    opacity: 0.7;
}

.note-card.encrypted .note-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.note-card.encrypted .note-title::before {
    content: '🔒';
    font-size: 1.2em;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .header {
        padding: 1rem 0;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .notes-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .modal-content {
        width: 95%;
        height: 90%;
        margin: 2.5% auto;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .search-filters {
        flex-wrap: wrap;
    }

    .calendar-grid {
        font-size: 0.8rem;
    }

    .social-share-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .format-options {
        grid-template-columns: 1fr;
    }

    .editor-tabs-container {
        flex-direction: column;
    }

    .vertical-tabs-sidebar {
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
    }

    .vertical-tabs {
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
    }

    .vertical-tab {
        min-width: 120px;
        margin-right: 0.5rem;
        margin-bottom: 0;
    }

    .tab-content-area {
        width: 100%;
    }

    .password-modal .modal-content {
        width: 95%;
        max-width: none;
    }

    .password-body {
        padding: 1.5rem 1rem;
    }

    .unlock-password-field {
        max-width: none;
    }
}

/* ===== CONTEXT MENU STYLES ===== */
.context-menu {
    position: fixed;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
    min-width: 220px;
    z-index: 10000;
    display: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    font-family: var(--font-family);
    font-size: 14px;
    user-select: none;
    animation: contextMenuFadeIn 0.2s ease-out;
    transition: opacity 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Add a subtle glow effect */
.context-menu {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1);
}

[data-theme="light"] .context-menu {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(0, 0, 0, 0.05);
}

.context-menu-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
    position: relative;
}

.context-menu-item:hover:not(.disabled) {
    background: var(--primary-color);
    color: white;
    transform: translateX(2px);
}

.context-menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.context-menu-item i {
    width: 16px;
    margin-right: 12px;
    text-align: center;
    font-size: 14px;
}

.context-menu-item span:first-of-type {
    flex: 1;
    margin-right: 20px;
}

.context-menu-item .shortcut {
    font-size: 12px;
    opacity: 0.7;
    font-family: 'Courier New', monospace;
}

.context-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 6px 0;
    opacity: 0.3;
}

/* Dark theme adjustments */
[data-theme="dark"] .context-menu {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .context-menu-item:hover:not(.disabled) {
    background: var(--primary-color);
}

[data-theme="dark"] .context-menu-divider {
    background: rgba(255, 255, 255, 0.1);
}

/* Light theme adjustments */
[data-theme="light"] .context-menu {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .context-menu-item:hover:not(.disabled) {
    background: var(--primary-color);
}

[data-theme="light"] .context-menu-divider {
    background: rgba(0, 0, 0, 0.1);
}

/* Responsive design for context menu */
@media (max-width: 768px) {
    .context-menu {
        min-width: 200px;
        font-size: 16px;
    }

    .context-menu-item {
        padding: 12px 16px;
    }

    .context-menu-item i {
        font-size: 16px;
    }

    .context-menu-item .shortcut {
        display: none; /* Hide shortcuts on mobile */
    }
}

/* ===== ENHANCED REMINDER SYSTEM STYLES ===== */

/* Reminder Modal */
.reminder-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10002;
    animation: fadeIn 0.3s ease-out;
}

.reminder-modal {
    background: var(--bg-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideInUp 0.3s ease-out;
}

.reminder-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
}

.reminder-modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.4rem;
    font-weight: 600;
}

.reminder-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.reminder-modal-close:hover {
    background: var(--hover-color);
    color: var(--text-color);
}

.reminder-modal-body {
    padding: 24px;
}

.reminder-modal-body h4 {
    margin: 0 0 12px 0;
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.reminder-modal-body p {
    margin: 0 0 20px 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

.reminder-modal-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.reminder-modal-actions .btn {
    flex: 1;
    min-width: 120px;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

/* Reminder Notification Panel */
.reminder-notification-panel {
    position: fixed;
    top: 80px;
    right: 0px;
    width: 350px;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-height: 500px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
}

.reminder-notification-panel.show {
    transform: translateX(0);
}

.reminder-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: #8c83d7;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    border-bottom: 1px solid var(--border-color);
}

.reminder-notification-header-title {
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reminder-notification-header-title i {
    font-size: 1.1rem;
    animation: pulse 2s infinite;
}

.reminder-notification-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reminder-notification-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.reminder-notification-body {
    max-height: 400px;
    overflow-y: auto;
}

.no-reminder-notifications {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.no-reminder-notifications i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.no-reminder-notifications p {
    margin: 0;
    font-size: 0.9rem;
}

.reminder-notification-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgb(153 217 242), rgb(222 193 105));
    border-left: 4px solid #ffc107;
    margin: 0;
}

.reminder-notification-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.reminder-notification-item:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.reminder-notification-item:hover {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
    transform: translateX(-2px);
}

.reminder-notification-icon {
    font-size: 28px;
    margin-right: 14px;
    flex-shrink: 0;
    animation: pulse 2s infinite;
}

.reminder-notification-content {
    flex: 1;
    min-width: 0;
}

.reminder-notification-title {
    font-weight: 400;
    color: var(--text-color);
    margin-bottom: 6px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.reminder-notification-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 8px;
}

.reminder-notification-time {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-weight: 500;
}

.reminder-notification-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    margin-left: 12px;
    flex-direction: column;
}

.reminder-notification-actions .btn {
    padding: 8px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
    font-weight: 500;
    min-width: 80px;
    text-align: center;
}

/* Reminder Indicators on Note Thumbnails */
.reminder-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 18px;
    z-index: 10;
    animation: pulse 2s infinite;
}

.reminder-indicator.reminder-today {
    animation: bounce 1s infinite;
}

.reminder-indicator.reminder-overdue {
    animation: shake 0.5s infinite;
}

.reminder-indicator.reminder-future {
    opacity: 0.8;
    animation: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .reminder-notification-panel {
        width: calc(100% - 40px) !important;
        right: 20px !important;
        left: 20px !important;
        top: 60px !important;
    }

    .reminder-modal {
        width: 95%;
        margin: 20px;
    }

    .reminder-modal-actions {
        flex-direction: column;
    }

    .reminder-modal-actions .btn {
        width: 100%;
        min-width: auto;
    }

    .reminder-notification-actions {
        flex-direction: column;
        gap: 6px;
        margin-left: 8px;
    }

    .reminder-notification-actions .btn {
        width: 100%;
        text-align: center;
        padding: 6px 8px;
        font-size: 0.8rem;
    }

    .reminder-notification-item {
        padding: 12px;
        flex-direction: column;
        align-items: stretch;
    }

    .reminder-notification-icon {
        font-size: 24px;
        margin-right: 0;
        margin-bottom: 8px;
        text-align: center;
    }

    .reminder-notification-content {
        margin-bottom: 12px;
    }
}

/* Dark Mode Support for Reminder Notifications */
[data-theme="dark"] .reminder-notification-panel {
    background: var(--dark-color);
    border-color: var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .reminder-notification-item {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 193, 7, 0.08));
    border-left-color: #ffc107;
}

[data-theme="dark"] .reminder-notification-item:hover {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
}

[data-theme="dark"] .reminder-notification-title {
    color: var(--text-color);
}

[data-theme="dark"] .reminder-notification-message {
    color: var(--text-secondary);
}

[data-theme="dark"] .reminder-notification-time {
    color: var(--text-muted);
}

[data-theme="dark"] .reminder-notification-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

[data-theme="dark"] .reminder-notification-close {
    background: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .reminder-notification-close:hover {
    background: rgba(255, 255, 255, 0.25);
}

/* ===== BACKGROUND CUSTOMIZATION STYLES ===== */

/* Background Type Toggle */
.background-type-toggle {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.bg-type-btn {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.bg-type-btn:hover {
    border-color: var(--primary-color);
    background: var(--hover-color);
}

.bg-type-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

/* Color Picker */
.color-picker-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.color-picker {
    width: 60px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    background: none;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 8px;
    max-width: 400px;
}

.preset-color {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.preset-color:hover {
    transform: scale(1.1);
    border-color: var(--primary-color);
}

.preset-color.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Gradient Presets */
.gradient-presets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
    max-width: 500px;
}

.gradient-preset {
    width: 80px;
    height: 50px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.gradient-preset:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.gradient-preset.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Pattern Presets */
.pattern-presets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    max-width: 400px;
}

.pattern-preset {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.pattern-preset:hover {
    border-color: var(--primary-color);
    background: var(--hover-color);
}

.pattern-preset.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.pattern-preview {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background-color: var(--text-secondary);
}

.pattern-dots {
    background-image: radial-gradient(circle, var(--text-color) 1px, transparent 1px);
    background-size: 8px 8px;
}

.pattern-grid {
    background-image:
        linear-gradient(var(--text-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--text-color) 1px, transparent 1px);
    background-size: 8px 8px;
}

.pattern-diagonal {
    background-image: repeating-linear-gradient(
        45deg,
        var(--text-color),
        var(--text-color) 1px,
        transparent 1px,
        transparent 8px
    );
}

.pattern-waves {
    background-image:
        radial-gradient(ellipse at top, var(--text-color), transparent),
        radial-gradient(ellipse at bottom, var(--text-color), transparent);
    background-size: 8px 4px;
    background-position: 0 0, 4px 2px;
}

.pattern-hexagon {
    background-image:
        linear-gradient(30deg, transparent 24%, var(--text-color) 25%, var(--text-color) 26%, transparent 27%, transparent 74%, var(--text-color) 75%, var(--text-color) 76%, transparent 77%, transparent),
        linear-gradient(-30deg, transparent 24%, var(--text-color) 25%, var(--text-color) 26%, transparent 27%, transparent 74%, var(--text-color) 75%, var(--text-color) 76%, transparent 77%, transparent);
    background-size: 12px 20px;
}

.pattern-circuit {
    background-color: #0a0a0a;
    background-image:
        linear-gradient(90deg, #00ff41 1px, transparent 1px),
        linear-gradient(180deg, #00ff41 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, #00ff41 1px, transparent 1px);
    background-size: 20px 20px, 20px 20px, 10px 10px;
    background-position: 0 0, 0 0, 5px 5px;
}

.pattern-matrix {
    background-color: #000000;
    background-image:
        linear-gradient(0deg, transparent 24%, #00ff00 25%, #00ff00 26%, transparent 27%, transparent 74%, #00ff00 75%, #00ff00 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, #00ff00 25%, #00ff00 26%, transparent 27%, transparent 74%, #00ff00 75%, #00ff00 76%, transparent 77%, transparent);
    background-size: 8px 8px;
}

.pattern-carbon {
    background-color: #1a1a1a;
    background-image:
        radial-gradient(circle at 25% 25%, #333 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, #333 2px, transparent 2px),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%);
    background-size: 8px 8px, 8px 8px, 4px 4px, 4px 4px;
    background-position: 0 0, 4px 4px, 0 0, 2px 2px;
}

.pattern-neural {
    background-color: #0d1117;
    background-image:
        radial-gradient(circle at 20% 20%, #4a9eff 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, #4a9eff 1px, transparent 1px),
        linear-gradient(45deg, transparent 40%, rgba(74, 158, 255, 0.1) 50%, transparent 60%);
    background-size: 15px 15px, 15px 15px, 30px 30px;
    background-position: 0 0, 7px 7px, 0 0;
}

.pattern-geometric {
    background-color: #1a1a1a;
    background-image:
        linear-gradient(45deg, #333 25%, transparent 25%),
        linear-gradient(-45deg, #333 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #333 75%),
        linear-gradient(-45deg, transparent 75%, #333 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

.pattern-constellation {
    background-color: #0a0a0a;
    background-image:
        radial-gradient(circle at 10% 20%, #ffffff 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, #ffffff 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, #ffffff 0.5px, transparent 0.5px),
        linear-gradient(45deg, transparent 48%, rgba(255, 255, 255, 0.1) 49%, rgba(255, 255, 255, 0.1) 51%, transparent 52%);
    background-size: 50px 50px, 30px 30px, 20px 20px, 100px 100px;
    background-position: 0 0, 25px 25px, 10px 10px, 0 0;
}

.pattern-cyberpunk {
    background-color: #000000;
    background-image:
        linear-gradient(90deg, #ff0080 1px, transparent 1px),
        linear-gradient(0deg, #00ffff 1px, transparent 1px),
        radial-gradient(circle at 25% 25%, #ff0080 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, #00ffff 2px, transparent 2px);
    background-size: 25px 25px, 25px 25px, 50px 50px, 50px 50px;
    background-position: 0 0, 0 0, 0 0, 25px 25px;
}

/* Background Actions */
.background-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 16px;
}

.background-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Mobile Responsive for Background Customization */
@media (max-width: 768px) {
    .background-type-toggle {
        flex-direction: column;
    }

    .bg-type-btn {
        width: 100%;
        justify-content: center;
    }

    .preset-colors {
        grid-template-columns: repeat(5, 1fr);
    }

    .gradient-presets {
        grid-template-columns: repeat(2, 1fr);
    }

    .pattern-presets {
        grid-template-columns: repeat(2, 1fr);
    }

    .background-actions {
        flex-direction: column;
    }

    .background-actions .btn {
        width: 100%;
        min-width: auto;
    }
}



/* Sound Selection Styles */
.sound-selection-container {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.sound-select {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sound-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.sound-select:hover {
    border-color: var(--primary-color);
}

.sound-select option {
    background: #777777;
    color: #fff;
    padding: 8px;
}

#previewSoundBtn {
    min-width: 80px;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .sound-selection-container {
        flex-direction: column;
        align-items: stretch;
    }

    .sound-select {
        min-width: auto;
        width: 100%;
    }

    #previewSoundBtn {
        width: 100%;
    }
}

/* ===== BOOKMARK SYSTEM STYLES ===== */

/* Bookmark Button in Header */
.btn-bookmark {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-bookmark::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-bookmark:hover::before {
    left: 100%;
}

.btn-bookmark:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.btn-bookmark i {
    animation: bookmarkPulse 2s infinite;
}

@keyframes bookmarkPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Search Tabs */
.search-tabs {
    display: flex;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
    background: var(--card-bg);
    border-radius: 8px 8px 0 0;
    overflow: hidden;
}

.search-tab {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.search-tab:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.search-tab.active {
    background: var(--primary-color);
    color: white;
}

.search-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
}

/* Search Tab Content */
.search-tab-content {
    display: none;
}

.search-tab-content.active {
    display: block;
}

/* Bookmark Search Container */
.bookmark-search-container {
    padding: 1rem 0;
}

/* Bookmark Filters Section */
.bookmark-filters-section {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
}

.bookmark-sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.bookmark-sort-options label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.bookmark-sort-select {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 13px;
}

.bookmark-quick-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.quick-filter {
    padding: 6px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 500;
}

.quick-filter:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.quick-filter.active {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.bookmark-advanced-options {
    display: flex;
    gap: 8px;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.bookmark-advanced-options .btn {
    font-size: 12px;
    padding: 6px 10px;
}

.bookmark-actions {
    display: flex;
    gap: 10px;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.bookmark-categories {
    display: flex;
    gap: 8px;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.category-filter {
    padding: 6px 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 500;
}

.category-filter:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.category-filter.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Bookmark Results */
.bookmark-results {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

/* Enhanced Bookmark Item */
.bookmark-item {
    position: relative;
}

.bookmark-item.favorite::before {
    content: '⭐';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 16px;
    z-index: 2;
}

.bookmark-visit-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    font-size: 11px;
    color: var(--text-muted);
}

.visit-count {
    background: var(--info-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
}

.last-visited {
    color: var(--text-secondary);
}

.bookmark-status {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.working {
    background: var(--success-color);
}

.status-indicator.broken {
    background: var(--danger-color);
}

.status-indicator.checking {
    background: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.bookmark-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.bookmark-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bookmark-favicon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border-radius: 4px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    flex-shrink: 0;
}

.bookmark-favicon img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

.bookmark-content {
    flex: 1;
    min-width: 0;
}

.bookmark-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-url {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.bookmark-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: var(--text-muted);
}

.bookmark-category {
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
}

.bookmark-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.bookmark-tag {
    background: var(--accent-color);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.bookmark-actions-menu {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bookmark-item:hover .bookmark-actions-menu {
    opacity: 1;
}

.bookmark-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--secondary-color);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.bookmark-action-btn:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.bookmark-action-btn.edit {
    background: var(--warning-color);
}

.bookmark-action-btn.delete {
    background: var(--danger-color);
}

.bookmark-action-btn.favorite {
    background: var(--warning-color);
}

.bookmark-action-btn.favorite.active {
    background: #ffc107;
    color: #212529;
}

.bookmark-action-btn.favorite.active i {
    color: #ffc107;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* Bookmark Stats */
.bookmark-stats {
    margin-top: 1rem;
    padding: 8px 12px;
    background: var(--card-bg);
    border-radius: 6px;
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
}

/* Bookmark Manager Modal */
.bookmark-modal .modal-content {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
}

/* Bookmark List Header Styles */
.bookmark-list-title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
    flex-wrap: wrap;
}

.bookmark-list-title-section h5 {
    margin: 0;
    flex-shrink: 0;
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 500;
}

.bookmark-list-search {
    flex: 1;
    max-width: 100%;
    min-width: 180px;
}

.bookmark-list-search .search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.bookmark-list-search .search-input-group input {
    width: 100%;
    padding: 6px 30px 6px 30px;
    border: 1px solid var(--border-color);
    border-radius: 18px;
    font-size: 13px;
    background: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.bookmark-list-search .search-input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.2);
}

.bookmark-list-search .search-icon {
    position: absolute;
    left: 10px;
    color: var(--text-muted);
    font-size: 12px;
    z-index: 1;
}

.bookmark-list-search .clear-search {
    position: absolute;
    right: 6px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 3px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 1;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
}

.bookmark-list-search .clear-search:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bookmark-list-title-section {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .bookmark-list-search {
        max-width: none;
        min-width: auto;
    }
}

.bookmark-modal.fullscreen .modal-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
    margin: 0;
}

.bookmark-modal.fullscreen .bookmark-manager-body {
    max-height: calc(100vh - 120px);
}

/* Compact Folder Management Section */
.folder-management-section-compact {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.folder-management-section-compact h4 {
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 16px;
}

.folder-actions-compact {
    display: flex;
    gap: 6px;
    margin-bottom: 0.75rem;
}

.folder-actions-compact .btn {
    font-size: 15px;
    padding: 6px 10px;
}

.folders-list-compact {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.folders-list-compact .folder-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.folders-list-compact .folder-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.folders-list-compact .folder-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    font-size: 14px;
}

.folders-list-compact .folder-description {
    font-size: 10px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.folders-list-compact .folder-count {
    font-size: 9px;
    color: var(--text-muted);
}

.folders-list-compact .folder-color {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* Folder Management Mode */
.folders-list-compact.managing .folder-item {
    position: relative;
    padding-bottom: 30px;
}

.folder-actions {
    position: absolute;
    bottom: 4px;
    left: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.folders-list-compact.managing .folder-item .folder-actions {
    opacity: 1;
}

.folder-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.folder-action-btn.edit {
    background: var(--warning-color);
    color: white;
}

.folder-action-btn.delete {
    background: var(--danger-color);
    color: white;
}

.folder-action-btn:hover {
    transform: scale(1.1);
}

.folder-empty-state {
    text-align: center;
    padding: 1rem;
    color: var(--text-secondary);
}

.folder-empty-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-muted);
}

.folder-empty-state p {
    margin: 0;
    font-size: 12px;
}

/* Compact Statistics */
.bookmark-statistics-compact {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    margin-bottom: 10px;
    margin-top: -20px;
}

.stat-item-compact {
    text-align: center;
    padding: 0px;
}

.stat-number-compact {
    display: block;
    font-size: 22px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: -5px;
}

.stat-label-compact {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Folder Management Section */
.folder-management-section {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.folder-management-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.folder-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
}

.folders-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.folder-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.folder-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.folder-item.selected {
    border-color: var(--primary-color);
    background: rgba(74, 107, 223, 0.1);
}

.folder-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.folder-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

.folder-count {
    font-size: 11px;
    color: var(--text-muted);
}

.folder-color {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Bookmark Statistics */
.bookmark-statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 1rem 0;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
    padding: 8px;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.bookmark-manager-body {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 1.5rem;
    max-height: 75vh;
    overflow: hidden;
}

/* Left Column Styles */
.bookmark-left-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
    padding-right: 8px;
}

/* Right Column Styles */
.bookmark-right-column {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Compact Form Styles */
.bookmark-form-section-compact {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.bookmark-form-section-compact h4 {
    margin-bottom: 0.75rem;
    color: #dc3545;
    font-weight: 500;
    font-size: 20px;
}

.bookmark-form-compact {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-row-compact {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.form-group-compact {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
	width: 180px;
}

.form-group-compact label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 16px;
}

.form-group-compact input,
.form-group-compact select,
.form-group-compact textarea {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group-compact input:focus,
.form-group-compact select:focus,
.form-group-compact textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1);
}

.form-group-compact textarea {
    resize: vertical;
    min-height: 60px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkbox-label-compact {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    color: var(--text-primary);
}

.checkbox-label-compact input[type="checkbox"] {
    display: none;
}

.checkmark-compact {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    margin-right: 6px;
    position: relative;
    transition: all 0.3s ease;
    background: var(--input-bg);
}

.checkbox-label-compact:hover .checkmark-compact {
    border-color: var(--primary-color);
}

.checkbox-label-compact input[type="checkbox"]:checked + .checkmark-compact {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label-compact input[type="checkbox"]:checked + .checkmark-compact::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.form-actions-compact {
    display: flex;
    gap: 8px;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.form-actions-compact .btn {
    font-size: 15px;
    padding: 6px 12px;
}

.bookmark-form-section {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.bookmark-form-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 500;
}

.bookmark-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 1rem;
    flex-wrap: wrap;
}

/* Bookmark List Section */
.bookmark-list-section {
    background: var(--card-bg);
    padding: 0.8rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.bookmark-list-header {
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.6rem;
    flex-wrap: wrap;
    gap: 0.6rem;
}

.bookmark-list-header h4 {
    color: var(--text-primary);
    font-weight: 500;
    margin: 0;
    font-size: 14px;
}

.bookmark-list-actions {
    display: flex;
    gap: 6px;
}

.bookmark-list {
    flex: 1;
    overflow-y: auto;
    max-height: 540px;
    padding-right: 6px;
}

.bookmark-list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.bookmark-list-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.bookmark-list-item.editing {
    border-color: var(--warning-color);
    background: rgba(255, 193, 7, 0.1);
}

.bookmark-list-item.favorite {
    border-left: 4px solid #ffc107;
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.05) 0%, transparent 100%);
}

.bookmark-list-item.favorite::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
    background: #ffc107;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: #212529;
    font-weight: bold;
    z-index: 1;
}

.bookmark-list-item.favorite::after {
    content: '★';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #212529;
    font-weight: bold;
    z-index: 2;
}

.bookmark-list-favicon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border-radius: 3px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    flex-shrink: 0;
}

.bookmark-list-content {
    flex: 1;
    min-width: 0;
}

.bookmark-list-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.favorite-star {
    color: #ffc107;
    font-size: 12px;
    animation: starTwinkle 2s infinite;
}

@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.bookmark-list-visits {
    background: var(--info-color);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    margin-left: 4px;
}

.bookmark-list-last-visit {
    color: var(--text-muted);
    font-size: 13px;
    margin-left: 4px;
}

.bookmark-list-status {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 4px;
    font-size: 13px;
}

.bookmark-list-url {
    font-size: 22px !important;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookmark-list-meta {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 4px;
}

.bookmark-list-category {
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 10px;
}

.bookmark-list-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bookmark-list-item:hover .bookmark-list-actions {
    opacity: 1;
}

.bookmark-list-action {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
}

.bookmark-list-action.edit {
    background: var(--warning-color);
    color: white;
}

.bookmark-list-action.delete {
    background: var(--danger-color);
    color: white;
}

.bookmark-list-action.favorite {
    background: var(--warning-color);
    color: white;
}

.bookmark-list-action.favorite.active {
    background: #ffc107;
    color: #212529;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
}

.bookmark-list-action.favorite.active i {
    animation: starPulse 1.5s infinite;
}

@keyframes starPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.bookmark-list-action.visit {
    background: var(--success-color);
    color: white;
}

.bookmark-list-action:hover {
    transform: scale(1.1);
}

/* Empty State for Bookmarks */
.bookmark-empty-state {
    text-align: center;
    padding: 1.2rem;
    color: var(--text-secondary);
}

.bookmark-empty-state i {
    font-size: 2rem;
    margin-bottom: 0.6rem;
    color: var(--text-muted);
}

.bookmark-empty-state h4 {
    margin-bottom: 0.3rem;
    color: var(--text-primary);
    font-size: 14px;
}

/* Responsive Design for Bookmark Manager */
@media (max-width: 768px) {
    .bookmark-manager-body {
        grid-template-columns: 1fr;
        gap: 1rem;
        max-height: 80vh;
    }

    .bookmark-left-column {
        order: 2;
    }

    .bookmark-right-column {
        order: 1;
    }

    .form-row-compact {
        grid-template-columns: 1fr;
    }

    .folders-list-compact {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .bookmark-statistics-compact {
        grid-template-columns: repeat(2, 1fr);
    }

    .bookmark-list-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .bookmark-actions,
    .bookmark-categories {
        justify-content: center;
    }

    .search-tabs {
        flex-direction: column;
    }

    .search-tab {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .bookmark-manager-body {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .bookmark-form-section-compact,
    .folder-management-section-compact {
        padding: 0.75rem;
    }

    .form-actions-compact {
        flex-direction: column;
    }

    .bookmark-statistics-compact {
        grid-template-columns: repeat(4, 1fr);
        gap: 4px;
        padding: 0.5rem;
    }

    .stat-number-compact {
        font-size: 14px;
    }

    .stat-label-compact {
        font-size: 9px;
    }
}

/* ===== DARK MODE BOOKMARK STYLES ===== */
[data-theme="dark"] .bookmark-filters-section {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .bookmark-sort-select {
    background: var(--input-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .bookmark-sort-select option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    padding: 8px !important;
}

[data-theme="dark"] .quick-filter {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-secondary);
}

[data-theme="dark"] .quick-filter:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

[data-theme="dark"] .quick-filter.active {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

[data-theme="dark"] .bookmark-item {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .bookmark-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

[data-theme="dark"] .bookmark-favicon {
    background: var(--primary-color);
}

[data-theme="dark"] .bookmark-title {
    color: var(--text-primary);
}

[data-theme="dark"] .bookmark-url {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-category {
    background: var(--primary-color);
}

[data-theme="dark"] .bookmark-tag {
    background: var(--accent-color);
}

[data-theme="dark"] .bookmark-action-btn {
    background: var(--secondary-color);
    color: white;
}

[data-theme="dark"] .bookmark-action-btn:hover {
    background: var(--primary-color);
}

[data-theme="dark"] .bookmark-action-btn.favorite.active {
    background: #ffc107;
    color: #212529;
}

[data-theme="dark"] .bookmark-stats {
    background: var(--card-bg);
    color: var(--text-secondary);
}

[data-theme="dark"] .folder-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .folder-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

[data-theme="dark"] .folder-name {
    color: var(--text-primary);
}

[data-theme="dark"] .folder-description {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-statistics {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .stat-number {
    color: var(--primary-color);
}

[data-theme="dark"] .stat-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-form-section {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
    background: var(--input-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group select:focus,
[data-theme="dark"] .form-group textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(74, 107, 223, 0.1) !important;
}

/* Select dropdown options in dark mode */
[data-theme="dark"] .form-group select option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: none !important;
}

[data-theme="dark"] .bookmark-sort-select option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    border: none !important;
}

/* Specific fix for bookmark category select */
[data-theme="dark"] #bookmarkCategory {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] #bookmarkCategory option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    padding: 8px !important;
}

[data-theme="dark"] #bookmarkFolder {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] #bookmarkFolder option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    padding: 8px !important;
}

[data-theme="dark"] .bookmark-list-section {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .bookmark-list-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .bookmark-list-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

[data-theme="dark"] .bookmark-list-title {
    color: var(--text-primary);
}

[data-theme="dark"] .bookmark-list-url {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-list-category {
    background: var(--primary-color);
}

[data-theme="dark"] .bookmark-empty-state {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-empty-state h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .bookmark-empty-state i {
    color: var(--text-muted);
}

/* Dark mode for compact elements */
[data-theme="dark"] .bookmark-form-section-compact {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-group-compact input,
[data-theme="dark"] .form-group-compact select,
[data-theme="dark"] .form-group-compact textarea {
    background: var(--input-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .form-group-compact input:focus,
[data-theme="dark"] .form-group-compact select:focus,
[data-theme="dark"] .form-group-compact textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(74, 107, 223, 0.1) !important;
}

[data-theme="dark"] .checkmark-compact {
    background: var(--input-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .checkbox-label-compact:hover .checkmark-compact {
    border-color: var(--primary-color);
}

[data-theme="dark"] .checkbox-label-compact input[type="checkbox"]:checked + .checkmark-compact {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

[data-theme="dark"] .folder-management-section-compact {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .folders-list-compact .folder-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .folders-list-compact .folder-item:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

[data-theme="dark"] .folders-list-compact .folder-name {
    color: var(--text-primary);
}

[data-theme="dark"] .folders-list-compact .folder-description {
    color: var(--text-secondary);
}

[data-theme="dark"] .bookmark-statistics-compact {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .stat-number-compact {
    color: var(--primary-color);
}

[data-theme="dark"] .stat-label-compact {
    color: var(--text-secondary);
}

/* Dark mode for folder management */
[data-theme="dark"] .folder-action-btn.edit {
    background: var(--warning-color);
    color: white;
}

[data-theme="dark"] .folder-action-btn.delete {
    background: var(--danger-color);
    color: white;
}

[data-theme="dark"] .folder-empty-state {
    color: var(--text-secondary);
}

[data-theme="dark"] .folder-empty-state i {
    color: var(--text-muted);
}

/* Dark mode for favorite indicators */
[data-theme="dark"] .bookmark-list-item.favorite {
    border-left: 4px solid #ffc107;
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.1) 0%, transparent 100%);
}

[data-theme="dark"] .favorite-star {
    color: #ffc107;
}

[data-theme="dark"] .bookmark-list-visits {
    background: var(--info-color);
    color: white;
}

[data-theme="dark"] .bookmark-list-last-visit {
    color: var(--text-muted);
}

[data-theme="dark"] .bookmark-list-action.favorite {
    background: var(--warning-color);
    color: white;
}

[data-theme="dark"] .bookmark-list-action.favorite.active {
    background: #ffc107;
    color: #212529;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.3);
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-primary);
    margin-top: 8px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
    background: var(--input-bg);
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

[data-theme="dark"] .checkmark {
    background: var(--input-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
}

[data-theme="dark"] .checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Global select styling for dark mode */
[data-theme="dark"] select {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] select option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
[data-theme="dark"] select::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] select::-webkit-scrollbar-track {
    background: var(--card-bg);
}

[data-theme="dark"] select::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] select::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Firefox */
[data-theme="dark"] select {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--card-bg);
}

/* Force all bookmark related selects */
[data-theme="dark"] .bookmark-modal select,
[data-theme="dark"] .bookmark-search-container select,
[data-theme="dark"] .bookmark-filters-section select {
    background: var(--input-bg) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

[data-theme="dark"] .bookmark-modal select option,
[data-theme="dark"] .bookmark-search-container select option,
[data-theme="dark"] .bookmark-filters-section select option {
    background: var(--card-bg) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px !important;
}

/* Very specific targeting for problematic selects */
[data-theme="dark"] select#bookmarkCategory,
[data-theme="dark"] select#bookmarkFolder,
[data-theme="dark"] select#bookmarkSortSelect {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
    border: 1px solid #4a5568 !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

[data-theme="dark"] select#bookmarkCategory option,
[data-theme="dark"] select#bookmarkFolder option,
[data-theme="dark"] select#bookmarkSortSelect option {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
    padding: 8px 12px !important;
}

/* Additional fallback for all selects in dark mode */
[data-theme="dark"] select,
[data-theme="dark"] select * {
    color: #e2e8f0 !important;
}

/* Custom dropdown arrow for better visibility */
[data-theme="dark"] select {
    background-repeat: no-repeat !important;
    background-position: right 8px center !important;
    background-size: 16px !important;
    padding-right: 32px !important;
}

/* ===== NOTIFICATION PERMISSION MODAL ===== */
.notification-permission-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.permission-dialog {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 24px;
    max-width: 480px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: permissionSlideIn 0.3s ease-out;
}

@keyframes permissionSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.permission-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.permission-header i {
    font-size: 24px;
    color: var(--primary-color);
}

.permission-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.4rem;
    font-weight: 600;
}

.permission-body p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 16px;
}

.permission-benefits {
    margin: 20px 0;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    color: var(--text-color);
}

.benefit-item:last-child {
    margin-bottom: 0;
}

.benefit-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.permission-note {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-style: italic;
}

.permission-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    flex-wrap: wrap;
}

.permission-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

.permission-actions .btn-link {
    flex: none;
    background: none;
    border: none;
    color: var(--text-muted);
    text-decoration: underline;
    padding: 8px 12px;
    font-size: 0.9rem;
}

.permission-actions .btn-link:hover {
    color: var(--text-color);
    background: var(--bg-hover);
}

/* Notification Status Indicator */
.notification-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.notification-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-granted {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-granted::before {
    background: #27ae60;
}

.status-denied {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-denied::before {
    background: #e74c3c;
}

.status-default {
    background: rgba(241, 196, 15, 0.1);
    color: #f1c40f;
}

.status-default::before {
    background: #f1c40f;
}

.status-unsupported {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

.status-unsupported::before {
    background: #95a5a6;
}

/* Notification Settings Styles */
.permission-status-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.notification-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.notification-actions .btn {
    flex: 1;
    min-width: 140px;
}

.notification-help {
    background: var(--bg-secondary);
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.notification-help p {
    margin: 8px 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-help p:last-child {
    margin-bottom: 0;
}

.notification-help i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

/* Bulk Download Button Styles */
#bulkDownloadBtn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#bulkDownloadBtn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#bulkDownloadBtn:active {
    transform: translateY(0);
}

#bulkDownloadBtn i {
    animation: downloadPulse 2s infinite;
}

@keyframes downloadPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Loading state for bulk download */
#bulkDownloadBtn.loading {
    pointer-events: none;
    opacity: 0.7;
}

#bulkDownloadBtn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Download Progress Modal Styles */
.download-progress-content {
    max-width: 500px;
    width: 90%;
}

.download-progress-info {
    padding: 20px 0;
}

.progress-text {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 500;
}

.progress-bar-container {
    position: relative;
    margin-bottom: 25px;
}

.progress-bar-bg {
    width: 100%;
    height: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 6px;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.download-details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
}

.detail-item {
    text-align: center;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.detail-label {
    display: block;
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.detail-value {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Mobile responsive for download progress */
@media (max-width: 768px) {
    .download-details {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .detail-item {
        padding: 10px;
    }

    .download-progress-content {
        width: 95%;
        margin: 10px;
    }
}