<!DOCTYPE html>
<html lang="bn" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <link rel="icon" href="assets/icon.png" type="image/x-icon">
    <title>আমার স্মার্ট নোট</title>

    <!-- Jodit Editor CSS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@4.2.27/es2021/jodit.min.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Backup Font Awesome CDN -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css" crossorigin="anonymous">

    <!-- Additional backup CDN -->
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-rqn26AG5Pj86AF4SO72RK5fyefcQ/x32DNQfChxWvbXIyXFePlEktwD18fEz+kQU" crossorigin="anonymous">

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

    <!-- JSZip Library for creating zip files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- Google Fonts - Kalpurush for Bengali -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kalpurush:wght@400&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Fullscreen Loader -->
    <div class="fullscreen-loader" id="fullscreenLoader">
        <div class="loader-container">
            <div class="loader-animation">
                <div class="loader-circle">
                    <div class="loader-inner-circle"></div>
                </div>
                <div class="loader-dots">
                    <div class="dot dot-1"></div>
                    <div class="dot dot-2"></div>
                    <div class="dot dot-3"></div>
                </div>
            </div>
            <div class="loader-text">
                <h2>আমার স্মার্ট নোট</h2>
                <p>লোড হচ্ছে...</p>
                <div class="loader-progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
            </div>
        </div>
        <div class="loader-background-animation">
            <div class="floating-note floating-note-1">📝</div>
            <div class="floating-note floating-note-2">📚</div>
            <div class="floating-note floating-note-3">💡</div>
            <div class="floating-note floating-note-4">✨</div>
            <div class="floating-note floating-note-5">📋</div>
            <div class="floating-note floating-note-6">🎯</div>
        </div>
    </div>

    <!-- Header -->
    <header class="header" id="header">
    <div class="container">
        <div class="header-content">
            <h1 class="logo">
                <i class="fas fa-sticky-note" style="font-size: 1.5rem !important;"></i>
                <span class="bengali-text" style="font-size: 1.5rem !important;">আমার স্মার্ট নোট</span>
                <span class="header-divider">|</span>
                <span class="english-text" style="font-size: 1.5rem !important;">MD Fahim Haque</span>
            </h1>
        </div>
        <div class="header-actions">
            <button class="btn btn-compact btn-dashboard" id="dashboardBtn" title="ড্যাশবোর্ড">
                <i class="fas fa-chart-bar"></i>
            </button>

            <button class="btn btn-compact btn-search" id="searchBtn" title="সার্চ">
                <i class="fas fa-search"></i>
            </button>
            <button class="btn btn-compact btn-bookmark" id="bookmarkBtn" title="বুকমার্ক ম্যানেজার">
                <i class="fas fa-bookmark"></i>
            </button>
            <button class="btn btn-compact btn-calendar" id="calendarViewBtn" title="ক্যালেন্ডার">
                <i class="fas fa-calendar"></i>
            </button>
            <button class="btn btn-compact btn-settings" id="settingsBtn" title="সেটিংস">
                <i class="fas fa-cog"></i>
            </button>
            <button class="btn btn-compact btn-help" id="helpBtn" title="সাহায্য ও শর্টকাট">
                <i class="fas fa-question-circle"></i>
            </button>
            <button class="btn btn-compact btn-drag" id="toggleDragBtn" title="ড্র্যাগ মোড চালু/বন্ধ">
                <i class="fas fa-arrows-alt"></i>
            </button>
            <button class="btn btn-compact btn-reset" id="resetOrderBtn" title="নোটের ক্রম রিসেট করুন" style="display: none;">
                <i class="fas fa-undo"></i>
            </button>

            <button class="btn btn-compact btn-warning" id="testReminderBtn" title="রিমাইন্ডার টেস্ট করুন">
                <i class="fas fa-clock"></i>
            </button>
            <button class="btn btn-compact btn-success" id="bulkDownloadBtn" title="সব নোট ডাউনলোড করুন">
                <i class="fas fa-download"></i>
            </button>
            <div class="sort-container">
                <select id="sortSelect" class="sort-select bengali-text" title="নোট সাজানোর ধরন">
                    <option value="updated" selected class="bengali-text">সর্বশেষ আপডেট</option>
                    <option value="priority" class="bengali-text">অগ্রাধিকার ভিত্তিক</option>
                    <option value="created" class="bengali-text">তৈরির তারিখ (নতুন)</option>
                    <option value="alphabetical" class="bengali-text">বর্ণানুক্রমিক (A-Z)</option>
                    <option value="reverse-alphabetical" class="bengali-text">বিপরীত বর্ণানুক্রমিক (Z-A)</option>
                    <option value="oldest" class="bengali-text">পুরাতন আগে</option>
                </select>
            </div>
            <div class="notification-bell-container">
                <button class="btn btn-icon notification-bell" id="notificationBell">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationBadge">0</span>
                </button>
            </div>
            <button class="btn btn-primary" id="addNoteBtn" title="নতুন নোট তৈরি করুন">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    </div>
</header>



<!-- Main Content -->
<main class="main" id="main">
    <div class="container">
        <!-- Notes Grid -->
        <div class="notes-grid" id="notesGrid">
            <!-- Notes will be dynamically added here -->
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState">
            <div class="empty-icon">
                <i class="fas fa-sticky-note"></i>
            </div>
            <h2 class="bengali-text">কোন নোট নেই</h2>
            <p class="bengali-text">আপনার প্রথম নোট তৈরি করতে "নতুন নোট" বাটনে ক্লিক করুন</p>
            <button class="btn btn-primary bengali-text" onclick="createNewNote()">
                <i class="fas fa-plus"></i>
                নতুন নোট তৈরি করুন
            </button>
        </div>
    </div>
</main>

<!-- Note Modal -->
<div class="modal" id="noteModal">
    <div class="modal-overlay" onclick="closeNoteModal()"></div>
    <div class="modal-content">

        <div class="modal-header">
            <h3 id="modalTitle" class="bengali-text">নোট সম্পাদনা</h3>
            <div class="modal-actions">
                <button class="btn btn-icon btn-danger" id="deleteNoteBtn" title="নোট ডিলেট করুন" style="display: none;">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-icon" id="autoSaveBtn" title="অটো সেভ চালু করুন">
                    <i class="fas fa-save"></i>
                </button>
                <button class="btn btn-icon" id="insertDateBtn" title="তারিখ যুক্ত করুন">
                    <i class="fas fa-calendar"></i>
                </button>
                <button class="btn btn-icon" id="insertTimeBtn" title="সময় যুক্ত করুন">
                    <i class="fas fa-clock"></i>
                </button>
                <button class="btn btn-icon" id="insertDateTimeBtn" title="পূর্ণ তারিখ ও সময় যুক্ত করুন">
                    <i class="fas fa-calendar-alt"></i>
                </button>
                <button class="btn btn-icon" id="insertEmojiBtn" title="ইমোজি যুক্ত করুন">
                    <i class="fas fa-smile"></i>
                </button>
                <button class="btn btn-icon" id="fullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" id="resetModalSizeBtn" title="সাইজ রিসেট করুন" onclick="resetModalSize('noteModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeNoteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body">
            <!-- Editor Section -->
            <div class="editor-section-compact">
                <!-- Tabs Header (Horizontal) -->
                <div class="tabs-header-compact">
                    <button class="tab-scroll-btn tab-scroll-left" id="tabScrollLeft" title="বামে স্ক্রল করুন">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="tabs-nav-container">
                        <div class="tabs-nav-compact" id="tabsNavCompact">
                            <!-- Tabs will be dynamically added here -->
                        </div>
                    </div>
                    <button class="tab-scroll-btn tab-scroll-right" id="tabScrollRight" title="ডানে স্ক্রল করুন">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <div class="tabs-actions-compact">
                        <button class="btn btn-icon-sm add-tab-btn-compact" id="addTabBtn" title="নতুন ট্যাব">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Active Tab Content -->
                <div class="tab-content-compact">
                    <div class="active-tab-header-compact">
                        <input type="text" id="activeTabTitle" class="tab-title-input-compact bengali-text" placeholder="ট্যাবের শিরোনাম...">
                        <button class="btn btn-icon-sm delete-tab-btn-compact" id="deleteTabBtn" title="ট্যাব ডিলেট">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="note-editor-container-compact">
                        <textarea id="noteEditor" class="bengali-text" placeholder="আপনার নোট লিখুন..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Compact Top Section -->
            <div class="note-meta-section">
                <div class="note-title-wrapper">
                    <input type="text" id="noteTitle" class="note-title-input-compact bengali-text" placeholder="নোটের শিরোনাম লিখুন...">
                </div>
                <div class="note-controls-row">
                    <div class="control-group">
                        <select id="notePriority" class="priority-select-compact bengali-text">
                            <option value="low" class="bengali-text">🟢 কম</option>
                            <option value="medium" selected class="bengali-text">🟡 মাধ্যম</option>
                            <option value="high" class="bengali-text">🟠 উচ্চ</option>
                            <option value="urgent" class="bengali-text">🔴 জরুরি</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <div class="reminder-container-compact">
                            <input type="datetime-local" id="noteReminder" class="reminder-input-compact" title="রিমাইন্ডার সেট করুন">
                            <button type="button" class="btn btn-icon-sm clear-reminder" id="clearReminderBtn" title="রিমাইন্ডার মুছুন">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="control-group">
                        <button type="button" class="btn btn-icon-sm toggle-advanced" id="toggleAdvancedBtn" title="উন্নত অপশন">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button type="button" class="btn btn-icon-sm" id="testPasswordBtn" title="পাসওয়ার্ড টেস্ট" onclick="debugPasswordProtection()" style="background: #ff6b6b; color: white;">
                            <i class="fas fa-lock"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Options (Collapsible) -->
            <div class="advanced-options" id="advancedOptions" style="display: block;">
                <div class="advanced-section">
                    <!-- Password Protection Section -->
                    <div class="note-password-section-compact">
                        <div class="password-container-compact">
                            <div class="password-toggle-compact">
                                <label class="checkbox-label-compact">
                                    <input type="checkbox" id="enablePassword" checked>
                                    <span class="checkmark-compact"></span>
                                    <span class="password-label">পাসওয়ার্ড সুরক্ষা</span>
                                </label>
                            </div>
                            <div class="password-input-group-compact" id="passwordInputGroup" style="display: flex;">
                                <div class="password-field-compact">
                                    <input type="password" id="notePassword" class="password-input-compact" placeholder="পাসওয়ার্ড">
                                    <button type="button" class="btn btn-icon-sm toggle-password" id="togglePasswordBtn" title="পাসওয়ার্ড দেখান/লুকান" onclick="togglePasswordVisibility('notePassword', 'togglePasswordBtn')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-field-compact">
                                    <input type="password" id="confirmPassword" class="password-input-compact" placeholder="নিশ্চিত করুন">
                                    <button type="button" class="btn btn-icon-sm toggle-password" id="toggleConfirmPasswordBtn" title="পাসওয়ার্ড দেখান/লুকান" onclick="togglePasswordVisibility('confirmPassword', 'toggleConfirmPasswordBtn')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength-compact" id="passwordStrength">
                                    <div class="strength-bar-compact">
                                        <div class="strength-fill" id="strengthFill"></div>
                                    </div>
                                    <span class="strength-text-compact" id="strengthText">পাসওয়ার্ড শক্তি</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Attachments Section -->
                    <div class="note-attachments-section-compact">
                        <div class="file-upload-area-compact" id="fileUploadArea">
                            <div class="file-upload-content-compact">
                                <i class="fas fa-paperclip"></i>
                                <span>ফাইল যুক্ত করুন</span>
                            </div>
                            <input type="file" id="fileInput" multiple accept="image/*,.pdf,.doc,.docx,.txt,.rtf,.csv,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z,audio/*,video/*" style="display: none;">
                        </div>
                        <div class="attached-files-compact" id="attachedFiles">
                            <!-- Attached files will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary bengali-text" onclick="closeNoteModal()">
                <i class="fas fa-times"></i>
                বাতিল
            </button>
            <button class="btn btn-info bengali-text" id="printCurrentNoteBtn" style="display: none;">
                <i class="fas fa-print"></i>
                প্রিন্ট
            </button>
            <button class="btn btn-warning" id="pdfCurrentNoteBtn" style="display: none;">
                <i class="fas fa-file-pdf"></i>
                PDF
            </button>
            <button class="btn btn-success bengali-text" id="shareNoteBtn" style="display: none;">
                <i class="fas fa-share-alt"></i>
                শেয়ার
            </button>
            <button class="btn btn-info bengali-text" id="exportNoteBtn" style="display: none;">
                <i class="fas fa-download"></i>
                এক্সপোর্ট
            </button>
            <button class="btn btn-secondary bengali-text" id="downloadTxtBtn" style="display: none;">
                <i class="fas fa-file-download"></i>
                TXT ডাউনলোড
            </button>
            <button class="btn btn-primary bengali-text" id="saveNoteBtn">
                <i class="fas fa-save"></i>
                সংরক্ষণ
            </button>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal" id="searchModal">
    <div class="modal-overlay" onclick="closeSearchModal()"></div>
    <div class="modal-content">

        <div class="modal-header">
            <h3 class="bengali-text">সার্চ ও বুকমার্ক</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="searchFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('searchModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeSearchModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body">
            <!-- Search Tabs -->
            <div class="search-tabs">
                <button class="search-tab active" data-tab="notes">
                    <i class="fas fa-sticky-note"></i>
                    নোট সার্চ
                </button>
                <button class="search-tab" data-tab="bookmarks">
                    <i class="fas fa-bookmark"></i>
                    বুকমার্ক সার্চ
                </button>
            </div>

            <!-- Notes Search Tab -->
            <div class="search-tab-content active" id="notesSearchTab">
                <div class="search-container">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="searchInput" class="bengali-text" placeholder="নোট খুঁজুন..." autocomplete="off">
                        <button class="btn btn-icon clear-search" id="clearSearchBtn" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                <!-- Advanced Search Options -->
                <div class="advanced-search-options">
                    <div class="search-mode-toggle">
                        <button class="search-mode-btn active" data-mode="normal">
                            <i class="fas fa-search"></i>
                            সাধারণ সার্চ
                        </button>
                        <button class="search-mode-btn" data-mode="regex">
                            <i class="fas fa-code"></i>
                            রেগেক্স সার্চ
                        </button>
                        <button class="search-mode-btn" data-mode="fulltext">
                            <i class="fas fa-file-text"></i>
                            ফুল-টেক্সট সার্চ
                        </button>
                    </div>

                    <div class="search-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="caseSensitiveSearch">
                            <span class="checkmark"></span>
                            কেস সেনসিটিভ
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="wholeWordSearch">
                            <span class="checkmark"></span>
                            পূর্ণ শব্দ
                        </label>

                    </div>
                </div>

                <div class="search-filters">
                    <button class="filter-btn active" data-filter="all">সব</button>
                    <button class="filter-btn" data-filter="today">আজকের</button>
                    <button class="filter-btn" data-filter="week">এই সপ্তাহের</button>
                    <button class="filter-btn" data-filter="month">এই মাসের</button>
                    <button class="filter-btn" data-filter="priority-high">উচ্চ অগ্রাধিকার</button>
                    <button class="filter-btn" data-filter="priority-urgent">জরুরি</button>
                </div>


            </div>

                <div class="search-results" id="searchResults">
                    <!-- Search results will appear here -->
                </div>

                <!-- Search Statistics -->
                <div class="search-stats" id="searchStats" style="display: none;">
                    <span id="searchResultCount">0 টি ফলাফল</span>
                    <span id="searchTime">0ms এ</span>
                </div>
            </div>

            <!-- Bookmarks Search Tab -->
            <div class="search-tab-content" id="bookmarksSearchTab">
                <div class="bookmark-search-container">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="bookmarkSearchInput" class="bengali-text" placeholder="বুকমার্ক খুঁজুন..." autocomplete="off">
                        <button class="btn btn-icon clear-search" id="clearBookmarkSearchBtn" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="bookmark-actions">
                        <button class="btn btn-primary" id="addBookmarkBtn">
                            <i class="fas fa-plus"></i>
                            নতুন বুকমার্ক
                        </button>
                        <button class="btn btn-secondary" id="manageBookmarksBtn">
                            <i class="fas fa-cog"></i>
                            ম্যানেজ করুন
                        </button>
                    </div>

                    <div class="bookmark-filters-section">
                        <!-- Sort Options -->
                        <div class="bookmark-sort-options">
                            <label>সাজানো:</label>
                            <select id="bookmarkSortSelect" class="bookmark-sort-select">
                                <option value="recent">সাম্প্রতিক</option>
                                <option value="popular">জনপ্রিয়</option>
                                <option value="alphabetical">বর্ণানুক্রমিক</option>
                                <option value="category">ক্যাটেগরি অনুযায়ী</option>
                                <option value="lastVisited">শেষ ভিজিট</option>
                                <option value="visitCount">ভিজিট সংখ্যা</option>
                            </select>
                        </div>

                        <!-- Quick Filters -->
                        <div class="bookmark-quick-filters">
                            <button class="quick-filter active" data-filter="all">সব</button>
                            <button class="quick-filter" data-filter="favorites">ফেভরিট</button>
                            <button class="quick-filter" data-filter="popular">জনপ্রিয়</button>
                            <button class="quick-filter" data-filter="recent">সাম্প্রতিক</button>
                            <button class="quick-filter" data-filter="recentlyVisited">সম্প্রতি ভিজিট</button>
                        </div>

                        <!-- Category Filters -->
                        <div class="bookmark-categories">
                            <button class="category-filter active" data-category="all">সব</button>
                            <button class="category-filter" data-category="work">কাজ</button>
                            <button class="category-filter" data-category="education">শিক্ষা</button>
                            <button class="category-filter" data-category="entertainment">বিনোদন</button>
                            <button class="category-filter" data-category="social">সামাজিক</button>
                            <button class="category-filter" data-category="tools">টুলস</button>
                            <button class="category-filter" data-category="news">সংবাদ</button>
                            <button class="category-filter" data-category="other">অন্যান্য</button>
                        </div>

                        <!-- Advanced Options -->
                        <div class="bookmark-advanced-options">
                            <button class="btn btn-sm btn-secondary" id="checkBrokenLinksBtn">
                                <i class="fas fa-link"></i>
                                ব্রোকেন লিংক চেক
                            </button>
                            <button class="btn btn-sm btn-warning" id="findDuplicatesBtn">
                                <i class="fas fa-copy"></i>
                                ডুপ্লিকেট খুঁজুন
                            </button>
                            <button class="btn btn-sm btn-info" id="updateMetadataBtn">
                                <i class="fas fa-sync"></i>
                                মেটাডেটা আপডেট
                            </button>
                        </div>
                    </div>

                    <div class="bookmark-results" id="bookmarkResults">
                        <!-- Bookmark results will appear here -->
                    </div>

                    <div class="bookmark-stats" id="bookmarkStats" style="display: none;">
                        <span id="bookmarkResultCount">0 টি বুকমার্ক</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bookmark Manager Modal -->
<div class="modal bookmark-modal" id="bookmarkModal">
    <div class="modal-overlay" onclick="closeBookmarkModal()"></div>
    <div class="modal-content bookmark-content">

        <div class="modal-header">
            <h3>বুকমার্ক ম্যানেজার</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="bookmarkFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('bookmarkModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeBookmarkModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body bookmark-manager-body">
            <!-- Left Column: Form and Folder Management -->
            <div class="bookmark-left-column">
                <!-- Compact Bookmark Form -->
                <div class="bookmark-form-section-compact">
                    <h4>বুকমার্ক যোগ/সম্পাদনা</h4>
                    <form id="bookmarkForm" class="bookmark-form-compact">
                        <div class="form-row-compact">
                            <div class="form-group-compact">
                                <label for="bookmarkTitle">শিরোনাম *</label>
                                <input type="text" id="bookmarkTitle" class="bengali-text" placeholder="বুকমার্কের শিরোনাম..." required>
                            </div>
                            <div class="form-group-compact">
                                <label for="bookmarkUrl">URL *</label>
                                <input type="url" id="bookmarkUrl" placeholder="https://example.com" required>
                            </div>
                        </div>
                        <div class="form-row-compact">
                            <div class="form-group-compact">
                                <label for="bookmarkCategory">ক্যাটেগরি</label>
                                <select id="bookmarkCategory" class="bengali-text">
                                    <option value="work">কাজ</option>
                                    <option value="education">শিক্ষা</option>
                                    <option value="entertainment">বিনোদন</option>
                                    <option value="social">সামাজিক</option>
                                    <option value="tools">টুলস</option>
                                    <option value="news">সংবাদ</option>
                                    <option value="other">অন্যান্য</option>
                                </select>
                            </div>
                            <div class="form-group-compact">
                                <label for="bookmarkFolder">ফোল্ডার</label>
                                <select id="bookmarkFolder" class="bengali-text">
                                    <option value="">কোন ফোল্ডার নেই</option>
                                    <!-- Folders will be populated dynamically -->
                                </select>
                            </div>
                        </div>
                        <div class="form-row-compact">
                            <div class="form-group-compact">
                                <label for="bookmarkTags">ট্যাগ</label>
                                <input type="text" id="bookmarkTags" class="bengali-text" placeholder="ট্যাগ1, ট্যাগ2...">
                            </div>
                            <div class="form-group-compact checkbox-group">
                                <label class="checkbox-label-compact">
                                    <input type="checkbox" id="bookmarkFavorite">
                                    <span class="checkmark-compact"></span>
                                    ফেভরিট
                                </label>
                            </div>
                        </div>
                        <div class="form-group-compact">
                            <label for="bookmarkDescription">বিবরণ</label>
                            <textarea id="bookmarkDescription" class="bengali-text" placeholder="বুকমার্কের বিবরণ..." rows="2"></textarea>
                        </div>
                        <div class="form-actions-compact">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-save"></i>
                                সংরক্ষণ
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" id="cancelBookmarkBtn">
                                <i class="fas fa-times"></i>
                                বাতিল
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" id="deleteBookmarkBtn" style="display: none;">
                                <i class="fas fa-trash"></i>
                                মুছুন
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Folder Management -->
                <div class="folder-management-section-compact">
                    <h4>ফোল্ডার ম্যানেজমেন্ট</h4>
                    <div class="folder-actions-compact">
                        <button class="btn btn-sm btn-primary" id="addFolderBtn">
                            <i class="fas fa-folder-plus"></i>
                            নতুন ফোল্ডার
                        </button>
                        <button class="btn btn-sm btn-secondary" id="manageFoldersBtn">
                            <i class="fas fa-cog"></i>
                            সম্পাদনা
                        </button>
                    </div>
                    <div class="folders-list-compact" id="foldersList">
                        <!-- Folders will be displayed here -->
                    </div>
                </div>
            </div>

            <!-- Right Column: Bookmark List -->
            <div class="bookmark-right-column">
                <div class="bookmark-list-section">
                    <div class="bookmark-list-header">
                        <div class="bookmark-list-title-section">
                            <h5>সংরক্ষিত বুকমার্ক</h5>
                            <div class="bookmark-list-search">
                                <div class="search-input-group">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="bookmarkListSearchInput" class="bengali-text" placeholder="বুকমার্ক খুঁজুন..." autocomplete="off">
                                    <button class="btn btn-icon clear-search" id="clearBookmarkListSearchBtn" style="display: none;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <input type="file" id="importBookmarksFile" accept=".json" style="display: none;">
                    </div>

                    <!-- Bookmark Statistics -->
                    <div class="bookmark-statistics-compact">
                        <div class="stat-item-compact">
                            <span class="stat-number-compact" id="totalBookmarksCount">0</span>
                            <span class="stat-label-compact">মোট</span>
                        </div>
                        <div class="stat-item-compact">
                            <span class="stat-number-compact" id="favoriteBookmarksCount">0</span>
                            <span class="stat-label-compact">ফেভরিট</span>
                        </div>
                        <div class="stat-item-compact">
                            <span class="stat-number-compact" id="totalVisitsCount">0</span>
                            <span class="stat-label-compact">ভিজিট</span>
                        </div>
                        <div class="stat-item-compact">
                            <span class="stat-number-compact" id="brokenLinksCount">0</span>
                            <span class="stat-label-compact">ব্রোকেন</span>
                        </div>
                    </div>

                    <div class="bookmark-list" id="bookmarkList">
                        <!-- Bookmarks will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Download Progress Modal -->
<div class="modal" id="downloadProgressModal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content download-progress-content">
        <div class="modal-header">
            <h3><i class="fas fa-download"></i> নোট ডাউনলোড করা হচ্ছে</h3>
        </div>
        <div class="modal-body">
            <div class="download-progress-info">
                <div class="progress-text">
                    <span id="downloadProgressText">প্রস্তুত করা হচ্ছে...</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-bg">
                        <div class="progress-bar-fill" id="downloadProgressBar"></div>
                    </div>
                    <div class="progress-percentage" id="downloadProgressPercentage">0%</div>
                </div>
                <div class="download-details">
                    <div class="detail-item">
                        <span class="detail-label">মোট নোট:</span>
                        <span class="detail-value" id="totalNotesCount">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">প্রক্রিয়াকৃত:</span>
                        <span class="detail-value" id="processedNotesCount">0</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">ফাইলের আকার:</span>
                        <span class="detail-value" id="fileSizeEstimate">গণনা করা হচ্ছে...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Emoji Picker Modal -->
<div class="modal emoji-modal" id="emojiModal">
    <div class="modal-overlay" onclick="closeEmojiModal()"></div>
    <div class="modal-content emoji-picker">
        <div class="modal-header">
            <h3>ইমোজি নির্বাচন করুন</h3>
            <button class="btn btn-icon modal-close" onclick="closeEmojiModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="emoji-categories">
                <button class="emoji-category active" data-category="smileys">😀</button>
                <button class="emoji-category" data-category="people">👤</button>
                <button class="emoji-category" data-category="nature">🌿</button>
                <button class="emoji-category" data-category="food">🍎</button>
                <button class="emoji-category" data-category="activities">⚽</button>
                <button class="emoji-category" data-category="travel">🚗</button>
                <button class="emoji-category" data-category="objects">💡</button>
                <button class="emoji-category" data-category="symbols">❤️</button>
            </div>
            <div class="emoji-grid" id="emojiGrid">
                <!-- Emojis will be loaded here -->
            </div>
        </div>
    </div>
</div>



<!-- Settings Modal -->
<div class="modal settings-modal" id="settingsModal">
    <div class="modal-overlay" onclick="closeSettingsModal()"></div>
    <div class="modal-content settings-content">

        <div class="modal-header">
            <h3>সেটিংস ও কাস্টমাইজেশন</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="settingsFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('settingsModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeSettingsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body settings-body">
            <!-- Background Color Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-paint-brush"></i> ব্যাকগ্রাউন্ড কাস্টমাইজেশন</h4>

                <!-- Background Type Selection -->
                <div class="setting-group">
                    <label>ব্যাকগ্রাউন্ড টাইপ:</label>
                    <div class="background-type-toggle">
                        <button class="bg-type-btn active" data-type="solid">
                            <i class="fas fa-square"></i>
                            সলিড কালার
                        </button>
                        <button class="bg-type-btn" data-type="gradient">
                            <i class="fas fa-palette"></i>
                            গ্রেডিয়েন্ট
                        </button>
                        <button class="bg-type-btn" data-type="pattern">
                            <i class="fas fa-th"></i>
                            প্যাটার্ন
                        </button>
                    </div>
                </div>

                <!-- Solid Color Options -->
                <div class="setting-group" id="solidColorOptions">
                    <label>ব্যাকগ্রাউন্ড কালার:</label>
                    <div class="color-picker-container">
                        <input type="color" id="backgroundColorPicker" value="#1a1a1a" class="color-picker">
                        <div class="preset-colors">
                            <div class="preset-color" data-color="#1a1a1a" style="background: #1a1a1a;" title="ডার্ক গ্রে"></div>
                            <div class="preset-color" data-color="#ffffff" style="background: #ffffff;" title="সাদা"></div>
                            <div class="preset-color" data-color="#000000" style="background: #000000;" title="কালো"></div>
                            <div class="preset-color" data-color="#2c3e50" style="background: #2c3e50;" title="নেভি ব্লু"></div>
                            <div class="preset-color" data-color="#34495e" style="background: #34495e;" title="স্লেট গ্রে"></div>
                            <div class="preset-color" data-color="#27ae60" style="background: #27ae60;" title="সবুজ"></div>
                            <div class="preset-color" data-color="#8e44ad" style="background: #8e44ad;" title="বেগুনি"></div>
                            <div class="preset-color" data-color="#e74c3c" style="background: #e74c3c;" title="লাল"></div>
                            <div class="preset-color" data-color="#f39c12" style="background: #f39c12;" title="কমলা"></div>
                            <div class="preset-color" data-color="#3498db" style="background: #3498db;" title="নীল"></div>
                        </div>
                    </div>
                </div>

                <!-- Gradient Options -->
                <div class="setting-group" id="gradientOptions" style="display: none;">
                    <label>গ্রেডিয়েন্ট সিলেকশন:</label>
                    <div class="gradient-presets">
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" title="নীল-বেগুনি"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);" title="গোলাপি-লাল"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);" title="নীল-সায়ান"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);" title="সবুজ-টিল"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #fa709a 0%, #fee140 100%)" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);" title="গোলাপি-হলুদ"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);" title="মিন্ট-গোলাপি"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);" title="কোরাল-গোলাপি"></div>
                        <div class="gradient-preset" data-gradient="linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);" title="পিচ-কমলা"></div>
                    </div>
                </div>

                <!-- Pattern Options -->
                <div class="setting-group" id="patternOptions" style="display: none;">
                    <label>প্যাটার্ন সিলেকশন:</label>
                    <div class="pattern-presets">
                        <div class="pattern-preset" data-pattern="dots" title="ডট প্যাটার্ন">
                            <div class="pattern-preview pattern-dots"></div>
                            <span>ডট</span>
                        </div>
                        <div class="pattern-preset" data-pattern="grid" title="গ্রিড প্যাটার্ন">
                            <div class="pattern-preview pattern-grid"></div>
                            <span>গ্রিড</span>
                        </div>
                        <div class="pattern-preset" data-pattern="diagonal" title="ডায়াগোনাল প্যাটার্ন">
                            <div class="pattern-preview pattern-diagonal"></div>
                            <span>ডায়াগোনাল</span>
                        </div>
                        <div class="pattern-preset" data-pattern="waves" title="ওয়েভ প্যাটার্ন">
                            <div class="pattern-preview pattern-waves"></div>
                            <span>ওয়েভ</span>
                        </div>
                        <div class="pattern-preset" data-pattern="hexagon" title="হেক্সাগন প্যাটার্ন">
                            <div class="pattern-preview pattern-hexagon"></div>
                            <span>হেক্সাগন</span>
                        </div>
                        <div class="pattern-preset" data-pattern="circuit" title="সার্কিট প্যাটার্ন">
                            <div class="pattern-preview pattern-circuit"></div>
                            <span>সার্কিট</span>
                        </div>
                        <div class="pattern-preset" data-pattern="matrix" title="ম্যাট্রিক্স প্যাটার্ন">
                            <div class="pattern-preview pattern-matrix"></div>
                            <span>ম্যাট্রিক্স</span>
                        </div>
                        <div class="pattern-preset" data-pattern="carbon" title="কার্বন ফাইবার প্যাটার্ন">
                            <div class="pattern-preview pattern-carbon"></div>
                            <span>কার্বন</span>
                        </div>
                        <div class="pattern-preset" data-pattern="neural" title="নিউরাল নেটওয়ার্ক প্যাটার্ন">
                            <div class="pattern-preview pattern-neural"></div>
                            <span>নিউরাল</span>
                        </div>
                        <div class="pattern-preset" data-pattern="geometric" title="জ্যামিতিক প্যাটার্ন">
                            <div class="pattern-preview pattern-geometric"></div>
                            <span>জ্যামিতিক</span>
                        </div>
                        <div class="pattern-preset" data-pattern="constellation" title="নক্ষত্রমণ্ডল প্যাটার্ন">
                            <div class="pattern-preview pattern-constellation"></div>
                            <span>নক্ষত্র</span>
                        </div>
                        <div class="pattern-preset" data-pattern="cyberpunk" title="সাইবারপাঙ্ক প্যাটার্ন">
                            <div class="pattern-preview pattern-cyberpunk"></div>
                            <span>সাইবারপাঙ্ক</span>
                        </div>
                    </div>
                </div>

                <!-- Background Actions -->
                <div class="setting-group">
                    <div class="background-actions">
                        <button class="btn btn-primary" id="applyBackgroundBtn">
                            <i class="fas fa-check"></i>
                            প্রয়োগ করুন
                        </button>
                        <button class="btn btn-secondary" id="previewBackgroundBtn">
                            <i class="fas fa-eye"></i>
                            প্রিভিউ
                        </button>
                        <button class="btn btn-warning" id="resetBackgroundBtn">
                            <i class="fas fa-undo"></i>
                            রিসেট
                        </button>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-bell"></i> নোটিফিকেশন সেটিংস</h4>

                <!-- Permission Status -->
                <div class="setting-group">
                    <label>অনুমতির অবস্থা:</label>
                    <div class="permission-status-container">
                        <span class="notification-status" id="notificationStatus">চেক করা হচ্ছে...</span>
                        <button class="btn btn-sm btn-primary" id="requestPermissionBtn" style="display: none;">
                            <i class="fas fa-bell"></i>
                            অনুমতি চান
                        </button>
                        <button class="btn btn-sm btn-secondary" id="openBrowserSettingsBtn" style="display: none;">
                            <i class="fas fa-cog"></i>
                            ব্রাউজার সেটিংস
                        </button>
                    </div>
                </div>

                <!-- Notification Controls -->
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="notificationEnabled" checked>
                        <span class="checkmark"></span>
                        নোটিফিকেশন চালু করুন
                    </label>
                </div>

                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="notificationSound" checked>
                        <span class="checkmark"></span>
                        নোটিফিকেশন সাউন্ড
                    </label>
                </div>

                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="reminderNotifications" checked>
                        <span class="checkmark"></span>
                        রিমাইন্ডার নোটিফিকেশন
                    </label>
                </div>

                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoSaveNotifications">
                        <span class="checkmark"></span>
                        অটো-সেভ নোটিফিকেশন
                    </label>
                </div>

                <!-- Notification Actions -->
                <div class="setting-group">
                    <div class="notification-actions">
                        <button class="btn btn-info btn-sm" id="testNotificationBtn">
                            <i class="fas fa-bell"></i>
                            টেস্ট নোটিফিকেশন
                        </button>
                        <button class="btn btn-warning btn-sm" id="resetNotificationSettingsBtn">
                            <i class="fas fa-undo"></i>
                            রিসেট করুন
                        </button>
                    </div>
                </div>

                <!-- Help Text -->
                <div class="setting-group">
                    <div class="notification-help">
                        <p><i class="fas fa-info-circle"></i> নোটিফিকেশন ব্রাউজার বন্ধ থাকলেও কাজ করবে।</p>
                        <p><i class="fas fa-shield-alt"></i> আপনি যেকোনো সময় ব্রাউজার সেটিংস থেকে এটি পরিবর্তন করতে পারবেন।</p>
                    </div>
                </div>
            </div>

            <!-- Theme Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-palette"></i> থিম সেটিংস</h4>
                <div class="setting-group">
                    <label>থিম মোড:</label>
                    <div class="theme-toggle">
                        <button class="theme-btn" data-theme="light">
                            <i class="fas fa-sun"></i>
                            লাইট মোড
                        </button>
                        <button class="theme-btn active" data-theme="dark">
                            <i class="fas fa-moon"></i>
                            ডার্ক মোড
                        </button>
                        <button class="theme-btn" data-theme="auto">
                            <i class="fas fa-adjust"></i>
                            অটো
                        </button>
                    </div>
                </div>
                <div class="setting-group">
                    <label>কালার থিম:</label>
                    <div class="color-themes">
                        <div class="color-theme active" data-color="blue">
                            <div class="color-preview" style="background: #4a6bdf;"></div>
                            <span>নীল</span>
                        </div>
                        <div class="color-theme" data-color="green">
                            <div class="color-preview" style="background: #28a745;"></div>
                            <span>সবুজ</span>
                        </div>
                        <div class="color-theme" data-color="purple">
                            <div class="color-preview" style="background: #6f42c1;"></div>
                            <span>বেগুনি</span>
                        </div>
                        <div class="color-theme" data-color="orange">
                            <div class="color-preview" style="background: #fd7e14;"></div>
                            <span>কমলা</span>
                        </div>
                        <div class="color-theme" data-color="red">
                            <div class="color-preview" style="background: #dc3545;"></div>
                            <span>লাল</span>
                        </div>
                        <div class="color-theme" data-color="teal">
                            <div class="color-preview" style="background: #1A1A1A;"></div>
                            <span>টিল</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Font Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-font"></i> ফন্ট সেটিংস</h4>
                <div class="setting-group">
                    <label for="fontSizeSlider">ফন্ট সাইজ: <span id="fontSizeValue">18px</span></label>
                    <input type="range" id="fontSizeSlider" min="14" max="24" value="18" class="slider">
                </div>
                <div class="setting-group">
                    <label for="fontFamilySelect">ফন্ট পরিবার:</label>
                    <select id="fontFamilySelect" class="font-select bengali-text">
                        <option value="Kalpurush" class="bengali-text">কালপুরুষ (ডিফল্ট)</option>
                        <option value="SolaimanLipi" class="bengali-text">সোলাইমান লিপি</option>
                        <option value="Nikosh" class="bengali-text">নিকোশ</option>
                        <option value="Mukti" class="bengali-text">মুক্তি</option>
                        <option value="Courier New">Courier New (ইংরেজি - ডিফল্ট)</option>
                        <option value="Roboto">Roboto (ইংরেজি)</option>
                        <option value="Open Sans">Open Sans (ইংরেজি)</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="fontWeightSelect">ফন্ট ওজন:</label>
                    <select id="fontWeightSelect" class="font-select">
                        <option value="300">হালকা</option>
                        <option value="400" selected>নরমাল</option>
                        <option value="500">মিডিয়াম</option>
                        <option value="600">সেমি-বোল্ড</option>
                        <option value="700">বোল্ড</option>
                    </select>
                </div>
            </div>

            <!-- Editor Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-edit"></i> এডিটর সেটিংস</h4>
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="autoSaveToggle" checked>
                        <span class="checkmark"></span>
                        অটো সেভ চালু রাখুন
                    </label>
                </div>
                <div class="setting-group">
                    <label for="autoSaveInterval">অটো সেভ ইন্টারভাল (সেকেন্ড):</label>
                    <input type="number" id="autoSaveInterval" min="0" max="300" value="0" class="number-input">
                    <small class="setting-help">০ = রিয়েল টাইম সেভ (তাৎক্ষণিক)</small>
                </div>
                <div class="setting-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="spellCheckToggle" checked>
                        <span class="checkmark"></span>
                        বানান পরীক্ষা চালু রাখুন
                    </label>
                </div>
                <div class="setting-group">
                    <label for="notificationVolumeSlider">নোটিফিকেশন সাউন্ড ভলিউম: <span id="notificationVolumeValue">100%</span></label>
                    <input type="range" id="notificationVolumeSlider" min="0" max="100" value="100" class="slider">
                </div>
                <div class="setting-group">
                    <button class="btn btn-secondary" id="testNotificationSoundBtn">
                        <i class="fas fa-volume-up"></i>
                        সাউন্ড টেস্ট করুন
                    </button>
                </div>

                <div class="setting-group">
                    <label>নোটিফিকেশন সাউন্ড:</label>
                    <div class="sound-selection-container">
                        <select id="notificationSoundSelect" class="sound-select">
                            <option value="default">ডিফল্ট সাউন্ড</option>
                            <option value="bell">বেল সাউন্ড</option>
                            <option value="chime">চাইম সাউন্ড</option>
                            <option value="ding">ডিং সাউন্ড</option>
                            <option value="notification">নোটিফিকেশন সাউন্ড</option>
                            <option value="alert">অ্যালার্ট সাউন্ড</option>
                            <option value="soft">সফট সাউন্ড</option>
                            <option value="beep">বিপ সাউন্ড</option>
                            <option value="tone">টোন সাউন্ড</option>
                        </select>
                        <button class="btn btn-sm btn-secondary" id="previewSoundBtn">
                            <i class="fas fa-play"></i>
                            প্রিভিউ
                        </button>
                    </div>
                    <small class="setting-help">আপনার পছন্দের নোটিফিকেশন সাউন্ড নির্বাচন করুন</small>
                </div>
            </div>



            <!-- Reset Settings -->
            <div class="settings-section">
                <h4><i class="fas fa-undo"></i> রিসেট</h4>
                <div class="setting-group">
                    <button class="btn btn-warning" id="resetSettingsBtn">
                        <i class="fas fa-undo"></i>
                        ডিফল্ট সেটিংস পুনরুদ্ধার
                    </button>
                </div>
            </div>

            <!-- Important Settings -->
            <div class="settings-section important-settings">
                <h4><i class="fas fa-exclamation-triangle"></i> গুরুত্বপূর্ণ সেটিংস</h4>
                <div class="important-settings-grid">
                    <!-- Storage Path Setting -->
                    <div class="setting-group storage-path-setting">
                        <div class="storage-path-container">
                            <div class="storage-path-header">
                                <i class="fas fa-folder-open"></i>
                                <span>সংরক্ষণ পাথ সেটিং</span>
                            </div>
                            <div class="storage-path-input-group">
                                <input type="text" id="storagePathInput" class="storage-path-input" placeholder="আপনার পছন্দের সংরক্ষণ পাথ লিখুন">
                                <button class="btn btn-primary btn-sm" id="updateStoragePathBtn">
                                    <i class="fas fa-save"></i>
                                    আপডেট
                                </button>
                            </div>
                            <div class="current-path-display">
                                <small>বর্তমান পাথ: <span id="currentStoragePath">F:\Desktop\WEB WORLD\01_website\fahim_web_site_directory_2.0\notes</span></small>
                                <button class="btn btn-icon btn-sm copy-current-path-btn" id="copyCurrentPathBtn" title="বর্তমান পাথ কপি করুন">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <button class="btn btn-secondary btn-important" id="loadSampleBtn">
                            <i class="fas fa-download"></i>
                            <span>স্যাম্পল ডাটা লোড করুন</span>
                            <small>ডেমো নোট ও বুকমার্ক যোগ করুন</small>
                        </button>
                    </div>
                    <div class="setting-group">
                        <button class="btn btn-info btn-important" id="backupBtn">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>ব্যাকআপ তৈরি করুন</span>
                            <small>আপনার সব ডাটা সংরক্ষণ করুন</small>
                        </button>
                    </div>
                    <div class="setting-group">
                        <button class="btn btn-warning btn-important" id="restoreBtn">
                            <i class="fas fa-cloud-download-alt"></i>
                            <span>ব্যাকআপ পুনরুদ্ধার করুন</span>
                            <small>পূর্বের ব্যাকআপ থেকে ডাটা ফিরিয়ে আনুন</small>
                        </button>
                    </div>
                    <div class="setting-group danger-zone">
                        <button class="btn btn-danger btn-important btn-danger-highlight" id="clearSampleBtn">
                            <i class="fas fa-trash-alt"></i>
                            <span>সব ডাটা মুছে ফেলুন</span>
                            <small>⚠️ সতর্কতা: এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Calendar View Modal -->
<div class="modal calendar-modal" id="calendarModal">
    <div class="modal-overlay" onclick="closeCalendarModal()"></div>
    <div class="modal-content calendar-content">

        <div class="modal-header">
            <h3>ক্যালেন্ডার ভিউ</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="calendarFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('calendarModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeCalendarModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body calendar-body">
            <div class="calendar-controls">
                <button class="btn btn-icon" id="prevMonthBtn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <h4 id="currentMonthYear">জুলাই ২০২৫</h4>
                <button class="btn btn-icon" id="nextMonthBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <button class="btn btn-secondary" id="todayBtn">
                    <i class="fas fa-calendar-day"></i>
                    আজ
                </button>
            </div>
            <div class="calendar-grid" id="calendarGrid">
                <!-- Calendar will be generated here -->
            </div>
            <div class="calendar-legend">
                <div class="legend-item">
                    <span class="legend-color has-notes"></span>
                    <span>নোট আছে</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color has-reminder"></span>
                    <span>রিমাইন্ডার আছে</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color today"></span>
                    <span>আজ</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Modal -->
<div class="modal dashboard-modal" id="dashboardModal">
    <div class="modal-overlay" onclick="closeDashboardModal()"></div>
    <div class="modal-content dashboard-content">

        <div class="modal-header">
            <h3>ড্যাশবোর্ড ও অ্যানালিটিক্স</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="dashboardFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>

                <button class="btn btn-icon modal-close" onclick="closeDashboardModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body dashboard-body">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-sticky-note"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="totalNotesCount">0</h4>
                        <p>মোট নোট</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="todayNotesCount">0</h4>
                        <p>আজকের নোট</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="weekNotesCount">0</h4>
                        <p>এই সপ্তাহের নোট</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h4 id="urgentNotesCount">0</h4>
                        <p>জরুরি নোট</p>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-grid">
                <div class="chart-container">
                    <h4>সাপ্তাহিক ট্রেন্ড</h4>
                    <canvas id="weeklyTrendChart"></canvas>
                </div>
                <div class="chart-container">
                    <h4>অগ্রাধিকার ভিত্তিক বিতরণ</h4>
                    <canvas id="priorityChart"></canvas>
                </div>
                <div class="chart-container">
                    <h4>মাসিক অগ্রগতি</h4>
                    <canvas id="monthlyChart"></canvas>
                </div>
                <div class="chart-container">
                    <h4>নোট দৈর্ঘ্য বিশ্লেষণ</h4>
                    <canvas id="lengthChart"></canvas>
                </div>
                <div class="chart-container">
                    <h4>দৈনিক কার্যকলাপ</h4>
                    <canvas id="dailyActivityChart"></canvas>
                </div>
                <div class="chart-container">
                    <h4>নোট ক্যাটাগরি</h4>
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <h4>সাম্প্রতিক কার্যকলাপ</h4>
                <div class="activity-list" id="activityList">
                    <!-- Activity items will be added here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Panel -->
<div class="notification-panel" id="notificationPanel">
    <div class="notification-panel-overlay" onclick="closeNotificationPanel()"></div>
    <div class="notification-panel-content">
        <div class="notification-panel-header">
            <h3>নোটিফিকেশন</h3>
            <div class="notification-panel-actions">
                <button class="btn btn-icon" id="clearAllNotifications" title="সব ক্লিয়ার করুন">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-icon" onclick="closeNotificationPanel()" title="বন্ধ করুন">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="notification-panel-body" id="notificationPanelBody">
            <div class="no-notifications">
                <i class="fas fa-bell-slash"></i>
                <p>কোন নোটিফিকেশন নেই</p>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification Container -->
<div class="notification-container" id="notificationContainer">
    <!-- Toast notifications will appear here -->
</div>

<!-- Reminder Notification Panel -->
<div class="reminder-notification-panel" id="reminderNotificationPanel">
    <div class="reminder-notification-header">
        <div class="reminder-notification-header-title">
            <i class="fas fa-bell"></i>
            রিমাইন্ডার নোটিফিকেশন
        </div>
        <button class="reminder-notification-close" onclick="closeAllReminderNotifications()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="reminder-notification-body" id="reminderNotificationBody">
        <div class="no-reminder-notifications">
            <i class="fas fa-bell-slash"></i>
            <p>কোন রিমাইন্ডার নোটিফিকেশন নেই</p>
        </div>
        <!-- Reminder notifications will appear here -->
    </div>
</div>

<!-- Reminder Modal Container -->
<div id="reminderModalContainer">
    <!-- Reminder modals will be dynamically created here -->
</div>

<!-- Scroll Buttons -->
<div class="scroll-buttons">
    <button class="scroll-btn scroll-to-top" id="scrollToTop" title="উপরে যান">
        <i class="fas fa-chevron-up"></i>
    </button>
    <button class="scroll-btn scroll-to-bottom" id="scrollToBottom" title="নিচে যান">
        <i class="fas fa-chevron-down"></i>
    </button>
</div>



<!-- Animated Footer -->
<footer class="animated-footer" id="footer">
    <div class="footer-background">
        <div class="footer-wave wave-1"></div>
        <div class="footer-wave wave-2"></div>
        <div class="footer-wave wave-3"></div>
    </div>

    <div class="footer-content">
        <div class="footer-particles">
            <div class="particle particle-1">💡</div>
            <div class="particle particle-2">📝</div>
            <div class="particle particle-3">✨</div>
            <div class="particle particle-4">📚</div>
            <div class="particle particle-5">🎯</div>
            <div class="particle particle-6">💫</div>
        </div>

        <div class="footer-main">
            <div class="footer-logo">
                <i class="fas fa-sticky-note footer-icon"></i>
                <h3 class="footer-title">© ২০২৫ নোট অ্যাপ</h3>
            </div>

            <div class="footer-text">
                <p class="footer-description">
                    <span class="footer-name">Smart Note System MD Fahim Haque</span><br>
                    <span class="footer-name">Website : <a href="https://mdfahimhaque.com" target="_blank" style="color: #28a745;">mdfahimhaque.com</a></span>
                </p>
            </div>

            <div class="footer-social">
                <div class="social-link" title="GitHub">
                    <i class="fab fa-github"></i>
                </div>
                <div class="social-link" title="LinkedIn">
                    <i class="fab fa-linkedin-in"></i>
                </div>
                <div class="social-link" title="Twitter">
                    <i class="fab fa-twitter"></i>
                </div>
                <div class="social-link" title="Email">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="social-link" title="Facebook">
                    <i class="fab fa-facebook-f"></i>
                </div>
                <div class="social-link" title="Instagram">
                    <i class="fab fa-instagram"></i>
                </div>
            </div>

            <div class="footer-copyright">
                <p>&copy; 2025 Smart Note System. All rights reserved.</p>
            </div>
        </div>
    </div>

    <div class="footer-glow"></div>
</footer>

<!-- File Preview Modal -->
<div class="modal file-preview-modal" id="filePreviewModal">
    <div class="modal-overlay" onclick="closeFilePreviewModal()"></div>
    <div class="modal-content file-preview-content">

        <div class="modal-header">
            <h3 id="filePreviewTitle">ফাইল প্রিভিউ</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="downloadFileBtn" title="ডাউনলোড করুন">
                    <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-icon" id="filePreviewFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('filePreviewModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeFilePreviewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body file-preview-body">
            <div class="file-preview-container" id="filePreviewContainer">
                <!-- File preview will be displayed here -->
            </div>
            <div class="file-info" id="fileInfo">
                <!-- File information will be displayed here -->
            </div>
        </div>
    </div>
</div>





<!-- Share Note Modal -->
<div class="modal share-modal" id="shareModal">
    <div class="modal-overlay" onclick="closeShareModal()"></div>
    <div class="modal-content share-content">

        <div class="modal-header">
            <h3>নোট শেয়ার করুন</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="shareFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('shareModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body share-body">
            <!-- Share Link Section -->
            <div class="share-section">
                <h4><i class="fas fa-link"></i> শেয়ার লিংক</h4>
                <p>এই লিংক দিয়ে অন্যরা আপনার নোট দেখতে পারবে:</p>
                <div class="share-link-container">
                    <input type="text" id="shareLink" readonly class="share-link-input">
                    <button class="btn btn-primary" id="copyShareLinkBtn">
                        <i class="fas fa-copy"></i>
                        কপি করুন
                    </button>
                </div>
                <div class="share-options">
                    <div class="custom-domain-section">
                        <label for="customDomainInput">কাস্টম ডোমেইন (ঐচ্ছিক):</label>
                        <input type="url" id="customDomainInput" placeholder="https://your-domain.com" class="custom-domain-input">
                        <button class="btn btn-sm btn-secondary" id="updateDomainBtn">আপডেট করুন</button>
                    </div>
                    <label class="checkbox-label">
                        <input type="checkbox" id="shareReadOnly" checked>
                        <span class="checkmark"></span>
                        শুধুমাত্র পড়ার জন্য
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="shareExpiry">
                        <span class="checkmark"></span>
                        মেয়াদ নির্ধারণ করুন
                    </label>
                </div>
                <div class="share-expiry-section" id="shareExpirySection" style="display: none;">
                    <label for="shareExpiryDate">মেয়াদ শেষ:</label>
                    <input type="datetime-local" id="shareExpiryDate" class="expiry-input">
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="share-section">
                <h4><i class="fas fa-qrcode"></i> QR কোড</h4>
                <p>মোবাইল ডিভাইসে সহজে শেয়ার করতে QR কোড স্ক্যান করুন:</p>
                <div class="qr-code-container">
                    <div class="qr-code" id="qrCode">
                        <!-- QR code will be generated here -->
                    </div>
                    <div class="qr-actions">
                        <button class="btn btn-secondary" id="downloadQRBtn">
                            <i class="fas fa-download"></i>
                            QR কোড ডাউনলোড
                        </button>
                        <button class="btn btn-secondary" id="printQRBtn">
                            <i class="fas fa-print"></i>
                            QR কোড প্রিন্ট
                        </button>
                    </div>
                </div>
            </div>

            <!-- Social Share Section -->
            <div class="share-section">
                <h4><i class="fas fa-share-alt"></i> সোশ্যাল মিডিয়া</h4>
                <div class="social-share-buttons">
                    <button class="social-btn facebook" id="shareFacebookBtn">
                        <i class="fab fa-facebook-f"></i>
                        Facebook
                    </button>
                    <button class="social-btn twitter" id="shareTwitterBtn">
                        <i class="fab fa-twitter"></i>
                        Twitter
                    </button>
                    <button class="social-btn whatsapp" id="shareWhatsAppBtn">
                        <i class="fab fa-whatsapp"></i>
                        WhatsApp
                    </button>
                    <button class="social-btn telegram" id="shareTelegramBtn">
                        <i class="fab fa-telegram"></i>
                        Telegram
                    </button>
                    <button class="social-btn email" id="shareEmailBtn">
                        <i class="fas fa-envelope"></i>
                        Email
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal export-modal" id="exportModal">
    <div class="modal-overlay" onclick="closeExportModal()"></div>
    <div class="modal-content export-content">

        <div class="modal-header">
            <h3>নোট এক্সপোর্ট করুন</h3>
            <div class="modal-actions">

                <button class="btn btn-icon modal-close" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body export-body">
            <div class="export-formats">
                <h4>এক্সপোর্ট ফরম্যাট নির্বাচন করুন:</h4>
                <div class="format-options">
                    <div class="format-option" data-format="markdown">
                        <div class="format-icon">
                            <i class="fab fa-markdown"></i>
                        </div>
                        <div class="format-details">
                            <h5>Markdown (.md)</h5>
                            <p>টেক্সট ফরম্যাটিং সহ</p>
                        </div>
                    </div>
                    <div class="format-option" data-format="html">
                        <div class="format-icon">
                            <i class="fab fa-html5"></i>
                        </div>
                        <div class="format-details">
                            <h5>HTML (.html)</h5>
                            <p>ওয়েব পেজ হিসেবে</p>
                        </div>
                    </div>
                    <div class="format-option" data-format="txt">
                        <div class="format-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="format-details">
                            <h5>Plain Text (.txt)</h5>
                            <p>সাধারণ টেক্সট ফাইল</p>
                        </div>
                    </div>
                    <div class="format-option" data-format="pdf">
                        <div class="format-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="format-details">
                            <h5>PDF (.pdf)</h5>
                            <p>পোর্টেবল ডকুমেন্ট</p>
                        </div>
                    </div>
                </div>

                <div class="export-options">
                    <h4>এক্সপোর্ট অপশন:</h4>
                    <label class="checkbox-label">
                        <input type="checkbox" id="includeAttachments" checked>
                        <span class="checkmark"></span>
                        সংযুক্ত ফাইল অন্তর্ভুক্ত করুন
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="includeMetadata" checked>
                        <span class="checkmark"></span>
                        মেটাডেটা অন্তর্ভুক্ত করুন (তারিখ, ট্যাগ ইত্যাদি)
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="preserveFormatting" checked>
                        <span class="checkmark"></span>
                        ফরম্যাটিং বজায় রাখুন
                    </label>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeExportModal()">
                <i class="fas fa-times"></i>
                বাতিল
            </button>
            <button class="btn btn-primary" id="exportNoteFileBtn">
                <i class="fas fa-download"></i>
                এক্সপোর্ট করুন
            </button>
        </div>
    </div>
</div>

<!-- Password Unlock Modal -->
<div class="modal password-modal" id="passwordModal">
    <div class="modal-overlay"></div>
    <div class="modal-content password-content">
        <div class="modal-header">
            <h3><i class="fas fa-lock"></i> নোট আনলক করুন</h3>
        </div>
        <div class="modal-body password-body">
            <div class="password-unlock-section">
                <div class="lock-icon">
                    <i class="fas fa-lock"></i>
                </div>
                <h4 id="lockedNoteTitle">এই নোটটি পাসওয়ার্ড দিয়ে সুরক্ষিত</h4>
                <p>নোটটি দেখতে সঠিক পাসওয়ার্ড লিখুন:</p>
                <div class="unlock-password-field">
                    <input type="password" id="unlockPassword" class="unlock-password-input" placeholder="পাসওয়ার্ড লিখুন...">
                    <button type="button" class="btn btn-icon toggle-unlock-password" id="toggleUnlockPasswordBtn" title="পাসওয়ার্ড দেখান/লুকান" onclick="togglePasswordVisibility('unlockPassword', 'toggleUnlockPasswordBtn')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="password-error" id="passwordError" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>ভুল পাসওয়ার্ড! আবার চেষ্টা করুন।</span>
                </div>
                <div class="password-attempts" id="passwordAttempts" style="display: none;">
                    <span>অবশিষ্ট চেষ্টা: <span id="attemptsLeft">3</span></span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closePasswordModal()">
                <i class="fas fa-times"></i>
                বাতিল
            </button>
            <button class="btn btn-primary" id="unlockNoteBtn" onclick="attemptUnlockNote()">
                <i class="fas fa-unlock"></i>
                আনলক করুন
            </button>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal help-modal" id="helpModal">
    <div class="modal-overlay" onclick="closeHelpModal()"></div>
    <div class="modal-content help-content">

        <div class="modal-header">
            <h3>সাহায্য ও কীবোর্ড শর্টকাট</h3>
            <div class="modal-actions">
                <button class="btn btn-icon" id="helpFullscreenBtn" title="ফুল স্ক্রিন">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="btn btn-icon" title="সাইজ রিসেট করুন" onclick="resetModalSize('helpModal')">
                    <i class="fas fa-compress-arrows-alt"></i>
                </button>
                <button class="btn btn-icon modal-close" onclick="closeHelpModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="modal-body help-body">
            <!-- Keyboard Shortcuts Section -->
            <div class="help-section">
                <h4><i class="fas fa-keyboard"></i> কীবোর্ড শর্টকাট</h4>
                <div class="shortcuts-grid">
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>S</kbd>
                        <span>নোট সংরক্ষণ করুন</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>D</kbd>
                        <span>বর্তমান নোট ডুপ্লিকেট করুন</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl</kbd> + <kbd>F</kbd>
                        <span>নোট খুঁজুন</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Esc</kbd>
                        <span>মোডাল বন্ধ করুন</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Alt</kbd> + <kbd>D</kbd>
                        <span>ড্র্যাগ মোড টগল করুন</span>
                    </div>
                </div>
            </div>

            <!-- Note Actions Section -->
            <div class="help-section">
                <h4><i class="fas fa-sticky-note"></i> নোট অ্যাকশন</h4>
                <div class="actions-grid">
                    <div class="action-item">
                        <i class="fas fa-copy" style="color: var(--success-color);"></i>
                        <div class="action-details">
                            <strong>ডুপ্লিকেট</strong>
                            <p>নোটের একটি কপি তৈরি করুন। সব ট্যাব, অ্যাটাচমেন্ট এবং সেটিংস কপি হবে।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-print" style="color: var(--info-color);"></i>
                        <div class="action-details">
                            <strong>প্রিন্ট</strong>
                            <p>নোটটি প্রিন্ট করুন।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-file-pdf" style="color: var(--danger-color);"></i>
                        <div class="action-details">
                            <strong>PDF ডাউনলোড</strong>
                            <p>নোটটি PDF ফরম্যাটে ডাউনলোড করুন।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-edit" style="color: var(--primary-color);"></i>
                        <div class="action-details">
                            <strong>সম্পাদনা</strong>
                            <p>নোটটি সম্পাদনা করুন।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-trash" style="color: var(--danger-color);"></i>
                        <div class="action-details">
                            <strong>ডিলেট</strong>
                            <p>নোটটি স্থায়ীভাবে মুছে ফেলুন।</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Drag and Drop Section -->
            <div class="help-section">
                <h4><i class="fas fa-arrows-alt"></i> ড্র্যাগ অ্যান্ড ড্রপ</h4>
                <div class="actions-grid">
                    <div class="action-item">
                        <i class="fas fa-hand-rock" style="color: var(--warning-color);"></i>
                        <div class="action-details">
                            <strong>ড্র্যাগ মোড চালু করুন</strong>
                            <p>হেডারে ড্র্যাগ বাটনে ক্লিক করুন বা Alt+D চাপুন।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-mouse" style="color: var(--success-color);"></i>
                        <div class="action-details">
                            <strong>নোট ড্র্যাগ করুন</strong>
                            <p>যেকোনো নোট কার্ডে ক্লিক করে ধরে রাখুন এবং টেনে নিয়ে যান।</p>
                        </div>
                    </div>
                    <div class="action-item">
                        <i class="fas fa-save" style="color: var(--primary-color);"></i>
                        <div class="action-details">
                            <strong>স্বয়ংক্রিয় সংরক্ষণ</strong>
                            <p>নোটের ক্রম পরিবর্তন হলে স্বয়ংক্রিয়ভাবে সংরক্ষিত হবে।</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="help-section">
                <h4><i class="fas fa-lightbulb"></i> টিপস ও ট্রিকস</h4>
                <ul class="tips-list">
                    <li>নোট ডুপ্লিকেট করার সময় একটি কনফার্মেশন ডায়ালগ দেখানো হবে।</li>
                    <li>ডুপ্লিকেট নোটের শিরোনামে "(কপি)" যোগ হবে।</li>
                    <li>একাধিকবার ডুপ্লিকেট করলে "(কপি ২)", "(কপি ৩)" ইত্যাদি হবে।</li>
                    <li>ডুপ্লিকেট নোট সবার উপরে দেখানো হবে।</li>
                    <li>নতুন ডুপ্লিকেট নোট ৩ সেকেন্ডের জন্য হাইলাইট হবে।</li>
                    <li>ড্র্যাগ মোডে নোট কার্ডে হোভার করলে সেগুলো উপরে উঠবে।</li>
                    <li>ড্র্যাগ করার সময় নোট কার্ড একটু ঘুরে যাবে।</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for restore -->
<input type="file" id="restoreFileInput" accept=".json" style="display: none;">

<!-- SortableJS for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<!-- Jodit Editor JS -->
<script src="https://unpkg.com/jodit@4.2.27/es2021/jodit.min.js"></script>

<!-- Notification Manager -->
<script src="js/notification-manager.js"></script>

<!-- Electron Audio System -->
<script src="js/electron-audio.js"></script>

<!-- Custom JavaScript -->
<script src="js/script.js"></script>
<script src="js/bookmarks.js"></script>
</body>
</html>